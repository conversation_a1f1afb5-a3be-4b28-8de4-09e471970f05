import { prisma } from '../config/database';
import { redisClient } from '../config/redis';
import { CustomError } from '../middleware/errorHandler';
import { logger } from '../config/logger';
import { TeamRole } from '@prisma/client';
import { 
  createTeamCacheKey, 
  createSuccessResponse, 
  getPaginationParams,
  createPaginationMeta,
  PaginationOptions 
} from '../utils/helpers';

export interface CreateTeamData {
  name: string;
  description?: string;
  creatorId: string;
}

export interface UpdateTeamData {
  name?: string;
  description?: string;
}

export interface AddMemberData {
  userId: string;
  role?: TeamRole;
}

export interface TeamSettingsData {
  workingHoursStart?: string;
  workingHoursEnd?: string;
  timezone?: string;
  maxDailyMeetings?: number;
  preferredMeetingLength?: number;
  bufferTime?: number;
  noMeetingDays?: string[];
}

class TeamService {
  private readonly CACHE_TTL = 3600; // 1 hour

  async createTeam(data: CreateTeamData) {
    try {
      const team = await prisma.team.create({
        data: {
          name: data.name,
          description: data.description,
          members: {
            create: {
              userId: data.creatorId,
              role: TeamRole.LEAD,
            },
          },
          settings: {
            create: {
              workingHoursStart: '09:00',
              workingHoursEnd: '17:00',
              timezone: 'UTC',
              maxDailyMeetings: 4,
              preferredMeetingLength: 30,
              bufferTime: 15,
              noMeetingDays: ['Saturday', 'Sunday'],
            },
          },
        },
        include: {
          members: {
            include: {
              team: {
                select: {
                  id: true,
                  firstName: true,
                  lastName: true,
                  email: true,
                  avatar: true,
                },
              },
            },
          },
          settings: true,
          _count: {
            select: {
              members: true,
              projects: true,
            },
          },
        },
      });

      logger.info(`Team created: ${team.name} by user ${data.creatorId}`);

      return createSuccessResponse(team, 'Team created successfully');
    } catch (error) {
      logger.error('Error creating team:', error);
      throw error;
    }
  }

  async getTeams(options: PaginationOptions & { search?: string; userId?: string }) {
    try {
      const { skip, take, orderBy } = getPaginationParams({ query: options } as any);
      const { search, userId } = options;

      const where: any = {};

      if (search) {
        where.OR = [
          { name: { contains: search, mode: 'insensitive' } },
          { description: { contains: search, mode: 'insensitive' } },
        ];
      }

      if (userId) {
        where.members = {
          some: {
            userId,
          },
        };
      }

      const [teams, total] = await Promise.all([
        prisma.team.findMany({
          where,
          skip,
          take,
          orderBy: orderBy || { createdAt: 'desc' },
          include: {
            _count: {
              select: {
                members: true,
                projects: true,
              },
            },
          },
        }),
        prisma.team.count({ where }),
      ]);

      const meta = createPaginationMeta(
        Math.floor(skip / take) + 1,
        take,
        total
      );

      return createSuccessResponse(teams, 'Teams retrieved successfully', meta);
    } catch (error) {
      logger.error('Error getting teams:', error);
      throw error;
    }
  }

  async getTeamById(teamId: string) {
    try {
      const cacheKey = createTeamCacheKey(teamId, 'details');
      const cachedTeam = await redisClient.cacheGet(cacheKey);

      if (cachedTeam) {
        return createSuccessResponse(cachedTeam, 'Team retrieved from cache');
      }

      const team = await prisma.team.findUnique({
        where: { id: teamId },
        include: {
          members: {
            include: {
              team: {
                select: {
                  id: true,
                  firstName: true,
                  lastName: true,
                  email: true,
                  avatar: true,
                  skills: true,
                  timezone: true,
                },
              },
            },
            orderBy: {
              joinedAt: 'asc',
            },
          },
          settings: true,
          projects: {
            select: {
              id: true,
              name: true,
              status: true,
              priority: true,
              startDate: true,
              endDate: true,
              _count: {
                select: {
                  tasks: true,
                },
              },
            },
            orderBy: {
              createdAt: 'desc',
            },
          },
          _count: {
            select: {
              members: true,
              projects: true,
            },
          },
        },
      });

      if (!team) {
        throw new CustomError('Team not found', 404);
      }

      // Cache the team data
      await redisClient.cacheSet(cacheKey, team, this.CACHE_TTL);

      return createSuccessResponse(team, 'Team retrieved successfully');
    } catch (error) {
      logger.error('Error getting team by ID:', error);
      throw error;
    }
  }

  async updateTeam(teamId: string, data: UpdateTeamData) {
    try {
      const team = await prisma.team.update({
        where: { id: teamId },
        data,
        include: {
          _count: {
            select: {
              members: true,
              projects: true,
            },
          },
        },
      });

      // Invalidate cache
      const cacheKey = createTeamCacheKey(teamId, 'details');
      await redisClient.del(cacheKey);

      logger.info(`Team updated: ${team.name}`);

      return createSuccessResponse(team, 'Team updated successfully');
    } catch (error) {
      logger.error('Error updating team:', error);
      throw error;
    }
  }

  async deleteTeam(teamId: string) {
    try {
      const team = await prisma.team.findUnique({
        where: { id: teamId },
        include: {
          projects: true,
        },
      });

      if (!team) {
        throw new CustomError('Team not found', 404);
      }

      // Check if team has active projects
      const activeProjects = team.projects.filter(
        project => project.status === 'ACTIVE' || project.status === 'PLANNING'
      );

      if (activeProjects.length > 0) {
        throw new CustomError(
          'Cannot delete team with active projects. Please complete or transfer projects first.',
          400
        );
      }

      await prisma.team.delete({
        where: { id: teamId },
      });

      // Invalidate cache
      const cacheKey = createTeamCacheKey(teamId, 'details');
      await redisClient.del(cacheKey);

      logger.info(`Team deleted: ${team.name}`);

      return createSuccessResponse(null, 'Team deleted successfully');
    } catch (error) {
      logger.error('Error deleting team:', error);
      throw error;
    }
  }

  async addMember(teamId: string, data: AddMemberData) {
    try {
      // Check if user is already a member
      const existingMember = await prisma.teamMember.findUnique({
        where: {
          teamId_userId: {
            teamId,
            userId: data.userId,
          },
        },
      });

      if (existingMember) {
        throw new CustomError('User is already a member of this team', 409);
      }

      // Check if user exists
      const user = await prisma.user.findUnique({
        where: { id: data.userId },
      });

      if (!user) {
        throw new CustomError('User not found', 404);
      }

      const teamMember = await prisma.teamMember.create({
        data: {
          teamId,
          userId: data.userId,
          role: data.role || TeamRole.MEMBER,
        },
        include: {
          team: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
              avatar: true,
              skills: true,
            },
          },
        },
      });

      // Invalidate cache
      const cacheKey = createTeamCacheKey(teamId, 'details');
      await redisClient.del(cacheKey);

      logger.info(`User ${user.email} added to team ${teamId}`);

      return createSuccessResponse(teamMember, 'Member added successfully');
    } catch (error) {
      logger.error('Error adding team member:', error);
      throw error;
    }
  }

  async removeMember(teamId: string, userId: string) {
    try {
      const teamMember = await prisma.teamMember.findUnique({
        where: {
          teamId_userId: {
            teamId,
            userId,
          },
        },
        include: {
          team: {
            select: {
              email: true,
            },
          },
        },
      });

      if (!teamMember) {
        throw new CustomError('User is not a member of this team', 404);
      }

      // Check if this is the last team lead
      if (teamMember.role === TeamRole.LEAD) {
        const leadCount = await prisma.teamMember.count({
          where: {
            teamId,
            role: TeamRole.LEAD,
          },
        });

        if (leadCount === 1) {
          throw new CustomError(
            'Cannot remove the last team lead. Please assign another lead first.',
            400
          );
        }
      }

      await prisma.teamMember.delete({
        where: {
          teamId_userId: {
            teamId,
            userId,
          },
        },
      });

      // Invalidate cache
      const cacheKey = createTeamCacheKey(teamId, 'details');
      await redisClient.del(cacheKey);

      logger.info(`User ${teamMember.team.email} removed from team ${teamId}`);

      return createSuccessResponse(null, 'Member removed successfully');
    } catch (error) {
      logger.error('Error removing team member:', error);
      throw error;
    }
  }

  async updateMemberRole(teamId: string, userId: string, role: TeamRole) {
    try {
      const teamMember = await prisma.teamMember.findUnique({
        where: {
          teamId_userId: {
            teamId,
            userId,
          },
        },
      });

      if (!teamMember) {
        throw new CustomError('User is not a member of this team', 404);
      }

      // If demoting from lead, check if there are other leads
      if (teamMember.role === TeamRole.LEAD && role !== TeamRole.LEAD) {
        const leadCount = await prisma.teamMember.count({
          where: {
            teamId,
            role: TeamRole.LEAD,
          },
        });

        if (leadCount === 1) {
          throw new CustomError(
            'Cannot demote the last team lead. Please assign another lead first.',
            400
          );
        }
      }

      const updatedMember = await prisma.teamMember.update({
        where: {
          teamId_userId: {
            teamId,
            userId,
          },
        },
        data: { role },
        include: {
          team: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
              avatar: true,
            },
          },
        },
      });

      // Invalidate cache
      const cacheKey = createTeamCacheKey(teamId, 'details');
      await redisClient.del(cacheKey);

      logger.info(`Team member role updated: ${userId} -> ${role} in team ${teamId}`);

      return createSuccessResponse(updatedMember, 'Member role updated successfully');
    } catch (error) {
      logger.error('Error updating member role:', error);
      throw error;
    }
  }

  async updateTeamSettings(teamId: string, data: TeamSettingsData) {
    try {
      const settings = await prisma.teamSettings.upsert({
        where: { teamId },
        update: data,
        create: {
          teamId,
          ...data,
        },
      });

      // Invalidate cache
      const cacheKey = createTeamCacheKey(teamId, 'details');
      await redisClient.del(cacheKey);

      logger.info(`Team settings updated for team ${teamId}`);

      return createSuccessResponse(settings, 'Team settings updated successfully');
    } catch (error) {
      logger.error('Error updating team settings:', error);
      throw error;
    }
  }

  async checkMembership(teamId: string, userId: string): Promise<boolean> {
    try {
      const member = await prisma.teamMember.findUnique({
        where: {
          teamId_userId: {
            teamId,
            userId,
          },
        },
      });

      return !!member;
    } catch (error) {
      logger.error('Error checking team membership:', error);
      return false;
    }
  }
}

export const teamService = new TeamService();
