import { Router, Request, Response } from 'express';
import { database } from '../config/database';
import { redisClient } from '../config/redis';
import { asyncHandler } from '../middleware/errorHandler';
import { aiService } from '../services/aiService';

const router = Router();

/**
 * @swagger
 * /api/health:
 *   get:
 *     summary: Basic health check
 *     description: Returns basic API health status
 *     tags: [Health]
 *     responses:
 *       200:
 *         description: API is healthy
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: CollabFlow API is running
 *                 timestamp:
 *                   type: string
 *                   format: date-time
 *                 version:
 *                   type: string
 *                   example: 1.0.0
 */
router.get('/', asyncHandler(async (req: Request, res: Response) => {
  res.json({
    success: true,
    message: 'CollabFlow API is running',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
  });
}));

/**
 * @swagger
 * /api/health/detailed:
 *   get:
 *     summary: Detailed health check
 *     description: Returns detailed health status of all system components
 *     tags: [Health]
 *     responses:
 *       200:
 *         description: System health status
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/HealthCheck'
 *       503:
 *         description: Service unavailable
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get('/detailed', asyncHandler(async (req: Request, res: Response) => {
  const healthChecks = {
    api: true,
    database: false,
    redis: false,
    ai: null as any,
    timestamp: new Date().toISOString(),
  };

  // Check database connection
  try {
    healthChecks.database = await database.healthCheck();
  } catch (error) {
    healthChecks.database = false;
  }

  // Check Redis connection
  try {
    healthChecks.redis = await redisClient.healthCheck();
  } catch (error) {
    healthChecks.redis = false;
  }

  // Check AI service
  try {
    healthChecks.ai = await aiService.healthCheck();
  } catch (error) {
    healthChecks.ai = { error: 'AI service unavailable' };
  }

  const allHealthy = healthChecks.database && healthChecks.redis;

  res.status(allHealthy ? 200 : 503).json({
    success: allHealthy,
    health: healthChecks,
  });
}));

/**
 * @swagger
 * /api/health/ai:
 *   get:
 *     summary: AI service health check
 *     description: Returns health status of AI providers (Ollama, OpenAI, etc.)
 *     tags: [Health, AI]
 *     responses:
 *       200:
 *         description: AI service health status
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 ai:
 *                   type: object
 *                   properties:
 *                     currentProvider:
 *                       type: string
 *                       example: ollama
 *                     providers:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           name:
 *                             type: string
 *                           available:
 *                             type: boolean
 *                           healthy:
 *                             type: boolean
 *                 timestamp:
 *                   type: string
 *                   format: date-time
 *       503:
 *         description: AI service unavailable
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get('/ai', asyncHandler(async (req: Request, res: Response) => {
  try {
    const aiHealth = await aiService.healthCheck();
    res.json({
      success: true,
      ai: aiHealth,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    res.status(503).json({
      success: false,
      error: 'AI service health check failed',
      timestamp: new Date().toISOString(),
    });
  }
}));

export default router;
