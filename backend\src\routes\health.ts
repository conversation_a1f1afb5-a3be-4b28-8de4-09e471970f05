import { Router, Request, Response } from 'express';
import { database } from '../config/database';
import { redisClient } from '../config/redis';
import { asyncHandler } from '../middleware/errorHandler';
import { aiService } from '../services/aiService';

const router = Router();

// Basic health check
router.get('/', asyncHandler(async (req: Request, res: Response) => {
  res.json({
    success: true,
    message: 'CollabFlow API is running',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
  });
}));

// Detailed health check
router.get('/detailed', asyncHandler(async (req: Request, res: Response) => {
  const healthChecks = {
    api: true,
    database: false,
    redis: false,
    ai: null as any,
    timestamp: new Date().toISOString(),
  };

  // Check database connection
  try {
    healthChecks.database = await database.healthCheck();
  } catch (error) {
    healthChecks.database = false;
  }

  // Check Redis connection
  try {
    healthChecks.redis = await redisClient.healthCheck();
  } catch (error) {
    healthChecks.redis = false;
  }

  // Check AI service
  try {
    healthChecks.ai = await aiService.healthCheck();
  } catch (error) {
    healthChecks.ai = { error: 'AI service unavailable' };
  }

  const allHealthy = healthChecks.database && healthChecks.redis;

  res.status(allHealthy ? 200 : 503).json({
    success: allHealthy,
    health: healthChecks,
  });
}));

// AI-specific health check
router.get('/ai', asyncHandler(async (req: Request, res: Response) => {
  try {
    const aiHealth = await aiService.healthCheck();
    res.json({
      success: true,
      ai: aiHealth,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    res.status(503).json({
      success: false,
      error: 'AI service health check failed',
      timestamp: new Date().toISOString(),
    });
  }
}));

export default router;
