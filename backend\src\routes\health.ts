import { Router, Request, Response } from 'express';
import { database } from '../config/database';
import { redisClient } from '../config/redis';
import { asyncHandler } from '../middleware/errorHandler';

const router = Router();

// Basic health check
router.get('/', asyncHandler(async (req: Request, res: Response) => {
  res.json({
    success: true,
    message: 'CollabFlow API is running',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
  });
}));

// Detailed health check
router.get('/detailed', asyncHandler(async (req: Request, res: Response) => {
  const healthChecks = {
    api: true,
    database: false,
    redis: false,
    timestamp: new Date().toISOString(),
  };

  // Check database connection
  try {
    healthChecks.database = await database.healthCheck();
  } catch (error) {
    healthChecks.database = false;
  }

  // Check Redis connection
  try {
    healthChecks.redis = await redisClient.healthCheck();
  } catch (error) {
    healthChecks.redis = false;
  }

  const allHealthy = Object.values(healthChecks).every(
    (status) => status === true || typeof status === 'string'
  );

  res.status(allHealthy ? 200 : 503).json({
    success: allHealthy,
    health: healthChecks,
  });
}));

export default router;
