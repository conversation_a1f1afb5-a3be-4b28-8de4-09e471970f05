import { logger } from '../../config/logger';
import { AIProvider, AIProviderConfig, AIProviderFactory } from './types';
import { OllamaProvider } from './providers/OllamaProvider';
import { OpenAIProvider } from './providers/OpenAIProvider';
import { FallbackProvider } from './providers/FallbackProvider';

export class DefaultAIProviderFactory implements AIProviderFactory {
  createProvider(config: AIProviderConfig): AIProvider {
    switch (config.provider) {
      case 'ollama':
        return new OllamaProvider(config);
      case 'openai':
        return new OpenAIProvider(config);
      case 'fallback':
        return new FallbackProvider();
      default:
        throw new Error(`Unsupported AI provider: ${config.provider}`);
    }
  }

  getSupportedProviders(): string[] {
    return ['ollama', 'openai', 'fallback'];
  }
}

export class AIProviderManager {
  private providers: AIProvider[] = [];
  private currentProvider: AIProvider | null = null;
  private factory: AIProviderFactory;
  private fallbackProvider: FallbackProvider;

  constructor(factory?: AIProviderFactory) {
    this.factory = factory || new DefaultAIProviderFactory();
    this.fallbackProvider = new FallbackProvider();
  }

  async initialize(): Promise<void> {
    logger.info('Initializing AI Provider Manager...');

    // Get configuration from environment
    const configs = this.getProviderConfigs();
    
    // Create providers
    for (const config of configs) {
      try {
        const provider = this.factory.createProvider(config);
        this.providers.push(provider);
        logger.info(`Created ${config.provider} provider`);
      } catch (error) {
        logger.error(`Failed to create ${config.provider} provider:`, error);
      }
    }

    // Find the first available provider
    await this.selectBestProvider();
  }

  async selectBestProvider(): Promise<void> {
    logger.info('Selecting best available AI provider...');

    for (const provider of this.providers) {
      try {
        const isAvailable = await provider.isAvailable();
        if (isAvailable) {
          const isHealthy = await provider.healthCheck();
          if (isHealthy) {
            this.currentProvider = provider;
            logger.info(`Selected ${provider.getProviderName()} as current AI provider`);
            return;
          } else {
            logger.warn(`Provider ${provider.getProviderName()} is available but not healthy`);
          }
        } else {
          logger.warn(`Provider ${provider.getProviderName()} is not available`);
        }
      } catch (error) {
        logger.error(`Error checking provider ${provider.getProviderName()}:`, error);
      }
    }

    // If no provider is available, use fallback
    this.currentProvider = this.fallbackProvider;
    logger.warn('No AI providers available, using fallback provider');
  }

  getCurrentProvider(): AIProvider {
    return this.currentProvider || this.fallbackProvider;
  }

  async executeWithFallback<T>(
    operation: (provider: AIProvider) => Promise<T>,
    operationName: string
  ): Promise<T> {
    const provider = this.getCurrentProvider();
    
    try {
      logger.debug(`Executing ${operationName} with ${provider.getProviderName()}`);
      return await operation(provider);
    } catch (error) {
      logger.error(`${operationName} failed with ${provider.getProviderName()}:`, error);
      
      // If current provider failed and it's not the fallback, try fallback
      if (provider !== this.fallbackProvider) {
        logger.info(`Falling back to fallback provider for ${operationName}`);
        try {
          return await operation(this.fallbackProvider);
        } catch (fallbackError) {
          logger.error(`Fallback also failed for ${operationName}:`, fallbackError);
          throw fallbackError;
        }
      } else {
        throw error;
      }
    }
  }

  async healthCheck(): Promise<{
    currentProvider: string;
    providers: Array<{
      name: string;
      available: boolean;
      healthy: boolean;
      error?: string;
    }>;
  }> {
    const providerStatuses = [];

    for (const provider of this.providers) {
      try {
        const available = await provider.isAvailable();
        const healthy = available ? await provider.healthCheck() : false;
        
        providerStatuses.push({
          name: provider.getProviderName(),
          available,
          healthy,
        });
      } catch (error) {
        providerStatuses.push({
          name: provider.getProviderName(),
          available: false,
          healthy: false,
          error: error instanceof Error ? error.message : 'Unknown error',
        });
      }
    }

    // Always include fallback
    providerStatuses.push({
      name: this.fallbackProvider.getProviderName(),
      available: true,
      healthy: true,
    });

    return {
      currentProvider: this.getCurrentProvider().getProviderName(),
      providers: providerStatuses,
    };
  }

  private getProviderConfigs(): AIProviderConfig[] {
    const configs: AIProviderConfig[] = [];

    // Get primary provider from environment
    const primaryProvider = process.env.AI_PROVIDER || 'ollama';
    
    // Ollama configuration
    if (primaryProvider === 'ollama' || process.env.OLLAMA_BASE_URL) {
      configs.push({
        provider: 'ollama',
        baseUrl: process.env.OLLAMA_BASE_URL || 'http://localhost:11434',
        model: process.env.OLLAMA_MODEL || 'llama2:7b',
        timeout: parseInt(process.env.AI_TIMEOUT || '30000'),
        maxRetries: parseInt(process.env.AI_MAX_RETRIES || '3'),
      });
    }

    // OpenAI configuration (for backward compatibility)
    if (primaryProvider === 'openai' || process.env.OPENAI_API_KEY) {
      configs.push({
        provider: 'openai',
        apiKey: process.env.OPENAI_API_KEY,
        model: process.env.OPENAI_MODEL || 'gpt-3.5-turbo',
        timeout: parseInt(process.env.AI_TIMEOUT || '30000'),
        maxRetries: parseInt(process.env.AI_MAX_RETRIES || '3'),
      });
    }

    // If no specific provider is configured, default to Ollama
    if (configs.length === 0) {
      configs.push({
        provider: 'ollama',
        baseUrl: 'http://localhost:11434',
        model: 'llama2:7b',
        timeout: 30000,
        maxRetries: 3,
      });
    }

    return configs;
  }

  // Convenience methods that delegate to current provider with fallback
  async analyzeMeetingNecessity(
    title: string,
    description: string,
    participants: string[],
    duration: number,
    context?: string
  ) {
    return this.executeWithFallback(
      (provider) => provider.analyzeMeetingNecessity(title, description, participants, duration, context),
      'analyzeMeetingNecessity'
    );
  }

  async analyzeTaskPriority(
    title: string,
    description: string,
    dueDate?: Date,
    dependencies?: string[],
    projectContext?: string
  ) {
    return this.executeWithFallback(
      (provider) => provider.analyzeTaskPriority(title, description, dueDate, dependencies, projectContext),
      'analyzeTaskPriority'
    );
  }

  async analyzeTeamCapacity(
    teamMembers: Array<{
      id: string;
      name: string;
      skills: string[];
      currentTasks: number;
      availability: number;
    }>,
    upcomingTasks: Array<{
      title: string;
      estimatedHours: number;
      requiredSkills: string[];
    }>
  ) {
    return this.executeWithFallback(
      (provider) => provider.analyzeTeamCapacity(teamMembers, upcomingTasks),
      'analyzeTeamCapacity'
    );
  }

  async detectConflicts(
    teamId: string,
    startDate: Date,
    endDate: Date,
    meetings: any[],
    tasks: any[]
  ) {
    return this.executeWithFallback(
      (provider) => provider.detectConflicts(teamId, startDate, endDate, meetings, tasks),
      'detectConflicts'
    );
  }
}

// Singleton instance
export const aiProviderManager = new AIProviderManager();
