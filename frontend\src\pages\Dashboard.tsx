import React from 'react';
import {
  <PERSON>,
  Typography,
  Grid,
  Card,
  CardContent,
  CardActions,
  Button,
  Chip,
  LinearProgress,
} from '@mui/material';
import {
  Groups as TeamsIcon,
  Work as ProjectsIcon,
  Assignment as TasksIcon,
  Event as MeetingsIcon,
} from '@mui/icons-material';

const Dashboard: React.FC = () => {
  // Mock data - will be replaced with real data from API
  const stats = {
    teams: 3,
    projects: 8,
    tasks: 24,
    meetings: 5,
  };

  const recentTasks = [
    { id: 1, title: 'Set up authentication system', status: 'In Progress', progress: 75 },
    { id: 2, title: 'Design user interface mockups', status: 'Todo', progress: 0 },
    { id: 3, title: 'Implement real-time notifications', status: 'Todo', progress: 0 },
  ];

  const upcomingMeetings = [
    { id: 1, title: 'Sprint Planning Meeting', time: 'Today, 2:00 PM' },
    { id: 2, title: 'Team Standup', time: 'Tomorrow, 9:00 AM' },
  ];

  return (
    <Box>
      <Typography variant="h4" component="h1" gutterBottom>
        Dashboard
      </Typography>
      
      <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
        Welcome back! Here's what's happening with your teams.
      </Typography>

      {/* Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="text.secondary" gutterBottom>
                    Teams
                  </Typography>
                  <Typography variant="h4">
                    {stats.teams}
                  </Typography>
                </Box>
                <TeamsIcon color="primary" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="text.secondary" gutterBottom>
                    Projects
                  </Typography>
                  <Typography variant="h4">
                    {stats.projects}
                  </Typography>
                </Box>
                <ProjectsIcon color="primary" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="text.secondary" gutterBottom>
                    Tasks
                  </Typography>
                  <Typography variant="h4">
                    {stats.tasks}
                  </Typography>
                </Box>
                <TasksIcon color="primary" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="text.secondary" gutterBottom>
                    Meetings
                  </Typography>
                  <Typography variant="h4">
                    {stats.meetings}
                  </Typography>
                </Box>
                <MeetingsIcon color="primary" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Grid container spacing={3}>
        {/* Recent Tasks */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Recent Tasks
              </Typography>
              {recentTasks.map((task) => (
                <Box key={task.id} sx={{ mb: 2 }}>
                  <Box display="flex" justifyContent="space-between" alignItems="center" sx={{ mb: 1 }}>
                    <Typography variant="body2">
                      {task.title}
                    </Typography>
                    <Chip 
                      label={task.status} 
                      size="small" 
                      color={task.status === 'In Progress' ? 'primary' : 'default'}
                    />
                  </Box>
                  <LinearProgress 
                    variant="determinate" 
                    value={task.progress} 
                    sx={{ height: 6, borderRadius: 3 }}
                  />
                </Box>
              ))}
            </CardContent>
            <CardActions>
              <Button size="small">View All Tasks</Button>
            </CardActions>
          </Card>
        </Grid>

        {/* Upcoming Meetings */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Upcoming Meetings
              </Typography>
              {upcomingMeetings.map((meeting) => (
                <Box key={meeting.id} sx={{ mb: 2 }}>
                  <Typography variant="body2" fontWeight="medium">
                    {meeting.title}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {meeting.time}
                  </Typography>
                </Box>
              ))}
            </CardContent>
            <CardActions>
              <Button size="small">View All Meetings</Button>
            </CardActions>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default Dashboard;
