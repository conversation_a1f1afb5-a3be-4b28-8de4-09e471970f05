// AI Service Types and Interfaces

export interface MeetingNecessityAnalysis {
  score: number; // 0-100
  reasoning: string;
  alternatives: string[];
  recommendations: string[];
}

export interface TaskPriorityAnalysis {
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  reasoning: string;
  estimatedHours: number;
  dependencies: string[];
}

export interface TeamCapacityAnalysis {
  overallUtilization: number; // 0-100
  bottlenecks: Array<{
    userId: string;
    utilization: number;
    skills: string[];
  }>;
  recommendations: string[];
}

export interface ConflictResolution {
  conflicts: Array<{
    type: 'schedule' | 'resource' | 'priority';
    description: string;
    severity: 'low' | 'medium' | 'high';
  }>;
  resolutions: Array<{
    conflictType: string;
    solution: string;
    impact: string;
  }>;
}

export interface AIProviderConfig {
  provider: 'ollama' | 'openai' | 'fallback';
  baseUrl?: string;
  apiKey?: string;
  model?: string;
  timeout?: number;
  maxRetries?: number;
}

export interface AIProviderResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  provider: string;
  fallbackUsed?: boolean;
}

export interface ChatMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

export interface ChatCompletionRequest {
  messages: ChatMessage[];
  model?: string;
  temperature?: number;
  maxTokens?: number;
  stream?: boolean;
}

export interface ChatCompletionResponse {
  choices: Array<{
    message: {
      content: string;
      role: string;
    };
    finishReason?: string;
  }>;
  usage?: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
}

// Abstract AI Provider Interface
export abstract class AIProvider {
  protected config: AIProviderConfig;
  protected name: string;

  constructor(config: AIProviderConfig, name: string) {
    this.config = config;
    this.name = name;
  }

  abstract isAvailable(): Promise<boolean>;
  abstract healthCheck(): Promise<boolean>;
  abstract chatCompletion(request: ChatCompletionRequest): Promise<ChatCompletionResponse>;

  // High-level AI analysis methods
  abstract analyzeMeetingNecessity(
    title: string,
    description: string,
    participants: string[],
    duration: number,
    context?: string
  ): Promise<MeetingNecessityAnalysis>;

  abstract analyzeTaskPriority(
    title: string,
    description: string,
    dueDate?: Date,
    dependencies?: string[],
    projectContext?: string
  ): Promise<TaskPriorityAnalysis>;

  abstract analyzeTeamCapacity(
    teamMembers: Array<{
      id: string;
      name: string;
      skills: string[];
      currentTasks: number;
      availability: number;
    }>,
    upcomingTasks: Array<{
      title: string;
      estimatedHours: number;
      requiredSkills: string[];
    }>
  ): Promise<TeamCapacityAnalysis>;

  abstract detectConflicts(
    teamId: string,
    startDate: Date,
    endDate: Date,
    meetings: any[],
    tasks: any[]
  ): Promise<ConflictResolution>;

  // Utility methods
  getProviderName(): string {
    return this.name;
  }

  getConfig(): AIProviderConfig {
    return { ...this.config };
  }
}

// Provider Factory
export interface AIProviderFactory {
  createProvider(config: AIProviderConfig): AIProvider;
  getSupportedProviders(): string[];
}
