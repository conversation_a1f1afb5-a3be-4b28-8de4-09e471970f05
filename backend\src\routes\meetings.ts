import { Router, Request, Response } from 'express';
import { validationResult } from 'express-validator';
import { asyncHand<PERSON>, CustomError } from '../middleware/errorHandler';
import { AuthenticatedRequest } from '../middleware/auth';
import { prisma } from '../config/database';
import { aiService } from '../services/aiService';
import { schedulingService } from '../services/schedulingService';
import { websocketService } from '../services/websocketService';
import { notificationService } from '../services/notificationService';
import {
  commonValidations,
  meetingValidations,
  paginationValidation
} from '../utils/validation';
import {
  createSuccessResponse,
  getPaginationParams,
  createPaginationMeta
} from '../utils/helpers';

const router = Router();

// Helper function to handle validation errors
const handleValidationErrors = (req: Request, res: Response, next: any) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errors.array(),
    });
  }
  next();
};

// Get all meetings
router.get('/',
  paginationValidation,
  handleValidationErrors,
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const { page, limit, sortBy, sortOrder, status, date, organizerId } = req.query;
    const userId = req.user!.id;

    const { skip, take, orderBy } = getPaginationParams({ query: req.query } as any);

    const where: any = {
      participants: {
        some: {
          userId,
        },
      },
    };

    // Apply filters
    if (status) where.status = status;
    if (organizerId) where.organizerId = organizerId;
    if (date) {
      const filterDate = new Date(date as string);
      const nextDay = new Date(filterDate);
      nextDay.setDate(nextDay.getDate() + 1);

      where.startTime = {
        gte: filterDate,
        lt: nextDay,
      };
    }

    const [meetings, total] = await Promise.all([
      prisma.meeting.findMany({
        where,
        skip,
        take,
        orderBy: orderBy || { startTime: 'asc' },
        include: {
          organizer: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              avatar: true,
            },
          },
          participants: {
            include: {
              user: {
                select: {
                  id: true,
                  firstName: true,
                  lastName: true,
                  avatar: true,
                },
              },
            },
          },
          agenda: {
            orderBy: { order: 'asc' },
          },
        },
      }),
      prisma.meeting.count({ where }),
    ]);

    const meta = createPaginationMeta(
      Math.floor(skip / take) + 1,
      take,
      total
    );

    res.json(createSuccessResponse(meetings, 'Meetings retrieved successfully', meta));
  })
);

// Create meeting with AI-powered necessity analysis
router.post('/',
  meetingValidations.create,
  handleValidationErrors,
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const {
      title,
      description,
      participants,
      startTime,
      endTime,
      location,
      meetingUrl,
      necessityScore,
      agenda,
    } = req.body;

    const organizerId = req.user!.id;
    const duration = Math.ceil((new Date(endTime).getTime() - new Date(startTime).getTime()) / (1000 * 60));

    // Use AI to analyze meeting necessity if not provided
    let finalNecessityScore = necessityScore;
    let aiRecommendations: string[] = [];

    if (!necessityScore) {
      try {
        const analysis = await aiService.analyzeMeetingNecessity(
          title,
          description || '',
          participants,
          duration
        );

        finalNecessityScore = analysis.score;
        aiRecommendations = analysis.recommendations;
      } catch (error) {
        // Fallback to default score if AI analysis fails
        finalNecessityScore = 50;
      }
    }

    // Check for scheduling conflicts
    const conflicts = await schedulingService.findOptimalMeetingTimes({
      duration,
      participants: [...participants, organizerId],
      timezone: 'UTC', // Should come from user preferences
    }, 1);

    const meeting = await prisma.meeting.create({
      data: {
        title,
        description,
        organizerId,
        startTime: new Date(startTime),
        endTime: new Date(endTime),
        location,
        meetingUrl,
        necessityScore: finalNecessityScore,
        participants: {
          create: [
            ...participants.map((userId: string) => ({ userId })),
            { userId: organizerId }, // Include organizer as participant
          ],
        },
        agenda: agenda ? {
          create: agenda.map((item: any, index: number) => ({
            title: item.title,
            description: item.description,
            duration: item.duration,
            presenterId: item.presenterId,
            order: index + 1,
          })),
        } : undefined,
      },
      include: {
        organizer: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            avatar: true,
          },
        },
        participants: {
          include: {
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                avatar: true,
              },
            },
          },
        },
        agenda: {
          orderBy: { order: 'asc' },
        },
      },
    });

    const response = {
      meeting,
      aiAnalysis: {
        necessityScore: finalNecessityScore,
        recommendations: aiRecommendations,
        conflicts: conflicts.timeSlots.length > 0 ? [] : ['Potential scheduling conflicts detected'],
      },
    };

    // Emit real-time updates to all participants
    const allParticipants = [...participants, organizerId];
    for (const participantId of allParticipants) {
      websocketService.emitToUser(participantId, 'meeting:created', {
        meeting,
        createdBy: req.user,
      });
    }

    // Schedule meeting reminder notifications (15 minutes before)
    const reminderTime = new Date(meeting.startTime.getTime() - 15 * 60 * 1000);
    if (reminderTime > new Date()) {
      // In a real implementation, you'd use a job scheduler like Bull or Agenda
      setTimeout(async () => {
        await notificationService.notifyMeetingReminder(meeting.id, 15);
      }, reminderTime.getTime() - Date.now());
    }

    res.status(201).json(createSuccessResponse(response, 'Meeting created successfully'));
  })
);

// Get meeting by ID
router.get('/:id',
  commonValidations.id,
  handleValidationErrors,
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const { id } = req.params;
    const userId = req.user!.id;

    const meeting = await prisma.meeting.findFirst({
      where: {
        id,
        participants: {
          some: { userId },
        },
      },
      include: {
        organizer: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            avatar: true,
          },
        },
        participants: {
          include: {
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                avatar: true,
                skills: true,
              },
            },
          },
        },
        agenda: {
          orderBy: { order: 'asc' },
        },
      },
    });

    if (!meeting) {
      throw new CustomError('Meeting not found', 404);
    }

    res.json(createSuccessResponse(meeting, 'Meeting retrieved successfully'));
  })
);

// Update meeting
router.put('/:id',
  commonValidations.id,
  meetingValidations.update,
  handleValidationErrors,
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const { id } = req.params;
    const userId = req.user!.id;
    const updateData = req.body;

    // Verify meeting access (organizer or admin)
    const existingMeeting = await prisma.meeting.findFirst({
      where: {
        id,
        OR: [
          { organizerId: userId },
          { participants: { some: { userId } } },
        ],
      },
    });

    if (!existingMeeting) {
      throw new CustomError('Meeting not found', 404);
    }

    // Only organizer or admin can update meeting details
    if (existingMeeting.organizerId !== userId && req.user!.role !== 'ADMIN') {
      throw new CustomError('Only the organizer can update meeting details', 403);
    }

    const meeting = await prisma.meeting.update({
      where: { id },
      data: {
        ...updateData,
        startTime: updateData.startTime ? new Date(updateData.startTime) : undefined,
        endTime: updateData.endTime ? new Date(updateData.endTime) : undefined,
      },
      include: {
        organizer: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            avatar: true,
          },
        },
        participants: {
          include: {
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                avatar: true,
              },
            },
          },
        },
        agenda: {
          orderBy: { order: 'asc' },
        },
      },
    });

    res.json(createSuccessResponse(meeting, 'Meeting updated successfully'));
  })
);

// Cancel/Delete meeting
router.delete('/:id',
  commonValidations.id,
  handleValidationErrors,
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const { id } = req.params;
    const userId = req.user!.id;

    // Verify meeting access (organizer or admin)
    const meeting = await prisma.meeting.findFirst({
      where: {
        id,
        organizerId: userId,
      },
    });

    if (!meeting && req.user!.role !== 'ADMIN') {
      throw new CustomError('Meeting not found or insufficient permissions', 404);
    }

    // Delete agenda items first
    await prisma.agendaItem.deleteMany({
      where: { meetingId: id },
    });

    // Delete participants
    await prisma.meetingParticipant.deleteMany({
      where: { meetingId: id },
    });

    // Delete the meeting
    await prisma.meeting.delete({
      where: { id },
    });

    res.json(createSuccessResponse(null, 'Meeting cancelled successfully'));
  })
);

export default router;
