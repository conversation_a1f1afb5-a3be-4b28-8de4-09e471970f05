import { apiClient } from './api';

export interface Notification {
  id: string;
  type: string;
  title: string;
  message: string;
  data?: any;
  read: boolean;
  createdAt: string;
}

export interface NotificationResponse {
  notifications: Notification[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPrevPage: boolean;
  };
  unreadCount: number;
}

export const notificationService = {
  // Get user notifications
  getNotifications: async (page: number = 1, limit: number = 20) => {
    return apiClient.get<NotificationResponse>('/notifications', {
      params: { page, limit },
    });
  },

  // Get unread notification count
  getUnreadCount: async () => {
    return apiClient.get<{ count: number }>('/notifications/unread-count');
  },

  // Mark notification as read
  markAsRead: async (notificationId: string) => {
    return apiClient.patch(`/notifications/${notificationId}/read`);
  },

  // Mark all notifications as read
  markAllAsRead: async () => {
    return apiClient.patch('/notifications/mark-all-read');
  },

  // Delete notification
  deleteNotification: async (notificationId: string) => {
    return apiClient.delete(`/notifications/${notificationId}`);
  },

  // Create test notification (development only)
  createTestNotification: async (data: {
    type?: string;
    title?: string;
    message?: string;
    data?: any;
  }) => {
    return apiClient.post('/notifications/test', data);
  },
};
