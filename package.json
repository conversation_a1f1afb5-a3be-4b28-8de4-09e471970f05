{"name": "collabflow", "version": "1.0.0", "description": "AI-powered team coordination platform - Seamless team orchestration", "main": "index.js", "scripts": {"install:all": "npm install && cd backend && npm install && cd ../frontend && npm install", "dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "cd backend && npm run dev", "dev:frontend": "cd frontend && npm start", "build": "npm run build:backend && npm run build:frontend", "build:backend": "cd backend && npm run build", "build:frontend": "cd frontend && npm run build", "test": "npm run test:backend && npm run test:frontend", "test:backend": "cd backend && npm test", "test:frontend": "cd frontend && npm test", "test:e2e": "cypress run", "lint": "npm run lint:backend && npm run lint:frontend", "lint:backend": "cd backend && npm run lint", "lint:frontend": "cd frontend && npm run lint", "docker:dev": "docker-compose up -d", "docker:prod": "docker-compose -f docker-compose.prod.yml up -d", "deploy:dev": "echo 'Deploying to development...'", "deploy:staging": "echo 'Deploying to staging...'", "deploy:prod": "echo 'Deploying to production...'"}, "keywords": ["team-coordination", "ai-powered", "collaboration", "project-management", "workflow-automation", "meeting-assistant", "capacity-planning"], "author": "HectorTa1989", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/HectorTa1989/CollabFlow.git"}, "bugs": {"url": "https://github.com/HectorTa1989/CollabFlow/issues"}, "homepage": "https://github.com/HectorTa1989/CollabFlow#readme", "devDependencies": {"concurrently": "^8.2.2", "cypress": "^13.6.0"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}