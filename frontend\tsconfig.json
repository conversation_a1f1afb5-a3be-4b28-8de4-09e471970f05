{"compilerOptions": {"target": "es5", "lib": ["dom", "dom.iterable", "es6"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "baseUrl": "src", "paths": {"@/*": ["*"], "@/components/*": ["components/*"], "@/pages/*": ["pages/*"], "@/hooks/*": ["hooks/*"], "@/services/*": ["services/*"], "@/store/*": ["store/*"], "@/utils/*": ["utils/*"], "@/types/*": ["types/*"], "@/assets/*": ["assets/*"]}}, "include": ["src", "../shared/types"]}