import { apiClient } from './api';

export interface MeetingNecessityAnalysis {
  score: number;
  reasoning: string;
  alternatives: string[];
  recommendations: string[];
}

export interface TaskPriorityAnalysis {
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  reasoning: string;
  estimatedHours: number;
  dependencies: string[];
}

export interface TeamCapacityAnalysis {
  overallUtilization: number;
  bottlenecks: Array<{
    userId: string;
    utilization: number;
    skills: string[];
  }>;
  recommendations: string[];
}

export interface ConflictResolution {
  conflicts: Array<{
    type: 'schedule' | 'resource' | 'priority';
    description: string;
    severity: 'low' | 'medium' | 'high';
  }>;
  resolutions: Array<{
    conflictType: string;
    solution: string;
    impact: string;
  }>;
}

export interface TimeSlot {
  start: string;
  end: string;
  score: number;
  conflicts: string[];
}

export interface SchedulingSuggestion {
  timeSlots: TimeSlot[];
  analysis: {
    totalParticipants: number;
    availableParticipants: number;
    conflictingMeetings: number;
    optimalScore: number;
  };
  recommendations: string[];
}

export interface TaskAssignmentSuggestion {
  userId: string;
  name: string;
  skills: string[];
  currentTasks: number;
  skillMatch: number;
  score: number;
  reasoning: string;
}

export const aiService = {
  // Meeting necessity analysis
  analyzeMeetingNecessity: async (data: {
    title: string;
    description?: string;
    participants: string[];
    duration: number;
    context?: string;
  }) => {
    return apiClient.post<{ analysis: MeetingNecessityAnalysis }>(
      '/ai/meetings/analyze-necessity',
      data
    );
  },

  // Task priority analysis
  analyzeTaskPriority: async (data: {
    title: string;
    description?: string;
    dueDate?: string;
    dependencies?: string[];
    projectContext?: string;
  }) => {
    return apiClient.post<{ analysis: TaskPriorityAnalysis }>(
      '/ai/tasks/analyze-priority',
      data
    );
  },

  // Team capacity analysis
  analyzeTeamCapacity: async (teamId: string) => {
    return apiClient.get<{ analysis: TeamCapacityAnalysis }>(
      `/ai/teams/${teamId}/analyze-capacity`
    );
  },

  // Conflict detection
  detectConflicts: async (data: {
    teamId: string;
    startDate: string;
    endDate: string;
  }) => {
    return apiClient.post<{ conflictAnalysis: ConflictResolution }>(
      '/ai/conflicts/detect',
      data
    );
  },

  // Meeting time suggestions
  suggestMeetingTimes: async (data: {
    duration: number;
    participants: string[];
    preferredTimes?: Array<{ start: string; end: string }>;
    timezone?: string;
    bufferTime?: number;
    excludeDates?: string[];
    lookAheadDays?: number;
  }) => {
    return apiClient.post<{ suggestions: SchedulingSuggestion }>(
      '/ai/meetings/suggest-times',
      data
    );
  },

  // Schedule recurring meeting
  scheduleRecurringMeeting: async (data: {
    title: string;
    description?: string;
    participants: string[];
    duration: number;
    recurrence: {
      frequency: 'daily' | 'weekly' | 'monthly';
      interval: number;
      daysOfWeek?: number[];
      endDate?: string;
      occurrences?: number;
    };
    preferredTimes?: Array<{ start: string; end: string }>;
    timezone?: string;
    bufferTime?: number;
  }) => {
    return apiClient.post<{ meetingIds: string[] }>(
      '/ai/meetings/schedule-recurring',
      data
    );
  },

  // Optimize team schedule
  optimizeTeamSchedule: async (
    teamId: string,
    data: {
      startDate: string;
      endDate: string;
    }
  ) => {
    return apiClient.post<{
      optimization: {
        currentEfficiency: number;
        optimizedSchedule: Array<{
          meetingId: string;
          currentTime: { start: string; end: string };
          suggestedTime: { start: string; end: string };
          improvement: number;
        }>;
        totalImprovement: number;
      };
    }>(`/ai/teams/${teamId}/optimize-schedule`, data);
  },

  // Task assignment suggestions
  suggestTaskAssignment: async (data: {
    taskId: string;
    teamId: string;
  }) => {
    return apiClient.post<{ suggestions: TaskAssignmentSuggestion[] }>(
      '/ai/tasks/suggest-assignment',
      data
    );
  },
};
