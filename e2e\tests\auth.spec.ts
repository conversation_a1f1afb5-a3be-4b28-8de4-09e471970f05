import { test, expect } from '@playwright/test';
import { TestHelpers } from '../utils/test-helpers';

test.describe('Authentication Flow', () => {
  let helpers: TestHelpers;

  test.beforeEach(async ({ page }) => {
    helpers = new TestHelpers(page);
  });

  test.describe('User Registration', () => {
    test('should register a new user successfully', async ({ page }) => {
      const userData = {
        email: helpers.generateRandomEmail(),
        password: 'TestPassword123!',
        firstName: 'John',
        lastName: 'Doe',
        skills: ['JavaScript', 'React'],
      };

      await helpers.register(userData);

      // Should be redirected to dashboard after successful registration
      await expect(page).toHaveURL('/dashboard');
      await expect(page.locator('[data-testid="user-menu"]')).toBeVisible();
      await expect(page.locator('[data-testid="welcome-message"]')).toContainText('<PERSON>');
    });

    test('should show validation errors for invalid registration data', async ({ page }) => {
      await page.goto('/register');

      // Try to register with invalid email
      await page.fill('[data-testid="email-input"]', 'invalid-email');
      await page.fill('[data-testid="password-input"]', 'weak');
      await page.fill('[data-testid="firstName-input"]', '');
      await page.fill('[data-testid="lastName-input"]', 'Doe');

      await page.click('[data-testid="register-button"]');

      // Should show validation errors
      await expect(page.locator('[data-testid="email-error"]')).toContainText('valid email');
      await expect(page.locator('[data-testid="password-error"]')).toContainText('password');
      await expect(page.locator('[data-testid="firstName-error"]')).toContainText('required');
    });

    test('should prevent registration with existing email', async ({ page }) => {
      const existingEmail = process.env.E2E_TEST_EMAIL || '<EMAIL>';

      await page.goto('/register');
      await page.fill('[data-testid="email-input"]', existingEmail);
      await page.fill('[data-testid="password-input"]', 'TestPassword123!');
      await page.fill('[data-testid="firstName-input"]', 'John');
      await page.fill('[data-testid="lastName-input"]', 'Doe');

      await page.click('[data-testid="register-button"]');

      // Should show error message
      await helpers.waitForToast('already exists');
    });
  });

  test.describe('User Login', () => {
    test('should login with valid credentials', async ({ page }) => {
      await helpers.login();

      // Should be redirected to dashboard
      await expect(page).toHaveURL('/dashboard');
      await expect(page.locator('[data-testid="user-menu"]')).toBeVisible();
    });

    test('should show error for invalid credentials', async ({ page }) => {
      await page.goto('/login');
      await page.fill('[data-testid="email-input"]', '<EMAIL>');
      await page.fill('[data-testid="password-input"]', 'wrongpassword');
      await page.click('[data-testid="login-button"]');

      // Should show error message
      await helpers.waitForToast('Invalid credentials');
      await expect(page).toHaveURL('/login');
    });

    test('should show validation errors for empty fields', async ({ page }) => {
      await page.goto('/login');
      await page.click('[data-testid="login-button"]');

      // Should show validation errors
      await expect(page.locator('[data-testid="email-error"]')).toContainText('required');
      await expect(page.locator('[data-testid="password-error"]')).toContainText('required');
    });

    test('should redirect to login when accessing protected route without authentication', async ({ page }) => {
      await page.goto('/dashboard');
      await expect(page).toHaveURL('/login');
    });
  });

  test.describe('User Logout', () => {
    test('should logout successfully', async ({ page }) => {
      await helpers.login();
      await helpers.logout();

      // Should be redirected to login page
      await expect(page).toHaveURL('/login');
      await expect(page.locator('[data-testid="user-menu"]')).not.toBeVisible();
    });

    test('should redirect to login when accessing protected route after logout', async ({ page }) => {
      await helpers.login();
      await helpers.logout();

      // Try to access protected route
      await page.goto('/dashboard');
      await expect(page).toHaveURL('/login');
    });
  });

  test.describe('Password Management', () => {
    test('should change password successfully', async ({ page }) => {
      await helpers.login();

      // Navigate to profile settings
      await page.click('[data-testid="user-menu"]');
      await page.click('[data-testid="profile-settings"]');

      // Change password
      await page.click('[data-testid="change-password-button"]');
      await page.fill('[data-testid="current-password-input"]', process.env.E2E_TEST_PASSWORD || 'TestPassword123!');
      await page.fill('[data-testid="new-password-input"]', 'NewPassword123!');
      await page.fill('[data-testid="confirm-password-input"]', 'NewPassword123!');
      await page.click('[data-testid="save-password-button"]');

      // Should show success message
      await helpers.waitForToast('Password changed successfully');

      // Logout and login with new password
      await helpers.logout();
      await helpers.login(undefined, 'NewPassword123!');

      // Should be able to login with new password
      await expect(page).toHaveURL('/dashboard');
    });

    test('should show error for incorrect current password', async ({ page }) => {
      await helpers.login();

      // Navigate to profile settings
      await page.click('[data-testid="user-menu"]');
      await page.click('[data-testid="profile-settings"]');

      // Try to change password with wrong current password
      await page.click('[data-testid="change-password-button"]');
      await page.fill('[data-testid="current-password-input"]', 'wrongpassword');
      await page.fill('[data-testid="new-password-input"]', 'NewPassword123!');
      await page.fill('[data-testid="confirm-password-input"]', 'NewPassword123!');
      await page.click('[data-testid="save-password-button"]');

      // Should show error message
      await helpers.waitForToast('Current password is incorrect');
    });
  });

  test.describe('Profile Management', () => {
    test('should update profile information', async ({ page }) => {
      await helpers.login();

      // Navigate to profile settings
      await page.click('[data-testid="user-menu"]');
      await page.click('[data-testid="profile-settings"]');

      // Update profile
      await page.fill('[data-testid="firstName-input"]', 'Updated');
      await page.fill('[data-testid="lastName-input"]', 'Name');
      
      // Update skills
      await page.fill('[data-testid="skills-input"]', 'TypeScript');
      await page.press('[data-testid="skills-input"]', 'Enter');
      
      await page.click('[data-testid="save-profile-button"]');

      // Should show success message
      await helpers.waitForToast('Profile updated successfully');

      // Verify changes are reflected in UI
      await expect(page.locator('[data-testid="user-name"]')).toContainText('Updated Name');
    });

    test('should validate profile update data', async ({ page }) => {
      await helpers.login();

      // Navigate to profile settings
      await page.click('[data-testid="user-menu"]');
      await page.click('[data-testid="profile-settings"]');

      // Try to save with invalid data
      await page.fill('[data-testid="firstName-input"]', '');
      await page.fill('[data-testid="lastName-input"]', '');
      await page.click('[data-testid="save-profile-button"]');

      // Should show validation errors
      await expect(page.locator('[data-testid="firstName-error"]')).toContainText('required');
      await expect(page.locator('[data-testid="lastName-error"]')).toContainText('required');
    });
  });

  test.describe('Session Management', () => {
    test('should maintain session across page refreshes', async ({ page }) => {
      await helpers.login();

      // Refresh the page
      await page.reload();

      // Should still be authenticated
      await expect(page).toHaveURL('/dashboard');
      await expect(page.locator('[data-testid="user-menu"]')).toBeVisible();
    });

    test('should handle expired session gracefully', async ({ page }) => {
      await helpers.login();

      // Simulate expired session by clearing localStorage
      await page.evaluate(() => {
        localStorage.removeItem('tokens');
      });

      // Try to access protected route
      await page.goto('/teams');

      // Should be redirected to login
      await expect(page).toHaveURL('/login');
      await helpers.waitForToast('Session expired');
    });
  });
});
