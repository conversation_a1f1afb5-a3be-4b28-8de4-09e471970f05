import { useEffect, useRef, useState } from 'react';
import { useAppSelector } from '../store';
import { websocketService } from '../services/websocketService';

export const useWebSocket = () => {
  const { tokens, isAuthenticated } = useAppSelector((state) => state.auth);
  const [isConnected, setIsConnected] = useState(false);
  const connectionInitialized = useRef(false);

  useEffect(() => {
    if (isAuthenticated && tokens?.accessToken && !connectionInitialized.current) {
      websocketService.connect(tokens.accessToken);
      connectionInitialized.current = true;
      setIsConnected(true);
    }

    if (!isAuthenticated && connectionInitialized.current) {
      websocketService.disconnect();
      connectionInitialized.current = false;
      setIsConnected(false);
    }

    return () => {
      if (connectionInitialized.current) {
        websocketService.disconnect();
        connectionInitialized.current = false;
        setIsConnected(false);
      }
    };
  }, [isAuthenticated, tokens?.accessToken]);

  return {
    isConnected,
    websocketService,
  };
};

export const useTaskSubscription = (taskId: string | null) => {
  const { websocketService } = useWebSocket();
  const subscribedTaskId = useRef<string | null>(null);

  useEffect(() => {
    if (taskId && taskId !== subscribedTaskId.current) {
      // Unsubscribe from previous task
      if (subscribedTaskId.current) {
        websocketService.unsubscribeFromTask(subscribedTaskId.current);
      }

      // Subscribe to new task
      websocketService.subscribeToTask(taskId);
      subscribedTaskId.current = taskId;
    }

    return () => {
      if (subscribedTaskId.current) {
        websocketService.unsubscribeFromTask(subscribedTaskId.current);
        subscribedTaskId.current = null;
      }
    };
  }, [taskId, websocketService]);
};

export const useProjectSubscription = (projectId: string | null) => {
  const { websocketService } = useWebSocket();
  const subscribedProjectId = useRef<string | null>(null);

  useEffect(() => {
    if (projectId && projectId !== subscribedProjectId.current) {
      // Unsubscribe from previous project
      if (subscribedProjectId.current) {
        websocketService.unsubscribeFromProject(subscribedProjectId.current);
      }

      // Subscribe to new project
      websocketService.subscribeToProject(projectId);
      subscribedProjectId.current = projectId;
    }

    return () => {
      if (subscribedProjectId.current) {
        websocketService.unsubscribeFromProject(subscribedProjectId.current);
        subscribedProjectId.current = null;
      }
    };
  }, [projectId, websocketService]);
};

export const useMeetingRoom = (meetingId: string | null) => {
  const { websocketService } = useWebSocket();
  const joinedMeetingId = useRef<string | null>(null);

  useEffect(() => {
    if (meetingId && meetingId !== joinedMeetingId.current) {
      // Leave previous meeting
      if (joinedMeetingId.current) {
        websocketService.leaveMeeting(joinedMeetingId.current);
      }

      // Join new meeting
      websocketService.joinMeeting(meetingId);
      joinedMeetingId.current = meetingId;
    }

    return () => {
      if (joinedMeetingId.current) {
        websocketService.leaveMeeting(joinedMeetingId.current);
        joinedMeetingId.current = null;
      }
    };
  }, [meetingId, websocketService]);
};

export const useTypingIndicator = (room: string, context: string) => {
  const { websocketService } = useWebSocket();
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isTypingRef = useRef(false);

  const startTyping = () => {
    if (!isTypingRef.current) {
      websocketService.startTyping(room, context);
      isTypingRef.current = true;
    }

    // Clear existing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    // Set timeout to stop typing after 3 seconds of inactivity
    typingTimeoutRef.current = setTimeout(() => {
      stopTyping();
    }, 3000);
  };

  const stopTyping = () => {
    if (isTypingRef.current) {
      websocketService.stopTyping(room, context);
      isTypingRef.current = false;
    }

    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
      typingTimeoutRef.current = null;
    }
  };

  useEffect(() => {
    return () => {
      stopTyping();
    };
  }, []);

  return {
    startTyping,
    stopTyping,
  };
};

export const usePresence = () => {
  const { websocketService } = useWebSocket();
  const [status, setStatus] = useState<'online' | 'away' | 'busy' | 'offline'>('online');

  const updateStatus = (newStatus: 'online' | 'away' | 'busy' | 'offline') => {
    setStatus(newStatus);
    websocketService.updatePresence(newStatus);
  };

  // Auto-detect away status based on user activity
  useEffect(() => {
    let awayTimeout: NodeJS.Timeout;

    const resetAwayTimer = () => {
      clearTimeout(awayTimeout);
      
      if (status === 'away') {
        updateStatus('online');
      }

      awayTimeout = setTimeout(() => {
        if (status === 'online') {
          updateStatus('away');
        }
      }, 5 * 60 * 1000); // 5 minutes
    };

    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'];
    
    events.forEach(event => {
      document.addEventListener(event, resetAwayTimer, true);
    });

    resetAwayTimer();

    return () => {
      clearTimeout(awayTimeout);
      events.forEach(event => {
        document.removeEventListener(event, resetAwayTimer, true);
      });
    };
  }, [status]);

  // Set offline when page is about to unload
  useEffect(() => {
    const handleBeforeUnload = () => {
      websocketService.updatePresence('offline');
    };

    window.addEventListener('beforeunload', handleBeforeUnload);

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, []);

  return {
    status,
    updateStatus,
  };
};
