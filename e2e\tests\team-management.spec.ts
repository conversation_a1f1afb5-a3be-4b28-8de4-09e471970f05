import { test, expect } from '@playwright/test';
import { TestHelpers } from '../utils/test-helpers';

test.describe('Team Management', () => {
  let helpers: TestHelpers;

  test.beforeEach(async ({ page }) => {
    helpers = new TestHelpers(page);
    await helpers.login();
  });

  test.describe('Team Creation', () => {
    test('should create a new team successfully', async ({ page }) => {
      const teamName = `Test Team ${helpers.generateRandomString()}`;
      const teamDescription = 'A test team for E2E testing';

      await helpers.createTeam(teamName, teamDescription);

      // Verify team appears in the list
      await expect(page.locator(`[data-testid="team-card"]:has-text("${teamName}")`)).toBeVisible();
      await expect(page.locator(`text=${teamDescription}`)).toBeVisible();

      // Verify team stats
      await expect(page.locator('[data-testid="team-member-count"]')).toContainText('1 members');
      await expect(page.locator('[data-testid="team-project-count"]')).toContainText('0 projects');
    });

    test('should show validation errors for invalid team data', async ({ page }) => {
      await helpers.navigateToTeams();
      await page.click('[data-testid="create-team-fab"]');

      // Try to create team without name
      await page.click('[data-testid="create-team-button"]');

      // Should show validation error
      await expect(page.locator('[data-testid="team-name-error"]')).toContainText('required');
    });

    test('should create team with AI-powered suggestions', async ({ page }) => {
      const teamName = `AI Team ${helpers.generateRandomString()}`;

      await helpers.navigateToTeams();
      await page.click('[data-testid="create-team-fab"]');

      await page.fill('[data-testid="team-name-input"]', teamName);
      await page.fill('[data-testid="team-description-input"]', 'AI and machine learning team');

      // Check if AI suggestions appear
      const aiSuggestions = page.locator('[data-testid="ai-suggestions"]');
      if (await aiSuggestions.isVisible()) {
        await expect(aiSuggestions).toContainText('AI suggests');
      }

      await page.click('[data-testid="create-team-button"]');

      // Verify team creation
      await expect(page.locator(`text=${teamName}`)).toBeVisible();
    });
  });

  test.describe('Team Details and Navigation', () => {
    test('should navigate to team details page', async ({ page }) => {
      const teamName = `Detail Team ${helpers.generateRandomString()}`;
      await helpers.createTeam(teamName);

      // Click on team card
      await page.click(`[data-testid="team-card"]:has-text("${teamName}")`);

      // Should navigate to team details
      await expect(page).toHaveURL(/\/teams\/[a-zA-Z0-9-]+$/);
      await expect(page.locator(`h1:has-text("${teamName}")`)).toBeVisible();
    });

    test('should display team information correctly', async ({ page }) => {
      const teamName = `Info Team ${helpers.generateRandomString()}`;
      const teamDescription = 'Team with detailed information';
      
      await helpers.createTeam(teamName, teamDescription);
      await page.click(`[data-testid="team-card"]:has-text("${teamName}")`);

      // Verify team information
      await expect(page.locator(`h1:has-text("${teamName}")`)).toBeVisible();
      await expect(page.locator(`text=${teamDescription}`)).toBeVisible();
      await expect(page.locator('[data-testid="team-member-count"]')).toContainText('1 members');
      await expect(page.locator('[data-testid="team-project-count"]')).toContainText('0 projects');
    });

    test('should show team tabs and navigate between them', async ({ page }) => {
      const teamName = `Tab Team ${helpers.generateRandomString()}`;
      await helpers.createTeam(teamName);
      await page.click(`[data-testid="team-card"]:has-text("${teamName}")`);

      // Check tabs are present
      await expect(page.locator('[data-testid="team-tab-overview"]')).toBeVisible();
      await expect(page.locator('[data-testid="team-tab-members"]')).toBeVisible();
      await expect(page.locator('[data-testid="team-tab-analytics"]')).toBeVisible();
      await expect(page.locator('[data-testid="team-tab-projects"]')).toBeVisible();

      // Navigate to members tab
      await page.click('[data-testid="team-tab-members"]');
      await expect(page.locator('[data-testid="team-members-list"]')).toBeVisible();

      // Navigate to analytics tab
      await page.click('[data-testid="team-tab-analytics"]');
      await expect(page.locator('[data-testid="team-analytics"]')).toBeVisible();
    });
  });

  test.describe('Team Member Management', () => {
    test('should add team member successfully', async ({ page }) => {
      const teamName = `Member Team ${helpers.generateRandomString()}`;
      await helpers.createTeam(teamName);
      await page.click(`[data-testid="team-card"]:has-text("${teamName}")`);

      // Navigate to members tab
      await page.click('[data-testid="team-tab-members"]');

      // Add member
      await page.click('[data-testid="add-member-button"]');
      await page.fill('[data-testid="member-email-input"]', '<EMAIL>');
      await page.click('[data-testid="member-role-select"]');
      await page.click('[data-value="MEMBER"]');
      await page.click('[data-testid="add-member-submit"]');

      // Should show success message
      await helpers.waitForToast('Member added successfully');
    });

    test('should show validation error for invalid email', async ({ page }) => {
      const teamName = `Validation Team ${helpers.generateRandomString()}`;
      await helpers.createTeam(teamName);
      await page.click(`[data-testid="team-card"]:has-text("${teamName}")`);

      await page.click('[data-testid="team-tab-members"]');
      await page.click('[data-testid="add-member-button"]');

      // Try to add member with invalid email
      await page.fill('[data-testid="member-email-input"]', 'invalid-email');
      await page.click('[data-testid="add-member-submit"]');

      // Should show validation error
      await expect(page.locator('[data-testid="member-email-error"]')).toContainText('valid email');
    });

    test('should remove team member', async ({ page }) => {
      const teamName = `Remove Team ${helpers.generateRandomString()}`;
      await helpers.createTeam(teamName);
      await page.click(`[data-testid="team-card"]:has-text("${teamName}")`);

      // First add a member (assuming we have a test user)
      await page.click('[data-testid="team-tab-members"]');
      await page.click('[data-testid="add-member-button"]');
      await page.fill('[data-testid="member-email-input"]', '<EMAIL>');
      await page.click('[data-testid="add-member-submit"]');

      // Wait for member to be added
      await helpers.waitForToast('Member added successfully');

      // Remove the member
      const memberRow = page.locator('[data-testid="member-row"]:has-text("<EMAIL>")');
      await memberRow.locator('[data-testid="remove-member-button"]').click();

      // Confirm removal
      await page.click('[data-testid="confirm-remove-button"]');

      // Should show success message
      await helpers.waitForToast('Member removed successfully');
      await expect(memberRow).not.toBeVisible();
    });
  });

  test.describe('Team Analytics', () => {
    test('should display team capacity analysis', async ({ page }) => {
      const teamName = `Analytics Team ${helpers.generateRandomString()}`;
      await helpers.createTeam(teamName);
      await page.click(`[data-testid="team-card"]:has-text("${teamName}")`);

      // Navigate to analytics tab
      await page.click('[data-testid="team-tab-analytics"]');

      // Generate capacity analysis
      await page.click('[data-testid="generate-analysis-button"]');

      // Wait for analysis to load
      await helpers.waitForLoadingToFinish();

      // Verify analytics are displayed
      await expect(page.locator('[data-testid="team-utilization"]')).toBeVisible();
      await expect(page.locator('[data-testid="team-bottlenecks"]')).toBeVisible();
      await expect(page.locator('[data-testid="ai-recommendations"]')).toBeVisible();
    });

    test('should show AI recommendations for team optimization', async ({ page }) => {
      const teamName = `AI Rec Team ${helpers.generateRandomString()}`;
      await helpers.createTeam(teamName);
      await page.click(`[data-testid="team-card"]:has-text("${teamName}")`);

      await page.click('[data-testid="team-tab-analytics"]');
      await page.click('[data-testid="generate-analysis-button"]');

      await helpers.waitForLoadingToFinish();

      // Check for AI recommendations
      const recommendations = page.locator('[data-testid="ai-recommendations"] li');
      await expect(recommendations).toHaveCount.greaterThan(0);
    });
  });

  test.describe('Team Settings', () => {
    test('should update team information', async ({ page }) => {
      const teamName = `Settings Team ${helpers.generateRandomString()}`;
      await helpers.createTeam(teamName);
      await page.click(`[data-testid="team-card"]:has-text("${teamName}")`);

      // Navigate to settings
      await page.click('[data-testid="team-settings-button"]');

      // Update team information
      const newName = `Updated ${teamName}`;
      const newDescription = 'Updated team description';

      await page.fill('[data-testid="team-name-input"]', newName);
      await page.fill('[data-testid="team-description-input"]', newDescription);
      await page.click('[data-testid="save-team-button"]');

      // Should show success message
      await helpers.waitForToast('Team updated successfully');

      // Verify changes
      await expect(page.locator(`h1:has-text("${newName}")`)).toBeVisible();
      await expect(page.locator(`text=${newDescription}`)).toBeVisible();
    });

    test('should configure team working hours', async ({ page }) => {
      const teamName = `Hours Team ${helpers.generateRandomString()}`;
      await helpers.createTeam(teamName);
      await page.click(`[data-testid="team-card"]:has-text("${teamName}")`);

      await page.click('[data-testid="team-settings-button"]');

      // Configure working hours
      await page.fill('[data-testid="working-hours-start"]', '09:00');
      await page.fill('[data-testid="working-hours-end"]', '17:00');
      await page.click('[data-testid="timezone-select"]');
      await page.click('[data-value="America/New_York"]');

      await page.click('[data-testid="save-settings-button"]');

      // Should show success message
      await helpers.waitForToast('Settings updated successfully');
    });
  });

  test.describe('Team Deletion', () => {
    test('should delete team successfully', async ({ page }) => {
      const teamName = `Delete Team ${helpers.generateRandomString()}`;
      await helpers.createTeam(teamName);

      await helpers.deleteTeam(teamName);

      // Verify team is removed from list
      await expect(page.locator(`[data-testid="team-card"]:has-text("${teamName}")`)).not.toBeVisible();
      await helpers.waitForToast('Team deleted successfully');
    });

    test('should show confirmation dialog before deletion', async ({ page }) => {
      const teamName = `Confirm Delete Team ${helpers.generateRandomString()}`;
      await helpers.createTeam(teamName);

      // Try to delete team
      const teamCard = page.locator(`[data-testid="team-card"]:has-text("${teamName}")`);
      await teamCard.locator('[data-testid="team-more-options"]').click();
      await page.click('[data-testid="delete-team-option"]');

      // Should show confirmation dialog
      await expect(page.locator('[data-testid="confirm-delete-dialog"]')).toBeVisible();
      await expect(page.locator('[data-testid="confirm-delete-message"]')).toContainText(teamName);

      // Cancel deletion
      await page.click('[data-testid="cancel-delete-button"]');
      await expect(page.locator('[data-testid="confirm-delete-dialog"]')).not.toBeVisible();

      // Team should still be visible
      await expect(teamCard).toBeVisible();
    });
  });

  test.describe('Team Search and Filtering', () => {
    test('should search teams by name', async ({ page }) => {
      const team1Name = `Search Team A ${helpers.generateRandomString()}`;
      const team2Name = `Search Team B ${helpers.generateRandomString()}`;

      await helpers.createTeam(team1Name);
      await helpers.createTeam(team2Name);

      // Search for team A
      await page.fill('[data-testid="team-search-input"]', 'Team A');

      // Should show only team A
      await expect(page.locator(`[data-testid="team-card"]:has-text("${team1Name}")`)).toBeVisible();
      await expect(page.locator(`[data-testid="team-card"]:has-text("${team2Name}")`)).not.toBeVisible();

      // Clear search
      await page.fill('[data-testid="team-search-input"]', '');

      // Should show both teams
      await expect(page.locator(`[data-testid="team-card"]:has-text("${team1Name}")`)).toBeVisible();
      await expect(page.locator(`[data-testid="team-card"]:has-text("${team2Name}")`)).toBeVisible();
    });
  });
});
