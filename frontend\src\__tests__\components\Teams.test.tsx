import React from 'react';
import { screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { jest } from '@jest/globals';

import Teams from '../../pages/Teams';
import { teamService } from '../../services/teamService';
import {
  renderWithProviders,
  createAuthenticatedState,
  createMockTeam,
  createMockUser,
  mockApiResponse,
  mockApiError,
} from '../utils/testUtils';

// Mock the team service
jest.mock('../../services/teamService', () => ({
  teamService: {
    getTeams: jest.fn(),
    createTeam: jest.fn(),
    deleteTeam: jest.fn(),
  },
}));

const mockTeamService = teamService as jest.Mocked<typeof teamService>;

// Mock react-router-dom navigation
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
}));

describe('Teams Component', () => {
  const mockUser = createMockUser();
  const mockTeams = [
    createMockTeam({ id: 'team1', name: 'Development Team', memberCount: 5, projectCount: 3 }),
    createMockTeam({ id: 'team2', name: 'Design Team', memberCount: 3, projectCount: 2 }),
    createMockTeam({ id: 'team3', name: 'Marketing Team', memberCount: 4, projectCount: 1 }),
  ];

  beforeEach(() => {
    jest.clearAllMocks();
    mockNavigate.mockClear();
  });

  describe('Rendering', () => {
    it('should render teams page with header', async () => {
      // Arrange
      mockTeamService.getTeams.mockResolvedValue(mockApiResponse(mockTeams));

      // Act
      renderWithProviders(<Teams />, {
        preloadedState: createAuthenticatedState(mockUser),
      });

      // Assert
      expect(screen.getByRole('heading', { name: /teams/i })).toBeInTheDocument();
      expect(screen.getByText(/manage your teams and collaborate effectively/i)).toBeInTheDocument();
    });

    it('should show loading state initially', () => {
      // Arrange
      mockTeamService.getTeams.mockImplementation(() => new Promise(() => {})); // Never resolves

      // Act
      renderWithProviders(<Teams />, {
        preloadedState: createAuthenticatedState(mockUser),
      });

      // Assert
      expect(screen.getByRole('progressbar')).toBeInTheDocument();
    });

    it('should render teams list after loading', async () => {
      // Arrange
      mockTeamService.getTeams.mockResolvedValue(mockApiResponse(mockTeams));

      // Act
      renderWithProviders(<Teams />, {
        preloadedState: createAuthenticatedState(mockUser),
      });

      // Assert
      await waitFor(() => {
        expect(screen.getByText('Development Team')).toBeInTheDocument();
        expect(screen.getByText('Design Team')).toBeInTheDocument();
        expect(screen.getByText('Marketing Team')).toBeInTheDocument();
      });

      // Check team details
      expect(screen.getByText('5 members')).toBeInTheDocument();
      expect(screen.getByText('3 projects')).toBeInTheDocument();
    });

    it('should show empty state when no teams exist', async () => {
      // Arrange
      mockTeamService.getTeams.mockResolvedValue(mockApiResponse([]));

      // Act
      renderWithProviders(<Teams />, {
        preloadedState: createAuthenticatedState(mockUser),
      });

      // Assert
      await waitFor(() => {
        expect(screen.getByText(/you're not a member of any teams yet/i)).toBeInTheDocument();
        expect(screen.getByText(/create your first team to get started/i)).toBeInTheDocument();
      });
    });

    it('should render floating action button', async () => {
      // Arrange
      mockTeamService.getTeams.mockResolvedValue(mockApiResponse(mockTeams));

      // Act
      renderWithProviders(<Teams />, {
        preloadedState: createAuthenticatedState(mockUser),
      });

      // Assert
      await waitFor(() => {
        const fab = screen.getByRole('button', { name: /create team/i });
        expect(fab).toBeInTheDocument();
      });
    });
  });

  describe('Team Interactions', () => {
    it('should navigate to team detail when team card is clicked', async () => {
      // Arrange
      mockTeamService.getTeams.mockResolvedValue(mockApiResponse(mockTeams));

      // Act
      renderWithProviders(<Teams />, {
        preloadedState: createAuthenticatedState(mockUser),
      });

      await waitFor(() => {
        expect(screen.getByText('Development Team')).toBeInTheDocument();
      });

      const teamCard = screen.getByText('Development Team').closest('[role="button"]') || 
                      screen.getByText('Development Team').closest('.MuiCard-root');
      
      if (teamCard) {
        await userEvent.click(teamCard);
      }

      // Assert
      expect(mockNavigate).toHaveBeenCalledWith('/teams/team1');
    });

    it('should open team menu when more options button is clicked', async () => {
      // Arrange
      mockTeamService.getTeams.mockResolvedValue(mockApiResponse(mockTeams));

      // Act
      renderWithProviders(<Teams />, {
        preloadedState: createAuthenticatedState(mockUser),
      });

      await waitFor(() => {
        expect(screen.getByText('Development Team')).toBeInTheDocument();
      });

      const moreButton = screen.getAllByLabelText(/more/i)[0];
      await userEvent.click(moreButton);

      // Assert
      expect(screen.getByText('Settings')).toBeInTheDocument();
      expect(screen.getByText('Delete Team')).toBeInTheDocument();
    });

    it('should navigate to team settings from menu', async () => {
      // Arrange
      mockTeamService.getTeams.mockResolvedValue(mockApiResponse(mockTeams));

      // Act
      renderWithProviders(<Teams />, {
        preloadedState: createAuthenticatedState(mockUser),
      });

      await waitFor(() => {
        expect(screen.getByText('Development Team')).toBeInTheDocument();
      });

      const moreButton = screen.getAllByLabelText(/more/i)[0];
      await userEvent.click(moreButton);

      const settingsOption = screen.getByText('Settings');
      await userEvent.click(settingsOption);

      // Assert
      expect(mockNavigate).toHaveBeenCalledWith('/teams/team1/settings');
    });
  });

  describe('Create Team Dialog', () => {
    it('should open create team dialog when FAB is clicked', async () => {
      // Arrange
      mockTeamService.getTeams.mockResolvedValue(mockApiResponse(mockTeams));

      // Act
      renderWithProviders(<Teams />, {
        preloadedState: createAuthenticatedState(mockUser),
      });

      await waitFor(() => {
        const fab = screen.getByRole('button', { name: /create team/i });
        expect(fab).toBeInTheDocument();
      });

      const fab = screen.getByRole('button', { name: /create team/i });
      await userEvent.click(fab);

      // Assert
      expect(screen.getByRole('dialog')).toBeInTheDocument();
      expect(screen.getByText('Create New Team')).toBeInTheDocument();
      expect(screen.getByLabelText(/team name/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/description/i)).toBeInTheDocument();
    });

    it('should create team successfully', async () => {
      // Arrange
      const newTeam = createMockTeam({ id: 'new-team', name: 'New Team' });
      mockTeamService.getTeams.mockResolvedValue(mockApiResponse(mockTeams));
      mockTeamService.createTeam.mockResolvedValue(mockApiResponse(newTeam));

      // Act
      renderWithProviders(<Teams />, {
        preloadedState: createAuthenticatedState(mockUser),
      });

      await waitFor(() => {
        const fab = screen.getByRole('button', { name: /create team/i });
        expect(fab).toBeInTheDocument();
      });

      // Open dialog
      const fab = screen.getByRole('button', { name: /create team/i });
      await userEvent.click(fab);

      // Fill form
      const nameInput = screen.getByLabelText(/team name/i);
      const descriptionInput = screen.getByLabelText(/description/i);
      
      await userEvent.type(nameInput, 'New Team');
      await userEvent.type(descriptionInput, 'A new team for testing');

      // Submit form
      const createButton = screen.getByRole('button', { name: /create team/i });
      await userEvent.click(createButton);

      // Assert
      await waitFor(() => {
        expect(mockTeamService.createTeam).toHaveBeenCalledWith({
          name: 'New Team',
          description: 'A new team for testing',
        });
      });
    });

    it('should show validation errors for invalid input', async () => {
      // Arrange
      mockTeamService.getTeams.mockResolvedValue(mockApiResponse(mockTeams));

      // Act
      renderWithProviders(<Teams />, {
        preloadedState: createAuthenticatedState(mockUser),
      });

      await waitFor(() => {
        const fab = screen.getByRole('button', { name: /create team/i });
        expect(fab).toBeInTheDocument();
      });

      // Open dialog
      const fab = screen.getByRole('button', { name: /create team/i });
      await userEvent.click(fab);

      // Try to submit without filling required fields
      const createButton = screen.getByRole('button', { name: /create team/i });
      await userEvent.click(createButton);

      // Assert
      await waitFor(() => {
        expect(screen.getByText(/team name is required/i)).toBeInTheDocument();
      });
    });

    it('should handle create team API error', async () => {
      // Arrange
      mockTeamService.getTeams.mockResolvedValue(mockApiResponse(mockTeams));
      mockTeamService.createTeam.mockRejectedValue(mockApiError('Team name already exists'));

      // Act
      renderWithProviders(<Teams />, {
        preloadedState: createAuthenticatedState(mockUser),
      });

      await waitFor(() => {
        const fab = screen.getByRole('button', { name: /create team/i });
        expect(fab).toBeInTheDocument();
      });

      // Open dialog and fill form
      const fab = screen.getByRole('button', { name: /create team/i });
      await userEvent.click(fab);

      const nameInput = screen.getByLabelText(/team name/i);
      await userEvent.type(nameInput, 'Existing Team');

      const createButton = screen.getByRole('button', { name: /create team/i });
      await userEvent.click(createButton);

      // Assert
      await waitFor(() => {
        expect(mockTeamService.createTeam).toHaveBeenCalled();
      });
    });

    it('should close dialog when cancel is clicked', async () => {
      // Arrange
      mockTeamService.getTeams.mockResolvedValue(mockApiResponse(mockTeams));

      // Act
      renderWithProviders(<Teams />, {
        preloadedState: createAuthenticatedState(mockUser),
      });

      await waitFor(() => {
        const fab = screen.getByRole('button', { name: /create team/i });
        expect(fab).toBeInTheDocument();
      });

      // Open dialog
      const fab = screen.getByRole('button', { name: /create team/i });
      await userEvent.click(fab);

      expect(screen.getByRole('dialog')).toBeInTheDocument();

      // Click cancel
      const cancelButton = screen.getByRole('button', { name: /cancel/i });
      await userEvent.click(cancelButton);

      // Assert
      await waitFor(() => {
        expect(screen.queryByRole('dialog')).not.toBeInTheDocument();
      });
    });
  });

  describe('Delete Team', () => {
    it('should delete team successfully', async () => {
      // Arrange
      mockTeamService.getTeams.mockResolvedValue(mockApiResponse(mockTeams));
      mockTeamService.deleteTeam.mockResolvedValue(mockApiResponse({}));

      // Act
      renderWithProviders(<Teams />, {
        preloadedState: createAuthenticatedState(mockUser),
      });

      await waitFor(() => {
        expect(screen.getByText('Development Team')).toBeInTheDocument();
      });

      // Open menu and click delete
      const moreButton = screen.getAllByLabelText(/more/i)[0];
      await userEvent.click(moreButton);

      const deleteOption = screen.getByText('Delete Team');
      await userEvent.click(deleteOption);

      // Assert
      await waitFor(() => {
        expect(mockTeamService.deleteTeam).toHaveBeenCalledWith('team1');
      });
    });

    it('should handle delete team API error', async () => {
      // Arrange
      mockTeamService.getTeams.mockResolvedValue(mockApiResponse(mockTeams));
      mockTeamService.deleteTeam.mockRejectedValue(mockApiError('Cannot delete team with active projects'));

      // Act
      renderWithProviders(<Teams />, {
        preloadedState: createAuthenticatedState(mockUser),
      });

      await waitFor(() => {
        expect(screen.getByText('Development Team')).toBeInTheDocument();
      });

      // Open menu and click delete
      const moreButton = screen.getAllByLabelText(/more/i)[0];
      await userEvent.click(moreButton);

      const deleteOption = screen.getByText('Delete Team');
      await userEvent.click(deleteOption);

      // Assert
      await waitFor(() => {
        expect(mockTeamService.deleteTeam).toHaveBeenCalledWith('team1');
      });
    });
  });

  describe('Error Handling', () => {
    it('should handle API error when loading teams', async () => {
      // Arrange
      mockTeamService.getTeams.mockRejectedValue(mockApiError('Failed to load teams'));

      // Act
      renderWithProviders(<Teams />, {
        preloadedState: createAuthenticatedState(mockUser),
      });

      // Assert
      await waitFor(() => {
        expect(mockTeamService.getTeams).toHaveBeenCalled();
      });
    });
  });
});

// Additional component tests for Tasks, Dashboard, and authentication forms will be created next...
