import { jest } from '@jest/globals';
import { Server as HTTPServer } from 'http';
import { Server as SocketIOServer } from 'socket.io';
import jwt from 'jsonwebtoken';
import { websocketService } from '../../services/websocketService';
import { prisma } from '../../config/database';

const mockPrisma = prisma as jest.Mocked<typeof prisma>;

// Mock Socket.IO
const mockSocket = {
  id: 'socket123',
  userId: 'user1',
  user: global.mockUser,
  join: jest.fn(),
  leave: jest.fn(),
  emit: jest.fn(),
  broadcast: {
    to: jest.fn().mockReturnValue({
      emit: jest.fn(),
    }),
  },
  to: jest.fn().mockReturnValue({
    emit: jest.fn(),
  }),
  handshake: {
    auth: {
      token: 'valid-jwt-token',
    },
  },
  disconnect: jest.fn(),
  on: jest.fn(),
};

const mockIO = {
  use: jest.fn(),
  on: jest.fn(),
  to: jest.fn().mockReturnValue({
    emit: jest.fn(),
  }),
  emit: jest.fn(),
} as any;

// Mock HTTP Server
const mockServer = {} as HTTPServer;

// Mock JWT
jest.mock('jsonwebtoken', () => ({
  verify: jest.fn(),
}));

const mockJwtVerify = jwt.verify as jest.MockedFunction<typeof jwt.verify>;

describe('WebSocketService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Reset the service state
    (websocketService as any).connectedUsers = new Map();
    (websocketService as any).userRooms = new Map();
    (websocketService as any).io = null;
  });

  describe('initialize', () => {
    it('should initialize WebSocket server with correct configuration', () => {
      // Act
      websocketService.initialize(mockServer);

      // Assert
      expect((websocketService as any).io).toBeDefined();
    });
  });

  describe('authenticateSocket', () => {
    it('should authenticate socket with valid token', async () => {
      // Arrange
      const mockDecoded = {
        userId: 'user1',
        type: 'access',
      };

      mockJwtVerify.mockReturnValue(mockDecoded as any);
      mockPrisma.user.findUnique.mockResolvedValue(global.mockUser as any);

      const next = jest.fn();

      // Act
      await (websocketService as any).authenticateSocket(mockSocket, next);

      // Assert
      expect(mockSocket.userId).toBe('user1');
      expect(mockSocket.user).toEqual(global.mockUser);
      expect(next).toHaveBeenCalledWith();
    });

    it('should reject socket with invalid token', async () => {
      // Arrange
      const mockSocketWithoutToken = {
        ...mockSocket,
        handshake: { auth: {} },
      };

      const next = jest.fn();

      // Act
      await (websocketService as any).authenticateSocket(mockSocketWithoutToken, next);

      // Assert
      expect(next).toHaveBeenCalledWith(new Error('Authentication token required'));
    });

    it('should reject socket with expired token', async () => {
      // Arrange
      mockJwtVerify.mockImplementation(() => {
        throw new Error('Token expired');
      });

      const next = jest.fn();

      // Act
      await (websocketService as any).authenticateSocket(mockSocket, next);

      // Assert
      expect(next).toHaveBeenCalledWith(new Error('Authentication failed'));
    });

    it('should reject socket for non-existent user', async () => {
      // Arrange
      const mockDecoded = {
        userId: 'nonexistent',
        type: 'access',
      };

      mockJwtVerify.mockReturnValue(mockDecoded as any);
      mockPrisma.user.findUnique.mockResolvedValue(null);

      const next = jest.fn();

      // Act
      await (websocketService as any).authenticateSocket(mockSocket, next);

      // Assert
      expect(next).toHaveBeenCalledWith(new Error('User not found'));
    });
  });

  describe('handleConnection', () => {
    beforeEach(() => {
      (websocketService as any).io = mockIO;
    });

    it('should handle new socket connection', async () => {
      // Arrange
      mockPrisma.teamMember.findMany.mockResolvedValue([
        { teamId: 'team1', userId: 'user1' },
        { teamId: 'team2', userId: 'user1' },
      ] as any);

      // Act
      await (websocketService as any).handleConnection(mockSocket);

      // Assert
      expect(mockSocket.join).toHaveBeenCalledWith('user:user1');
      expect(mockSocket.join).toHaveBeenCalledWith('team:team1');
      expect(mockSocket.join).toHaveBeenCalledWith('team:team2');
      
      const connectedUsers = (websocketService as any).connectedUsers;
      expect(connectedUsers.has('user1')).toBe(true);
      expect(connectedUsers.get('user1').has('socket123')).toBe(true);
    });

    it('should set up event handlers for socket', async () => {
      // Arrange
      mockPrisma.teamMember.findMany.mockResolvedValue([]);

      // Act
      await (websocketService as any).handleConnection(mockSocket);

      // Assert
      expect(mockSocket.on).toHaveBeenCalledWith('task:subscribe', expect.any(Function));
      expect(mockSocket.on).toHaveBeenCalledWith('task:unsubscribe', expect.any(Function));
      expect(mockSocket.on).toHaveBeenCalledWith('meeting:join', expect.any(Function));
      expect(mockSocket.on).toHaveBeenCalledWith('typing:start', expect.any(Function));
      expect(mockSocket.on).toHaveBeenCalledWith('presence:update', expect.any(Function));
      expect(mockSocket.on).toHaveBeenCalledWith('disconnect', expect.any(Function));
    });
  });

  describe('handleDisconnection', () => {
    it('should clean up user connections on disconnect', () => {
      // Arrange
      const connectedUsers = new Map();
      connectedUsers.set('user1', new Set(['socket123', 'socket456']));
      (websocketService as any).connectedUsers = connectedUsers;
      (websocketService as any).userRooms = new Map([['user1', new Set(['team:team1'])]]);
      (websocketService as any).io = mockIO;

      // Act
      (websocketService as any).handleDisconnection(mockSocket);

      // Assert
      expect(connectedUsers.get('user1').has('socket123')).toBe(false);
      expect(connectedUsers.get('user1').has('socket456')).toBe(true);
    });

    it('should remove user completely when last socket disconnects', () => {
      // Arrange
      const connectedUsers = new Map();
      connectedUsers.set('user1', new Set(['socket123']));
      (websocketService as any).connectedUsers = connectedUsers;
      (websocketService as any).userRooms = new Map([['user1', new Set(['team:team1'])]]);
      (websocketService as any).io = mockIO;

      // Act
      (websocketService as any).handleDisconnection(mockSocket);

      // Assert
      expect(connectedUsers.has('user1')).toBe(false);
      expect((websocketService as any).userRooms.has('user1')).toBe(false);
    });
  });

  describe('public methods', () => {
    beforeEach(() => {
      (websocketService as any).io = mockIO;
    });

    describe('emitToUser', () => {
      it('should emit event to specific user', () => {
        // Act
        websocketService.emitToUser('user1', 'test:event', { data: 'test' });

        // Assert
        expect(mockIO.to).toHaveBeenCalledWith('user:user1');
        expect(mockIO.to().emit).toHaveBeenCalledWith('test:event', { data: 'test' });
      });
    });

    describe('emitToTeam', () => {
      it('should emit event to team room', () => {
        // Act
        websocketService.emitToTeam('team1', 'team:update', { teamId: 'team1' });

        // Assert
        expect(mockIO.to).toHaveBeenCalledWith('team:team1');
        expect(mockIO.to().emit).toHaveBeenCalledWith('team:update', { teamId: 'team1' });
      });
    });

    describe('emitToProject', () => {
      it('should emit event to project room', () => {
        // Act
        websocketService.emitToProject('project1', 'project:update', { projectId: 'project1' });

        // Assert
        expect(mockIO.to).toHaveBeenCalledWith('project:project1');
        expect(mockIO.to().emit).toHaveBeenCalledWith('project:update', { projectId: 'project1' });
      });
    });

    describe('emitToTask', () => {
      it('should emit event to task room', () => {
        // Act
        websocketService.emitToTask('task1', 'task:update', { taskId: 'task1' });

        // Assert
        expect(mockIO.to).toHaveBeenCalledWith('task:task1');
        expect(mockIO.to().emit).toHaveBeenCalledWith('task:update', { taskId: 'task1' });
      });
    });

    describe('emitToMeeting', () => {
      it('should emit event to meeting room', () => {
        // Act
        websocketService.emitToMeeting('meeting1', 'meeting:update', { meetingId: 'meeting1' });

        // Assert
        expect(mockIO.to).toHaveBeenCalledWith('meeting:meeting1');
        expect(mockIO.to().emit).toHaveBeenCalledWith('meeting:update', { meetingId: 'meeting1' });
      });
    });

    describe('broadcastNotification', () => {
      it('should broadcast notification to user', () => {
        // Arrange
        const notification = { id: 'notif1', message: 'Test notification' };

        // Act
        websocketService.broadcastNotification('user1', notification);

        // Assert
        expect(mockIO.to).toHaveBeenCalledWith('user:user1');
        expect(mockIO.to().emit).toHaveBeenCalledWith('notification:new', notification);
      });
    });

    describe('isUserOnline', () => {
      it('should return true for online user', () => {
        // Arrange
        const connectedUsers = new Map();
        connectedUsers.set('user1', new Set(['socket123']));
        (websocketService as any).connectedUsers = connectedUsers;

        // Act
        const result = websocketService.isUserOnline('user1');

        // Assert
        expect(result).toBe(true);
      });

      it('should return false for offline user', () => {
        // Arrange
        (websocketService as any).connectedUsers = new Map();

        // Act
        const result = websocketService.isUserOnline('user1');

        // Assert
        expect(result).toBe(false);
      });
    });

    describe('getOnlineUsersCount', () => {
      it('should return correct count of online users', () => {
        // Arrange
        const connectedUsers = new Map();
        connectedUsers.set('user1', new Set(['socket123']));
        connectedUsers.set('user2', new Set(['socket456', 'socket789']));
        (websocketService as any).connectedUsers = connectedUsers;

        // Act
        const result = websocketService.getOnlineUsersCount();

        // Assert
        expect(result).toBe(2);
      });
    });

    describe('getConnectedSocketsForUser', () => {
      it('should return socket IDs for user', () => {
        // Arrange
        const connectedUsers = new Map();
        connectedUsers.set('user1', new Set(['socket123', 'socket456']));
        (websocketService as any).connectedUsers = connectedUsers;

        // Act
        const result = websocketService.getConnectedSocketsForUser('user1');

        // Assert
        expect(result).toEqual(['socket123', 'socket456']);
      });

      it('should return empty array for offline user', () => {
        // Arrange
        (websocketService as any).connectedUsers = new Map();

        // Act
        const result = websocketService.getConnectedSocketsForUser('user1');

        // Assert
        expect(result).toEqual([]);
      });
    });
  });
});
