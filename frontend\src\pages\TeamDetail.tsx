import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Grid,
  Card,
  CardContent,
  Button,
  Avatar,
  Chip,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Tabs,
  Tab,
  CircularProgress,
  Alert,
  Breadcrumbs,
  Link,
} from '@mui/material';
import {
  ArrowBack as ArrowBackIcon,
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Settings as SettingsIcon,
  People as PeopleIcon,
  Work as ProjectIcon,
  Analytics as AnalyticsIcon,
} from '@mui/icons-material';
import { useForm, Controller } from 'react-hook-form';
import { teamService, Team, TeamMember, AddMemberData } from '../services/teamService';
import { aiService } from '../services/aiService';
import { useAppSelector } from '../store';
import { useProjectSubscription } from '../hooks/useWebSocket';
import toast from 'react-hot-toast';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`team-tabpanel-${index}`}
      aria-labelledby={`team-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

const TeamDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { user } = useAppSelector((state) => state.auth);

  const [team, setTeam] = useState<Team | null>(null);
  const [loading, setLoading] = useState(true);
  const [tabValue, setTabValue] = useState(0);
  const [addMemberDialogOpen, setAddMemberDialogOpen] = useState(false);
  const [capacityAnalysis, setCapacityAnalysis] = useState<any>(null);
  const [analysisLoading, setAnalysisLoading] = useState(false);

  // Subscribe to project updates for this team
  useProjectSubscription(id || null);

  const {
    control,
    handleSubmit,
    reset,
    formState: { errors, isSubmitting },
  } = useForm<AddMemberData>({
    defaultValues: {
      userId: '',
      role: 'MEMBER',
    },
  });

  useEffect(() => {
    if (id) {
      loadTeamDetails();
    }
  }, [id]);

  const loadTeamDetails = async () => {
    if (!id) return;

    try {
      setLoading(true);
      const response = await teamService.getTeamById(id);
      setTeam(response.data);
    } catch (error: any) {
      toast.error(error.response?.data?.error || 'Failed to load team details');
      if (error.response?.status === 404) {
        navigate('/teams');
      }
    } finally {
      setLoading(false);
    }
  };

  const loadCapacityAnalysis = async () => {
    if (!id) return;

    try {
      setAnalysisLoading(true);
      const response = await aiService.analyzeTeamCapacity(id);
      setCapacityAnalysis(response.data.analysis);
    } catch (error: any) {
      toast.error('Failed to load capacity analysis');
      console.error('Capacity analysis error:', error);
    } finally {
      setAnalysisLoading(false);
    }
  };

  const handleAddMember = async (data: AddMemberData) => {
    if (!id) return;

    try {
      const response = await teamService.addMember(id, data);
      if (team) {
        setTeam({
          ...team,
          members: [...(team.members || []), response.data],
          memberCount: team.memberCount + 1,
        });
      }
      setAddMemberDialogOpen(false);
      reset();
      toast.success('Member added successfully!');
    } catch (error: any) {
      toast.error(error.response?.data?.error || 'Failed to add member');
    }
  };

  const handleRemoveMember = async (userId: string) => {
    if (!id || !team) return;

    try {
      await teamService.removeMember(id, userId);
      setTeam({
        ...team,
        members: team.members?.filter(m => m.userId !== userId) || [],
        memberCount: team.memberCount - 1,
      });
      toast.success('Member removed successfully');
    } catch (error: any) {
      toast.error(error.response?.data?.error || 'Failed to remove member');
    }
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
    if (newValue === 2 && !capacityAnalysis) {
      loadCapacityAnalysis();
    }
  };

  const canManageTeam = () => {
    if (!team || !user) return false;

    // Admin can manage any team
    if (user.role === 'ADMIN') return true;

    // Team lead can manage their team
    const userMembership = team.members?.find(m => m.userId === user.id);
    return userMembership?.role === 'LEAD';
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  if (!team) {
    return (
      <Alert severity="error">
        Team not found or you don't have access to view it.
      </Alert>
    );
  }

  return (
    <Box>
      {/* Header */}
      <Box mb={3}>
        <Breadcrumbs aria-label="breadcrumb" sx={{ mb: 2 }}>
          <Link
            component="button"
            variant="body1"
            onClick={() => navigate('/teams')}
            sx={{ textDecoration: 'none' }}
          >
            Teams
          </Link>
          <Typography color="text.primary">{team.name}</Typography>
        </Breadcrumbs>

        <Box display="flex" justifyContent="space-between" alignItems="flex-start">
          <Box>
            <Typography variant="h4" component="h1" gutterBottom>
              {team.name}
            </Typography>
            {team.description && (
              <Typography variant="body1" color="text.secondary" sx={{ mb: 2 }}>
                {team.description}
              </Typography>
            )}
            <Box display="flex" gap={1}>
              <Chip
                icon={<PeopleIcon />}
                label={`${team.memberCount} members`}
                variant="outlined"
              />
              <Chip
                icon={<ProjectIcon />}
                label={`${team.projectCount} projects`}
                variant="outlined"
              />
            </Box>
          </Box>

          {canManageTeam() && (
            <Box display="flex" gap={1}>
              <Button
                variant="outlined"
                startIcon={<SettingsIcon />}
                onClick={() => navigate(`/teams/${id}/settings`)}
              >
                Settings
              </Button>
              <Button
                variant="contained"
                startIcon={<AddIcon />}
                onClick={() => setAddMemberDialogOpen(true)}
              >
                Add Member
              </Button>
            </Box>
          )}
        </Box>
      </Box>

      {/* Tabs */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
        <Tabs value={tabValue} onChange={handleTabChange} aria-label="team tabs">
          <Tab label="Overview" />
          <Tab label="Members" />
          <Tab label="Analytics" />
          <Tab label="Projects" />
        </Tabs>
      </Box>

      {/* Overview Tab */}
      <TabPanel value={tabValue} index={0}>
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Team Information
                </Typography>
                <Typography variant="body2" color="text.secondary" paragraph>
                  Created: {new Date(team.createdAt).toLocaleDateString()}
                </Typography>
                <Typography variant="body2" color="text.secondary" paragraph>
                  Last Updated: {new Date(team.updatedAt).toLocaleDateString()}
                </Typography>
                {team.description && (
                  <Typography variant="body2" paragraph>
                    {team.description}
                  </Typography>
                )}
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Quick Stats
                </Typography>
                <Box display="flex" flexDirection="column" gap={2}>
                  <Box display="flex" justifyContent="space-between">
                    <Typography variant="body2">Total Members:</Typography>
                    <Typography variant="body2" fontWeight="bold">
                      {team.memberCount}
                    </Typography>
                  </Box>
                  <Box display="flex" justifyContent="space-between">
                    <Typography variant="body2">Active Projects:</Typography>
                    <Typography variant="body2" fontWeight="bold">
                      {team.projectCount}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </TabPanel>

      {/* Members Tab */}
      <TabPanel value={tabValue} index={1}>
        <Card>
          <CardContent>
            <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
              <Typography variant="h6">Team Members</Typography>
              {canManageTeam() && (
                <Button
                  variant="contained"
                  startIcon={<AddIcon />}
                  onClick={() => setAddMemberDialogOpen(true)}
                >
                  Add Member
                </Button>
              )}
            </Box>

            <List>
              {team.members?.map((member) => (
                <ListItem key={member.userId}>
                  <ListItemAvatar>
                    <Avatar src={member.team.avatar}>
                      {member.team.firstName[0]}{member.team.lastName[0]}
                    </Avatar>
                  </ListItemAvatar>
                  <ListItemText
                    primary={`${member.team.firstName} ${member.team.lastName}`}
                    secondary={
                      <Box>
                        <Typography variant="body2" color="text.secondary">
                          {member.team.email}
                        </Typography>
                        <Box display="flex" gap={0.5} mt={0.5}>
                          <Chip label={member.role} size="small" color="primary" />
                          {member.team.skills?.map((skill) => (
                            <Chip key={skill} label={skill} size="small" variant="outlined" />
                          ))}
                        </Box>
                      </Box>
                    }
                  />
                  {canManageTeam() && member.userId !== user?.id && (
                    <ListItemSecondaryAction>
                      <IconButton
                        edge="end"
                        onClick={() => handleRemoveMember(member.userId)}
                        color="error"
                      >
                        <DeleteIcon />
                      </IconButton>
                    </ListItemSecondaryAction>
                  )}
                </ListItem>
              ))}
            </List>
          </CardContent>
        </Card>
      </TabPanel>

      {/* Analytics Tab */}
      <TabPanel value={tabValue} index={2}>
        {analysisLoading ? (
          <Box display="flex" justifyContent="center" p={4}>
            <CircularProgress />
          </Box>
        ) : capacityAnalysis ? (
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Team Utilization
                  </Typography>
                  <Typography variant="h3" color="primary" gutterBottom>
                    {Math.round(capacityAnalysis.overallUtilization)}%
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Overall team capacity utilization
                  </Typography>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Bottlenecks
                  </Typography>
                  <Typography variant="h3" color="warning.main" gutterBottom>
                    {capacityAnalysis.bottlenecks.length}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Team members at high capacity
                  </Typography>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    AI Recommendations
                  </Typography>
                  <List>
                    {capacityAnalysis.recommendations.map((recommendation: string, index: number) => (
                      <ListItem key={index}>
                        <ListItemText primary={recommendation} />
                      </ListItem>
                    ))}
                  </List>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        ) : (
          <Box textAlign="center" p={4}>
            <Button
              variant="contained"
              startIcon={<AnalyticsIcon />}
              onClick={loadCapacityAnalysis}
            >
              Generate Capacity Analysis
            </Button>
          </Box>
        )}
      </TabPanel>

      {/* Projects Tab */}
      <TabPanel value={tabValue} index={3}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Team Projects
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Project management coming soon...
            </Typography>
          </CardContent>
        </Card>
      </TabPanel>

      {/* Add Member Dialog */}
      <Dialog
        open={addMemberDialogOpen}
        onClose={() => setAddMemberDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <form onSubmit={handleSubmit(handleAddMember)}>
          <DialogTitle>Add Team Member</DialogTitle>
          <DialogContent>
            <Controller
              name="userId"
              control={control}
              rules={{ required: 'Please select a user' }}
              render={({ field }) => (
                <TextField
                  {...field}
                  autoFocus
                  margin="dense"
                  label="User Email"
                  fullWidth
                  variant="outlined"
                  error={!!errors.userId}
                  helperText={errors.userId?.message || 'Enter the email of the user to add'}
                  sx={{ mb: 2 }}
                />
              )}
            />
            <Controller
              name="role"
              control={control}
              render={({ field }) => (
                <FormControl fullWidth variant="outlined">
                  <InputLabel>Role</InputLabel>
                  <Select {...field} label="Role">
                    <MenuItem value="MEMBER">Member</MenuItem>
                    <MenuItem value="SENIOR">Senior</MenuItem>
                    <MenuItem value="LEAD">Lead</MenuItem>
                  </Select>
                </FormControl>
              )}
            />
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setAddMemberDialogOpen(false)}>Cancel</Button>
            <Button type="submit" variant="contained" disabled={isSubmitting}>
              {isSubmitting ? 'Adding...' : 'Add Member'}
            </Button>
          </DialogActions>
        </form>
      </Dialog>
    </Box>
  );
};

export default TeamDetail;
