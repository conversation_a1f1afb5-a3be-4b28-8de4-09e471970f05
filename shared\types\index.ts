// User Types
export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  avatar?: string;
  role: UserRole;
  skills: string[];
  timezone: string;
  availability: AvailabilitySchedule;
  createdAt: Date;
  updatedAt: Date;
}

export enum UserRole {
  ADMIN = 'admin',
  MANAGER = 'manager',
  MEMBER = 'member',
  VIEWER = 'viewer'
}

export interface AvailabilitySchedule {
  monday: TimeSlot[];
  tuesday: TimeSlot[];
  wednesday: TimeSlot[];
  thursday: TimeSlot[];
  friday: TimeSlot[];
  saturday: TimeSlot[];
  sunday: TimeSlot[];
}

export interface TimeSlot {
  start: string; // HH:MM format
  end: string;   // HH:MM format
}

// Team Types
export interface Team {
  id: string;
  name: string;
  description: string;
  members: TeamMember[];
  projects: string[]; // Project IDs
  settings: TeamSettings;
  createdAt: Date;
  updatedAt: Date;
}

export interface TeamMember {
  userId: string;
  role: TeamRole;
  joinedAt: Date;
}

export enum TeamRole {
  LEAD = 'lead',
  SENIOR = 'senior',
  MEMBER = 'member'
}

export interface TeamSettings {
  workingHours: TimeSlot;
  timezone: string;
  meetingPreferences: MeetingPreferences;
}

export interface MeetingPreferences {
  maxDailyMeetings: number;
  preferredMeetingLength: number; // minutes
  bufferTime: number; // minutes between meetings
  noMeetingDays: string[]; // day names
}

// Project Types
export interface Project {
  id: string;
  name: string;
  description: string;
  teamId: string;
  status: ProjectStatus;
  priority: Priority;
  startDate: Date;
  endDate?: Date;
  tasks: string[]; // Task IDs
  createdAt: Date;
  updatedAt: Date;
}

export enum ProjectStatus {
  PLANNING = 'planning',
  ACTIVE = 'active',
  ON_HOLD = 'on_hold',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled'
}

export enum Priority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

// Task Types
export interface Task {
  id: string;
  title: string;
  description: string;
  projectId: string;
  assigneeId?: string;
  status: TaskStatus;
  priority: Priority;
  estimatedHours: number;
  actualHours?: number;
  dependencies: string[]; // Task IDs
  tags: string[];
  dueDate?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export enum TaskStatus {
  TODO = 'todo',
  IN_PROGRESS = 'in_progress',
  IN_REVIEW = 'in_review',
  BLOCKED = 'blocked',
  COMPLETED = 'completed'
}

// Meeting Types
export interface Meeting {
  id: string;
  title: string;
  description: string;
  organizer: string; // User ID
  participants: string[]; // User IDs
  startTime: Date;
  endTime: Date;
  location?: string;
  meetingUrl?: string;
  agenda: AgendaItem[];
  necessityScore: number; // 0-100
  status: MeetingStatus;
  createdAt: Date;
  updatedAt: Date;
}

export interface AgendaItem {
  id: string;
  title: string;
  description?: string;
  duration: number; // minutes
  presenter?: string; // User ID
}

export enum MeetingStatus {
  SCHEDULED = 'scheduled',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled'
}

// Analytics Types
export interface TeamAnalytics {
  teamId: string;
  period: AnalyticsPeriod;
  productivity: ProductivityMetrics;
  capacity: CapacityMetrics;
  meetings: MeetingMetrics;
  tasks: TaskMetrics;
}

export interface ProductivityMetrics {
  tasksCompleted: number;
  averageTaskCompletionTime: number; // hours
  velocityTrend: number[]; // tasks per week
  burndownRate: number;
}

export interface CapacityMetrics {
  totalCapacity: number; // hours
  utilizedCapacity: number; // hours
  availableCapacity: number; // hours
  overallocation: number; // hours
}

export interface MeetingMetrics {
  totalMeetings: number;
  totalMeetingTime: number; // hours
  averageMeetingDuration: number; // minutes
  meetingEfficiencyScore: number; // 0-100
}

export interface TaskMetrics {
  totalTasks: number;
  completedTasks: number;
  overdueTasks: number;
  averageTaskDuration: number; // hours
}

export enum AnalyticsPeriod {
  WEEK = 'week',
  MONTH = 'month',
  QUARTER = 'quarter',
  YEAR = 'year'
}

// API Response Types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// WebSocket Types
export interface WebSocketMessage {
  type: WebSocketMessageType;
  payload: any;
  timestamp: Date;
}

export enum WebSocketMessageType {
  TASK_UPDATED = 'task_updated',
  MEETING_SCHEDULED = 'meeting_scheduled',
  TEAM_NOTIFICATION = 'team_notification',
  USER_STATUS_CHANGED = 'user_status_changed',
  PROJECT_UPDATED = 'project_updated'
}

// Notification Types
export interface Notification {
  id: string;
  userId: string;
  type: NotificationType;
  title: string;
  message: string;
  data?: any;
  read: boolean;
  createdAt: Date;
}

export enum NotificationType {
  TASK_ASSIGNED = 'task_assigned',
  TASK_DUE = 'task_due',
  MEETING_REMINDER = 'meeting_reminder',
  DEPENDENCY_COMPLETED = 'dependency_completed',
  TEAM_INVITATION = 'team_invitation'
}
