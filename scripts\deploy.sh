#!/bin/bash

# CollabFlow Deployment Script
# This script handles deployment to different environments

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Default values
ENVIRONMENT="development"
BUILD_IMAGES=true
PUSH_IMAGES=false
REGISTRY=""
TAG="latest"

# Help function
show_help() {
    echo "CollabFlow Deployment Script"
    echo ""
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -e, --environment ENV    Target environment (development|staging|production)"
    echo "  -t, --tag TAG           Docker image tag (default: latest)"
    echo "  -r, --registry URL      Container registry URL"
    echo "  --no-build              Skip building Docker images"
    echo "  --push                  Push images to registry"
    echo "  -h, --help              Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 -e development                    # Deploy to development"
    echo "  $0 -e staging --push -r ghcr.io     # Build, push and deploy to staging"
    echo "  $0 -e production -t v1.0.0 --push   # Deploy specific version to production"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -e|--environment)
            ENVIRONMENT="$2"
            shift 2
            ;;
        -t|--tag)
            TAG="$2"
            shift 2
            ;;
        -r|--registry)
            REGISTRY="$2"
            shift 2
            ;;
        --no-build)
            BUILD_IMAGES=false
            shift
            ;;
        --push)
            PUSH_IMAGES=true
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Validate environment
if [[ ! "$ENVIRONMENT" =~ ^(development|staging|production)$ ]]; then
    print_error "Invalid environment: $ENVIRONMENT"
    print_error "Valid environments: development, staging, production"
    exit 1
fi

# Set image names
if [ -n "$REGISTRY" ]; then
    BACKEND_IMAGE="$REGISTRY/collabflow-backend:$TAG"
    FRONTEND_IMAGE="$REGISTRY/collabflow-frontend:$TAG"
else
    BACKEND_IMAGE="collabflow-backend:$TAG"
    FRONTEND_IMAGE="collabflow-frontend:$TAG"
fi

# Build Docker images
build_images() {
    if [ "$BUILD_IMAGES" = true ]; then
        print_status "Building Docker images..."
        
        # Build backend image
        print_status "Building backend image: $BACKEND_IMAGE"
        docker build -t "$BACKEND_IMAGE" ./backend
        
        # Build frontend image
        print_status "Building frontend image: $FRONTEND_IMAGE"
        docker build -t "$FRONTEND_IMAGE" ./frontend
        
        print_success "Docker images built successfully!"
    else
        print_status "Skipping image build..."
    fi
}

# Push Docker images
push_images() {
    if [ "$PUSH_IMAGES" = true ]; then
        if [ -z "$REGISTRY" ]; then
            print_error "Registry URL is required for pushing images"
            exit 1
        fi
        
        print_status "Pushing Docker images to registry..."
        
        # Push backend image
        print_status "Pushing backend image: $BACKEND_IMAGE"
        docker push "$BACKEND_IMAGE"
        
        # Push frontend image
        print_status "Pushing frontend image: $FRONTEND_IMAGE"
        docker push "$FRONTEND_IMAGE"
        
        print_success "Docker images pushed successfully!"
    fi
}

# Deploy to development
deploy_development() {
    print_status "Deploying to development environment..."
    
    # Stop existing containers
    docker-compose down 2>/dev/null || true
    
    # Start services
    docker-compose up -d
    
    # Wait for services to be ready
    print_status "Waiting for services to be ready..."
    sleep 10
    
    # Check health
    check_health "http://localhost:3001/api/health" "Backend"
    check_health "http://localhost:3000" "Frontend"
    
    print_success "Development deployment completed!"
    print_status "Access the application at: http://localhost:3000"
}

# Deploy to staging
deploy_staging() {
    print_status "Deploying to staging environment..."
    
    # Use production compose file with staging overrides
    docker-compose -f docker-compose.prod.yml down 2>/dev/null || true
    
    # Set staging environment variables
    export POSTGRES_PASSWORD="${STAGING_POSTGRES_PASSWORD:-staging_password}"
    export JWT_SECRET="${STAGING_JWT_SECRET:-staging_jwt_secret}"
    export OPENAI_API_KEY="${STAGING_OPENAI_API_KEY}"
    
    # Start services
    docker-compose -f docker-compose.prod.yml up -d
    
    # Wait for services to be ready
    print_status "Waiting for services to be ready..."
    sleep 30
    
    print_success "Staging deployment completed!"
}

# Deploy to production
deploy_production() {
    print_status "Deploying to production environment..."
    
    # Validate required environment variables
    if [ -z "$PROD_POSTGRES_PASSWORD" ] || [ -z "$PROD_JWT_SECRET" ]; then
        print_error "Production environment variables are required:"
        print_error "- PROD_POSTGRES_PASSWORD"
        print_error "- PROD_JWT_SECRET"
        exit 1
    fi
    
    # Backup database (if applicable)
    backup_database
    
    # Use production compose file
    docker-compose -f docker-compose.prod.yml down 2>/dev/null || true
    
    # Set production environment variables
    export POSTGRES_PASSWORD="$PROD_POSTGRES_PASSWORD"
    export JWT_SECRET="$PROD_JWT_SECRET"
    export OPENAI_API_KEY="$PROD_OPENAI_API_KEY"
    
    # Start services
    docker-compose -f docker-compose.prod.yml up -d
    
    # Wait for services to be ready
    print_status "Waiting for services to be ready..."
    sleep 30
    
    # Run health checks
    check_health "http://localhost:3001/api/health" "Backend"
    check_health "http://localhost" "Frontend"
    
    print_success "Production deployment completed!"
}

# Check service health
check_health() {
    local url=$1
    local service=$2
    local max_attempts=30
    local attempt=1
    
    print_status "Checking $service health..."
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f -s "$url" > /dev/null 2>&1; then
            print_success "$service is healthy!"
            return 0
        fi
        
        print_status "Attempt $attempt/$max_attempts: $service not ready yet..."
        sleep 2
        ((attempt++))
    done
    
    print_error "$service health check failed after $max_attempts attempts"
    return 1
}

# Backup database
backup_database() {
    if [ "$ENVIRONMENT" = "production" ]; then
        print_status "Creating database backup..."
        
        # Create backup directory
        mkdir -p backups
        
        # Generate backup filename with timestamp
        BACKUP_FILE="backups/collabflow_backup_$(date +%Y%m%d_%H%M%S).sql"
        
        # Create database backup (adjust connection details as needed)
        if command -v pg_dump &> /dev/null; then
            pg_dump -h localhost -U collabflow_user collabflow > "$BACKUP_FILE"
            print_success "Database backup created: $BACKUP_FILE"
        else
            print_warning "pg_dump not found. Skipping database backup."
        fi
    fi
}

# Cleanup old images
cleanup_images() {
    print_status "Cleaning up old Docker images..."
    
    # Remove dangling images
    docker image prune -f
    
    # Remove old versions (keep last 3)
    if [ -n "$REGISTRY" ]; then
        # This would require additional logic to identify old images
        print_status "Registry cleanup would be implemented here"
    fi
    
    print_success "Image cleanup completed!"
}

# Main deployment function
main() {
    echo "🚀 CollabFlow Deployment"
    echo "Environment: $ENVIRONMENT"
    echo "Tag: $TAG"
    echo ""
    
    # Build images
    build_images
    
    # Push images
    push_images
    
    # Deploy based on environment
    case $ENVIRONMENT in
        development)
            deploy_development
            ;;
        staging)
            deploy_staging
            ;;
        production)
            deploy_production
            ;;
    esac
    
    # Cleanup
    cleanup_images
    
    echo ""
    print_success "🎉 Deployment to $ENVIRONMENT completed successfully!"
    
    if [ "$ENVIRONMENT" = "development" ]; then
        echo ""
        echo "📱 Application URLs:"
        echo "   Frontend: http://localhost:3000"
        echo "   Backend API: http://localhost:3001/api"
        echo "   API Health: http://localhost:3001/api/health"
        echo ""
        echo "🔑 Default admin credentials:"
        echo "   Email: <EMAIL>"
        echo "   Password: admin123"
    fi
}

# Run main function
main "$@"
