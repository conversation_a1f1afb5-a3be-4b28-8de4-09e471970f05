import { jest } from '@jest/globals';
import app from '../../app';
import { TestHelper } from '../helpers/testHelper';
import { prisma } from '../../config/database';
import { aiService } from '../../services/aiService';

const mockPrisma = prisma as jest.Mocked<typeof prisma>;
const testHelper = new TestHelper(app.app);

// Mock AI service
jest.mock('../../services/aiService', () => ({
  aiService: {
    analyzeTaskPriority: jest.fn(),
  },
}));

const mockAiService = aiService as jest.Mocked<typeof aiService>;

describe('Tasks API Integration Tests', () => {
  let mockUser: any;
  let mockTeam: any;
  let mockProject: any;
  let mockTask: any;

  beforeEach(async () => {
    await testHelper.cleanupDatabase();
    jest.clearAllMocks();

    mockUser = testHelper.createMockUser();
    mockTeam = testHelper.createMockTeam();
    mockProject = testHelper.createMockProject();
    mockTask = testHelper.createMockTask();
  });

  afterAll(async () => {
    await testHelper.cleanupDatabase();
  });

  describe('GET /api/tasks', () => {
    it('should return user tasks successfully', async () => {
      // Arrange
      const mockTasks = [
        mockTask,
        testHelper.createMockTask({ id: 'task2', title: 'Task 2' }),
      ];

      mockPrisma.task.findMany.mockResolvedValue(mockTasks as any);
      mockPrisma.task.count.mockResolvedValue(2);

      // Act
      const response = await testHelper.authenticatedRequest(mockUser)
        .get('/api/tasks');

      // Assert
      testHelper.expectSuccessResponse(response);
      expect(Array.isArray(response.body.data)).toBe(true);
      expect(response.body.data).toHaveLength(2);
      expect(response.body.data[0]).toMatchObject({
        id: mockTask.id,
        title: mockTask.title,
      });
    });

    it('should support filtering by status', async () => {
      // Arrange
      const todoTasks = [testHelper.createMockTask({ status: 'TODO' })];
      mockPrisma.task.findMany.mockResolvedValue(todoTasks as any);
      mockPrisma.task.count.mockResolvedValue(1);

      // Act
      const response = await testHelper.authenticatedRequest(mockUser)
        .get('/api/tasks')
        .query({ status: 'TODO' });

      // Assert
      testHelper.expectSuccessResponse(response);
      expect(mockPrisma.task.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            status: 'TODO',
          }),
        })
      );
    });

    it('should support filtering by priority', async () => {
      // Arrange
      const highPriorityTasks = [testHelper.createMockTask({ priority: 'HIGH' })];
      mockPrisma.task.findMany.mockResolvedValue(highPriorityTasks as any);
      mockPrisma.task.count.mockResolvedValue(1);

      // Act
      const response = await testHelper.authenticatedRequest(mockUser)
        .get('/api/tasks')
        .query({ priority: 'HIGH' });

      // Assert
      testHelper.expectSuccessResponse(response);
      expect(mockPrisma.task.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            priority: 'HIGH',
          }),
        })
      );
    });

    it('should support filtering by assignee', async () => {
      // Arrange
      const userTasks = [testHelper.createMockTask({ assigneeId: mockUser.id })];
      mockPrisma.task.findMany.mockResolvedValue(userTasks as any);
      mockPrisma.task.count.mockResolvedValue(1);

      // Act
      const response = await testHelper.authenticatedRequest(mockUser)
        .get('/api/tasks')
        .query({ assigneeId: mockUser.id });

      // Assert
      testHelper.expectSuccessResponse(response);
      expect(mockPrisma.task.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            assigneeId: mockUser.id,
          }),
        })
      );
    });

    it('should support pagination', async () => {
      // Arrange
      const mockTasks = Array.from({ length: 3 }, (_, i) => 
        testHelper.createMockTask({ id: `task${i}`, title: `Task ${i}` })
      );
      mockPrisma.task.findMany.mockResolvedValue(mockTasks.slice(0, 2) as any);
      mockPrisma.task.count.mockResolvedValue(5);

      // Act
      const response = await testHelper.authenticatedRequest(mockUser)
        .get('/api/tasks')
        .query({ page: 1, limit: 2 });

      // Assert
      testHelper.expectPaginatedResponse(response);
      expect(response.body.data).toHaveLength(2);
      expect(response.body.meta.total).toBe(5);
    });

    it('should require authentication', async () => {
      // Act
      const response = await testHelper.unauthenticatedRequest()
        .get('/api/tasks');

      // Assert
      testHelper.expectAuthenticationError(response);
    });
  });

  describe('POST /api/tasks', () => {
    it('should create task successfully with AI analysis', async () => {
      // Arrange
      const taskData = {
        title: 'New Task',
        description: 'A new task for testing',
        projectId: mockProject.id,
        assigneeId: mockUser.id,
        priority: 'MEDIUM',
        estimatedHours: 8,
        tags: ['testing'],
        dueDate: testHelper.futureDate(7).toISOString(),
      };

      const mockProjectWithTeam = {
        ...mockProject,
        team: {
          ...mockTeam,
          members: [{ userId: mockUser.id }],
        },
      };

      const createdTask = testHelper.createMockTask({
        ...taskData,
        id: 'new-task-id',
      });

      // Mock AI analysis
      mockAiService.analyzeTaskPriority.mockResolvedValue({
        priority: 'HIGH',
        reasoning: 'AI determined this is high priority',
        estimatedHours: 12,
        dependencies: [],
      });

      mockPrisma.project.findUnique.mockResolvedValue(mockProjectWithTeam as any);
      mockPrisma.task.create.mockResolvedValue(createdTask as any);

      // Act
      const response = await testHelper.authenticatedRequest(mockUser)
        .post('/api/tasks')
        .send(taskData);

      // Assert
      expect(response.status).toBe(201);
      testHelper.expectSuccessResponse(response);
      expect(response.body.data).toMatchObject({
        title: taskData.title,
        description: taskData.description,
      });
      expect(mockAiService.analyzeTaskPriority).toHaveBeenCalledWith(
        taskData.title,
        taskData.description,
        expect.any(Date),
        undefined,
        expect.stringContaining(mockProject.name)
      );
    });

    it('should create task without AI analysis when it fails', async () => {
      // Arrange
      const taskData = {
        title: 'New Task',
        description: 'A new task for testing',
        projectId: mockProject.id,
      };

      const mockProjectWithTeam = {
        ...mockProject,
        team: {
          ...mockTeam,
          members: [{ userId: mockUser.id }],
        },
      };

      const createdTask = testHelper.createMockTask({
        ...taskData,
        id: 'new-task-id',
        priority: 'MEDIUM', // Default fallback
        estimatedHours: 8, // Default fallback
      });

      // Mock AI analysis failure
      mockAiService.analyzeTaskPriority.mockRejectedValue(new Error('AI service unavailable'));

      mockPrisma.project.findUnique.mockResolvedValue(mockProjectWithTeam as any);
      mockPrisma.task.create.mockResolvedValue(createdTask as any);

      // Act
      const response = await testHelper.authenticatedRequest(mockUser)
        .post('/api/tasks')
        .send(taskData);

      // Assert
      expect(response.status).toBe(201);
      testHelper.expectSuccessResponse(response);
      expect(response.body.data.priority).toBe('MEDIUM');
      expect(response.body.data.estimatedHours).toBe(8);
    });

    it('should validate required fields', async () => {
      // Act
      const response = await testHelper.authenticatedRequest(mockUser)
        .post('/api/tasks')
        .send({});

      // Assert
      testHelper.expectValidationError(response);
      expect(response.body.details.some((d: any) => d.path === 'title')).toBe(true);
      expect(response.body.details.some((d: any) => d.path === 'projectId')).toBe(true);
    });

    it('should validate title length', async () => {
      // Act
      const response = await testHelper.authenticatedRequest(mockUser)
        .post('/api/tasks')
        .send({
          title: 'AB', // Too short
          projectId: mockProject.id,
        });

      // Assert
      testHelper.expectValidationError(response, 'title');
    });

    it('should validate project access', async () => {
      // Arrange
      mockPrisma.project.findUnique.mockResolvedValue(null);

      // Act
      const response = await testHelper.authenticatedRequest(mockUser)
        .post('/api/tasks')
        .send({
          title: 'Valid Task Title',
          projectId: 'inaccessible-project-id',
        });

      // Assert
      testHelper.expectNotFoundError(response);
    });

    it('should validate estimated hours range', async () => {
      // Act
      const response = await testHelper.authenticatedRequest(mockUser)
        .post('/api/tasks')
        .send({
          title: 'Valid Task Title',
          projectId: mockProject.id,
          estimatedHours: 0.25, // Too low
        });

      // Assert
      testHelper.expectValidationError(response, 'estimatedHours');
    });
  });

  describe('GET /api/tasks/:id', () => {
    it('should return task details successfully', async () => {
      // Arrange
      const taskWithDetails = {
        ...mockTask,
        project: {
          ...mockProject,
          team: mockTeam,
        },
        assignee: mockUser,
        dependencies: [],
        dependents: [],
      };

      mockPrisma.task.findFirst.mockResolvedValue(taskWithDetails as any);

      // Act
      const response = await testHelper.authenticatedRequest(mockUser)
        .get(`/api/tasks/${mockTask.id}`);

      // Assert
      testHelper.expectSuccessResponse(response);
      expect(response.body.data).toMatchObject({
        id: mockTask.id,
        title: mockTask.title,
      });
      expect(response.body.data).toHaveProperty('project');
      expect(response.body.data).toHaveProperty('assignee');
    });

    it('should return 404 for non-existent task', async () => {
      // Arrange
      mockPrisma.task.findFirst.mockResolvedValue(null);

      // Act
      const response = await testHelper.authenticatedRequest(mockUser)
        .get('/api/tasks/nonexistent-id');

      // Assert
      testHelper.expectNotFoundError(response);
    });

    it('should validate task ID format', async () => {
      // Act
      const response = await testHelper.authenticatedRequest(mockUser)
        .get('/api/tasks/invalid-id');

      // Assert
      testHelper.expectValidationError(response, 'id');
    });
  });

  describe('PUT /api/tasks/:id', () => {
    it('should update task successfully', async () => {
      // Arrange
      const updateData = {
        title: 'Updated Task Title',
        description: 'Updated description',
        status: 'IN_PROGRESS' as const,
        priority: 'HIGH' as const,
      };

      const existingTask = {
        ...mockTask,
        project: {
          ...mockProject,
          team: {
            ...mockTeam,
            members: [{ userId: mockUser.id }],
          },
        },
      };

      const updatedTask = { ...existingTask, ...updateData };

      mockPrisma.task.findFirst.mockResolvedValue(existingTask as any);
      mockPrisma.task.update.mockResolvedValue(updatedTask as any);

      // Act
      const response = await testHelper.authenticatedRequest(mockUser)
        .put(`/api/tasks/${mockTask.id}`)
        .send(updateData);

      // Assert
      testHelper.expectSuccessResponse(response);
      expect(response.body.data).toMatchObject(updateData);
      expect(mockPrisma.task.update).toHaveBeenCalledWith({
        where: { id: mockTask.id },
        data: expect.objectContaining(updateData),
        include: expect.any(Object),
      });
    });

    it('should return 404 for non-existent task', async () => {
      // Arrange
      mockPrisma.task.findFirst.mockResolvedValue(null);

      // Act
      const response = await testHelper.authenticatedRequest(mockUser)
        .put(`/api/tasks/${mockTask.id}`)
        .send({ title: 'Updated Title' });

      // Assert
      testHelper.expectNotFoundError(response);
    });

    it('should validate update data', async () => {
      // Arrange
      const existingTask = {
        ...mockTask,
        project: {
          ...mockProject,
          team: {
            ...mockTeam,
            members: [{ userId: mockUser.id }],
          },
        },
      };

      mockPrisma.task.findFirst.mockResolvedValue(existingTask as any);

      // Act
      const response = await testHelper.authenticatedRequest(mockUser)
        .put(`/api/tasks/${mockTask.id}`)
        .send({
          title: '', // Invalid: empty title
          estimatedHours: -1, // Invalid: negative hours
        });

      // Assert
      testHelper.expectValidationError(response);
    });
  });

  describe('DELETE /api/tasks/:id', () => {
    it('should delete task successfully as team lead', async () => {
      // Arrange
      const taskWithAccess = {
        ...mockTask,
        project: {
          ...mockProject,
          team: {
            ...mockTeam,
            members: [{ userId: mockUser.id, role: 'LEAD' }],
          },
        },
      };

      mockPrisma.task.findFirst.mockResolvedValue(taskWithAccess as any);
      mockPrisma.taskDependency.deleteMany.mockResolvedValue({ count: 0 });
      mockPrisma.task.delete.mockResolvedValue(taskWithAccess as any);

      // Act
      const response = await testHelper.authenticatedRequest(mockUser)
        .delete(`/api/tasks/${mockTask.id}`);

      // Assert
      testHelper.expectSuccessResponse(response);
      expect(mockPrisma.taskDependency.deleteMany).toHaveBeenCalledWith({
        where: {
          OR: [
            { taskId: mockTask.id },
            { dependencyId: mockTask.id },
          ],
        },
      });
      expect(mockPrisma.task.delete).toHaveBeenCalledWith({
        where: { id: mockTask.id },
      });
    });

    it('should allow admin to delete any task', async () => {
      // Arrange
      const mockAdmin = testHelper.createMockAdmin();
      
      mockPrisma.task.findFirst.mockResolvedValue(null); // No access through team membership
      mockPrisma.taskDependency.deleteMany.mockResolvedValue({ count: 0 });
      mockPrisma.task.delete.mockResolvedValue(mockTask as any);

      // Act
      const response = await testHelper.authenticatedRequest(mockAdmin)
        .delete(`/api/tasks/${mockTask.id}`);

      // Assert
      testHelper.expectSuccessResponse(response);
    });

    it('should reject deletion from non-lead member', async () => {
      // Arrange
      const taskWithoutDeleteAccess = {
        ...mockTask,
        project: {
          ...mockProject,
          team: {
            ...mockTeam,
            members: [{ userId: mockUser.id, role: 'MEMBER' }],
          },
        },
      };

      mockPrisma.task.findFirst.mockResolvedValue(taskWithoutDeleteAccess as any);

      // Act
      const response = await testHelper.authenticatedRequest(mockUser)
        .delete(`/api/tasks/${mockTask.id}`);

      // Assert
      testHelper.expectAuthorizationError(response);
    });
  });
});
