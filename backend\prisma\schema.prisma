// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

enum UserRole {
  ADMIN
  MANAGER
  MEMBER
  VIEWER
}

enum TeamRole {
  LEAD
  SENIOR
  MEMBER
}

enum ProjectStatus {
  PLANNING
  ACTIVE
  ON_HOLD
  COMPLETED
  CANCELLED
}

enum TaskStatus {
  TODO
  IN_PROGRESS
  IN_REVIEW
  BLOCKED
  COMPLETED
}

enum Priority {
  LOW
  MEDIUM
  HIGH
  CRITICAL
}

enum MeetingStatus {
  SCHEDULED
  IN_PROGRESS
  COMPLETED
  CANCELLED
}

enum NotificationType {
  TASK_ASSIGNED
  TASK_DUE
  MEETING_REMINDER
  DEPENDENCY_COMPLETED
  TEAM_INVITATION
}

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  password  String
  firstName String
  lastName  String
  avatar    String?
  role      UserRole @default(MEMBER)
  skills    String[]
  timezone  String   @default("UTC")
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relationships
  teamMemberships    TeamMember[]
  assignedTasks      Task[]
  createdProjects    Project[]
  organizedMeetings  Meeting[]      @relation("MeetingOrganizer")
  meetingParticipants MeetingParticipant[]
  notifications      Notification[]
  availability       UserAvailability[]

  @@map("users")
}

model UserAvailability {
  id     String @id @default(cuid())
  userId String
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  dayOfWeek Int // 0 = Sunday, 1 = Monday, etc.
  startTime String // HH:MM format
  endTime   String // HH:MM format

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([userId, dayOfWeek, startTime, endTime])
  @@map("user_availability")
}

model Team {
  id          String   @id @default(cuid())
  name        String
  description String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relationships
  members  TeamMember[]
  projects Project[]
  settings TeamSettings?

  @@map("teams")
}

model TeamMember {
  id     String   @id @default(cuid())
  teamId String
  userId String
  role   TeamRole @default(MEMBER)

  team User @relation(fields: [userId], references: [id], onDelete: Cascade)
  user Team @relation(fields: [teamId], references: [id], onDelete: Cascade)

  joinedAt  DateTime @default(now())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([teamId, userId])
  @@map("team_members")
}

model TeamSettings {
  id     String @id @default(cuid())
  teamId String @unique
  team   Team   @relation(fields: [teamId], references: [id], onDelete: Cascade)

  workingHoursStart String @default("09:00") // HH:MM format
  workingHoursEnd   String @default("17:00") // HH:MM format
  timezone          String @default("UTC")

  maxDailyMeetings        Int @default(4)
  preferredMeetingLength  Int @default(30) // minutes
  bufferTime              Int @default(15) // minutes between meetings
  noMeetingDays           String[] // day names

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("team_settings")
}

model Project {
  id          String        @id @default(cuid())
  name        String
  description String?
  teamId      String
  creatorId   String
  status      ProjectStatus @default(PLANNING)
  priority    Priority      @default(MEDIUM)
  startDate   DateTime
  endDate     DateTime?

  team    Team @relation(fields: [teamId], references: [id], onDelete: Cascade)
  creator User @relation(fields: [creatorId], references: [id])

  // Relationships
  tasks Task[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("projects")
}

model Task {
  id             String     @id @default(cuid())
  title          String
  description    String?
  projectId      String
  assigneeId     String?
  status         TaskStatus @default(TODO)
  priority       Priority   @default(MEDIUM)
  estimatedHours Float      @default(0)
  actualHours    Float?
  tags           String[]
  dueDate        DateTime?

  project  Project @relation(fields: [projectId], references: [id], onDelete: Cascade)
  assignee User?   @relation(fields: [assigneeId], references: [id])

  // Self-referencing for dependencies
  dependencies TaskDependency[] @relation("TaskDependencies")
  dependents   TaskDependency[] @relation("DependentTasks")

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("tasks")
}

model TaskDependency {
  id           String @id @default(cuid())
  taskId       String
  dependencyId String

  task       Task @relation("TaskDependencies", fields: [taskId], references: [id], onDelete: Cascade)
  dependency Task @relation("DependentTasks", fields: [dependencyId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now())

  @@unique([taskId, dependencyId])
  @@map("task_dependencies")
}

model Meeting {
  id             String        @id @default(cuid())
  title          String
  description    String?
  organizerId    String
  startTime      DateTime
  endTime        DateTime
  location       String?
  meetingUrl     String?
  necessityScore Int           @default(50) // 0-100
  status         MeetingStatus @default(SCHEDULED)

  organizer User @relation("MeetingOrganizer", fields: [organizerId], references: [id])

  // Relationships
  participants MeetingParticipant[]
  agenda       AgendaItem[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("meetings")
}

model MeetingParticipant {
  id        String @id @default(cuid())
  meetingId String
  userId    String

  meeting Meeting @relation(fields: [meetingId], references: [id], onDelete: Cascade)
  user    User    @relation(fields: [userId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now())

  @@unique([meetingId, userId])
  @@map("meeting_participants")
}

model AgendaItem {
  id          String  @id @default(cuid())
  meetingId   String
  title       String
  description String?
  duration    Int     // minutes
  presenterId String?

  meeting Meeting @relation(fields: [meetingId], references: [id], onDelete: Cascade)

  order     Int
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("agenda_items")
}

model Notification {
  id      String           @id @default(cuid())
  userId  String
  type    NotificationType
  title   String
  message String
  data    Json?
  read    Boolean          @default(false)

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("notifications")
}
