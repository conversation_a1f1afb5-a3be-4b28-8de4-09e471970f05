import OpenAI from 'openai';
import { logger } from '../../../config/logger';
import {
  <PERSON>Provider,
  AIProviderConfig,
  ChatCompletionRequest,
  ChatCompletionResponse,
  MeetingNecessityAnalysis,
  TaskPriorityAnalysis,
  TeamCapacityAnalysis,
  ConflictResolution,
} from '../types';

export class OpenAIProvider extends AIProvider {
  private client: OpenAI;
  private defaultModel: string;

  constructor(config: AIProviderConfig) {
    super(config, 'openai');
    
    if (!config.apiKey) {
      throw new Error('OpenAI API key is required');
    }

    this.defaultModel = config.model || 'gpt-3.5-turbo';
    this.client = new OpenAI({
      apiKey: config.apiKey,
      timeout: config.timeout || 30000,
    });
  }

  async isAvailable(): Promise<boolean> {
    try {
      // Try to list models to check if API key is valid
      await this.client.models.list();
      return true;
    } catch (error) {
      logger.warn('OpenAI is not available:', error instanceof Error ? error.message : 'Unknown error');
      return false;
    }
  }

  async healthCheck(): Promise<boolean> {
    try {
      // Make a simple completion request to verify everything works
      const response = await this.client.chat.completions.create({
        model: this.defaultModel,
        messages: [{ role: 'user', content: 'Hello' }],
        max_tokens: 5,
      });
      
      return response.choices.length > 0;
    } catch (error) {
      logger.error('OpenAI health check failed:', error);
      return false;
    }
  }

  async chatCompletion(request: ChatCompletionRequest): Promise<ChatCompletionResponse> {
    try {
      const response = await this.client.chat.completions.create({
        model: request.model || this.defaultModel,
        messages: request.messages,
        temperature: request.temperature,
        max_tokens: request.maxTokens,
        stream: request.stream || false,
      });

      return {
        choices: response.choices.map(choice => ({
          message: {
            content: choice.message.content || '',
            role: choice.message.role,
          },
          finishReason: choice.finish_reason || undefined,
        })),
        usage: response.usage ? {
          promptTokens: response.usage.prompt_tokens,
          completionTokens: response.usage.completion_tokens,
          totalTokens: response.usage.total_tokens,
        } : undefined,
      };
    } catch (error) {
      logger.error('OpenAI chat completion failed:', error);
      throw new Error(`OpenAI chat completion failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async analyzeMeetingNecessity(
    title: string,
    description: string,
    participants: string[],
    duration: number,
    context?: string
  ): Promise<MeetingNecessityAnalysis> {
    const prompt = `
Analyze the necessity of this meeting and provide a score from 0-100:

Meeting Details:
- Title: ${title}
- Description: ${description}
- Participants: ${participants.length} people (${participants.join(', ')})
- Duration: ${duration} minutes
- Context: ${context || 'None provided'}

Please respond with a JSON object containing:
- score: number (0-100, where 100 is absolutely necessary)
- reasoning: string explaining the score
- alternatives: array of strings suggesting alternatives if score is low
- recommendations: array of strings with actionable advice

Consider factors like:
- Number of participants vs decision complexity
- Whether this could be handled asynchronously
- Time investment vs expected outcomes
- Urgency and importance of the topic

Respond only with valid JSON.`;

    try {
      const response = await this.chatCompletion({
        messages: [
          {
            role: 'system',
            content: 'You are an expert in team productivity and meeting optimization. Provide practical, actionable insights in valid JSON format.',
          },
          {
            role: 'user',
            content: prompt,
          },
        ],
        temperature: 0.3,
        maxTokens: 500,
      });

      const content = response.choices[0]?.message?.content;
      if (!content) {
        throw new Error('No response from AI service');
      }

      const analysis = JSON.parse(content);
      
      // Validate response structure
      if (typeof analysis.score !== 'number' || analysis.score < 0 || analysis.score > 100) {
        throw new Error('Invalid score in AI response');
      }

      return analysis;
    } catch (error) {
      logger.error('OpenAI meeting analysis failed:', error);
      throw error;
    }
  }

  async analyzeTaskPriority(
    title: string,
    description: string,
    dueDate?: Date,
    dependencies?: string[],
    projectContext?: string
  ): Promise<TaskPriorityAnalysis> {
    const dueDateStr = dueDate ? dueDate.toISOString().split('T')[0] : 'No due date';
    const dependenciesStr = dependencies?.length ? dependencies.join(', ') : 'None';

    const prompt = `
Analyze this task and determine its priority level:

Task Details:
- Title: ${title}
- Description: ${description}
- Due Date: ${dueDateStr}
- Dependencies: ${dependenciesStr}
- Project Context: ${projectContext || 'None provided'}

Please respond with a JSON object containing:
- priority: one of "LOW", "MEDIUM", "HIGH", "CRITICAL"
- reasoning: string explaining the priority assignment
- estimatedHours: number (estimated hours to complete)
- dependencies: array of strings listing key dependencies

Consider factors like:
- Business impact and urgency
- Complexity and effort required
- Dependencies and blockers
- Due date proximity
- Strategic importance

Respond only with valid JSON.`;

    try {
      const response = await this.chatCompletion({
        messages: [
          {
            role: 'system',
            content: 'You are an expert project manager. Analyze tasks objectively based on impact, urgency, and complexity. Respond in valid JSON format.',
          },
          {
            role: 'user',
            content: prompt,
          },
        ],
        temperature: 0.2,
        maxTokens: 400,
      });

      const content = response.choices[0]?.message?.content;
      if (!content) {
        throw new Error('No response from AI service');
      }

      return JSON.parse(content);
    } catch (error) {
      logger.error('OpenAI task analysis failed:', error);
      throw error;
    }
  }

  async analyzeTeamCapacity(
    teamMembers: Array<{
      id: string;
      name: string;
      skills: string[];
      currentTasks: number;
      availability: number;
    }>,
    upcomingTasks: Array<{
      title: string;
      estimatedHours: number;
      requiredSkills: string[];
    }>
  ): Promise<TeamCapacityAnalysis> {
    const prompt = `
Analyze team capacity and workload distribution:

Team Members:
${teamMembers.map(member => `
- ${member.name} (ID: ${member.id})
  - Skills: ${member.skills.join(', ')}
  - Current Tasks: ${member.currentTasks}
  - Availability: ${member.availability} hours/week
`).join('')}

Upcoming Tasks:
${upcomingTasks.map(task => `
- ${task.title}
  - Estimated Hours: ${task.estimatedHours}
  - Required Skills: ${task.requiredSkills.join(', ')}
`).join('')}

Please respond with a JSON object containing:
- overallUtilization: number (0-100 representing team utilization percentage)
- bottlenecks: array of objects with userId, utilization, and skills
- recommendations: array of strings with actionable advice

Respond only with valid JSON.`;

    try {
      const response = await this.chatCompletion({
        messages: [
          {
            role: 'system',
            content: 'You are an expert in team management and resource allocation. Analyze team capacity objectively and provide actionable insights in valid JSON format.',
          },
          {
            role: 'user',
            content: prompt,
          },
        ],
        temperature: 0.3,
        maxTokens: 600,
      });

      const content = response.choices[0]?.message?.content;
      if (!content) {
        throw new Error('No response from AI service');
      }

      return JSON.parse(content);
    } catch (error) {
      logger.error('OpenAI team capacity analysis failed:', error);
      throw error;
    }
  }

  async detectConflicts(
    teamId: string,
    startDate: Date,
    endDate: Date,
    meetings: any[],
    tasks: any[]
  ): Promise<ConflictResolution> {
    const prompt = `
Analyze potential conflicts in team schedule and resources:

Time Period: ${startDate.toISOString()} to ${endDate.toISOString()}
Team ID: ${teamId}

Meetings:
${meetings.map(meeting => `
- ${meeting.title}
  - Start: ${meeting.startTime}
  - End: ${meeting.endTime}
  - Participants: ${meeting.participants?.length || 0}
`).join('')}

Tasks:
${tasks.map(task => `
- ${task.title}
  - Due: ${task.dueDate || 'No due date'}
  - Assignee: ${task.assigneeId || 'Unassigned'}
  - Priority: ${task.priority || 'Unknown'}
`).join('')}

Please respond with a JSON object containing:
- conflicts: array of objects with type, description, and severity
- resolutions: array of objects with conflictType, solution, and impact

Types can be: 'schedule', 'resource', 'priority'
Severity can be: 'low', 'medium', 'high'

Respond only with valid JSON.`;

    try {
      const response = await this.chatCompletion({
        messages: [
          {
            role: 'system',
            content: 'You are an expert in project management and conflict resolution. Identify scheduling and resource conflicts and provide practical solutions in valid JSON format.',
          },
          {
            role: 'user',
            content: prompt,
          },
        ],
        temperature: 0.3,
        maxTokens: 600,
      });

      const content = response.choices[0]?.message?.content;
      if (!content) {
        throw new Error('No response from AI service');
      }

      return JSON.parse(content);
    } catch (error) {
      logger.error('OpenAI conflict detection failed:', error);
      throw error;
    }
  }
}
