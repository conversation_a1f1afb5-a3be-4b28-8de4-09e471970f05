import React from 'react';
import { screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { jest } from '@jest/globals';

import Tasks from '../../pages/Tasks';
import { taskService } from '../../services/taskService';
import { teamService } from '../../services/teamService';
import { aiService } from '../../services/aiService';
import {
  renderWithProviders,
  createAuthenticatedState,
  createMockTask,
  createMockUser,
  createMockTeam,
  mockApiResponse,
  mockApiError,
} from '../utils/testUtils';

// Mock services
jest.mock('../../services/taskService', () => ({
  taskService: {
    getTasks: jest.fn(),
    createTask: jest.fn(),
    updateTaskStatus: jest.fn(),
  },
}));

jest.mock('../../services/teamService', () => ({
  teamService: {
    getTeams: jest.fn(),
  },
}));

jest.mock('../../services/aiService', () => ({
  aiService: {
    analyzeTaskPriority: jest.fn(),
  },
}));

const mockTaskService = taskService as jest.Mocked<typeof taskService>;
const mockTeamService = teamService as jest.Mocked<typeof teamService>;
const mockAiService = aiService as jest.Mocked<typeof aiService>;

// Mock react-router-dom navigation
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
}));

describe('Tasks Component', () => {
  const mockUser = createMockUser();
  const mockTasks = [
    createMockTask({ 
      id: 'task1', 
      title: 'Implement login feature', 
      status: 'TODO', 
      priority: 'HIGH',
      estimatedHours: 16,
      actualHours: 0,
    }),
    createMockTask({ 
      id: 'task2', 
      title: 'Fix bug in payment system', 
      status: 'IN_PROGRESS', 
      priority: 'CRITICAL',
      estimatedHours: 8,
      actualHours: 4,
    }),
    createMockTask({ 
      id: 'task3', 
      title: 'Update documentation', 
      status: 'COMPLETED', 
      priority: 'LOW',
      estimatedHours: 4,
      actualHours: 3,
    }),
  ];

  const mockTeams = [
    createMockTeam({
      id: 'team1',
      name: 'Development Team',
      projects: [
        { id: 'project1', name: 'Web App' },
        { id: 'project2', name: 'Mobile App' },
      ],
      members: [
        { userId: 'user1', team: { firstName: 'John', lastName: 'Doe' } },
        { userId: 'user2', team: { firstName: 'Jane', lastName: 'Smith' } },
      ],
    }),
  ];

  beforeEach(() => {
    jest.clearAllMocks();
    mockNavigate.mockClear();
  });

  describe('Rendering', () => {
    it('should render tasks page with header and stats', async () => {
      // Arrange
      mockTaskService.getTasks.mockResolvedValue(mockApiResponse(mockTasks));

      // Act
      renderWithProviders(<Tasks />, {
        preloadedState: createAuthenticatedState(mockUser),
      });

      // Assert
      expect(screen.getByRole('heading', { name: /tasks/i })).toBeInTheDocument();
      expect(screen.getByText(/manage and track your tasks efficiently/i)).toBeInTheDocument();

      await waitFor(() => {
        // Check task stats
        expect(screen.getByText('3')).toBeInTheDocument(); // Total tasks
        expect(screen.getByText('Total Tasks')).toBeInTheDocument();
        expect(screen.getByText('1')).toBeInTheDocument(); // In Progress
        expect(screen.getByText('In Progress')).toBeInTheDocument();
      });
    });

    it('should show loading state initially', () => {
      // Arrange
      mockTaskService.getTasks.mockImplementation(() => new Promise(() => {})); // Never resolves

      // Act
      renderWithProviders(<Tasks />, {
        preloadedState: createAuthenticatedState(mockUser),
      });

      // Assert
      expect(screen.getByRole('progressbar')).toBeInTheDocument();
    });

    it('should render task tabs with correct counts', async () => {
      // Arrange
      mockTaskService.getTasks.mockResolvedValue(mockApiResponse(mockTasks));

      // Act
      renderWithProviders(<Tasks />, {
        preloadedState: createAuthenticatedState(mockUser),
      });

      // Assert
      await waitFor(() => {
        expect(screen.getByText('All (3)')).toBeInTheDocument();
        expect(screen.getByText('To Do (1)')).toBeInTheDocument();
        expect(screen.getByText('In Progress (1)')).toBeInTheDocument();
        expect(screen.getByText('Completed (1)')).toBeInTheDocument();
      });
    });

    it('should render task cards with correct information', async () => {
      // Arrange
      mockTaskService.getTasks.mockResolvedValue(mockApiResponse(mockTasks));

      // Act
      renderWithProviders(<Tasks />, {
        preloadedState: createAuthenticatedState(mockUser),
      });

      // Assert
      await waitFor(() => {
        expect(screen.getByText('Implement login feature')).toBeInTheDocument();
        expect(screen.getByText('Fix bug in payment system')).toBeInTheDocument();
        expect(screen.getByText('Update documentation')).toBeInTheDocument();
      });

      // Check priority chips
      expect(screen.getByText('HIGH')).toBeInTheDocument();
      expect(screen.getByText('CRITICAL')).toBeInTheDocument();
      expect(screen.getByText('LOW')).toBeInTheDocument();

      // Check status chips
      expect(screen.getByText('TODO')).toBeInTheDocument();
      expect(screen.getByText('IN PROGRESS')).toBeInTheDocument();
      expect(screen.getByText('COMPLETED')).toBeInTheDocument();
    });

    it('should show empty state when no tasks exist', async () => {
      // Arrange
      mockTaskService.getTasks.mockResolvedValue(mockApiResponse([]));

      // Act
      renderWithProviders(<Tasks />, {
        preloadedState: createAuthenticatedState(mockUser),
      });

      // Assert
      await waitFor(() => {
        expect(screen.getByText(/no tasks found in this category/i)).toBeInTheDocument();
      });
    });
  });

  describe('Task Filtering', () => {
    it('should filter tasks by status when tab is clicked', async () => {
      // Arrange
      mockTaskService.getTasks.mockResolvedValue(mockApiResponse(mockTasks));

      // Act
      renderWithProviders(<Tasks />, {
        preloadedState: createAuthenticatedState(mockUser),
      });

      await waitFor(() => {
        expect(screen.getByText('All (3)')).toBeInTheDocument();
      });

      // Click on "To Do" tab
      const todoTab = screen.getByText('To Do (1)');
      await userEvent.click(todoTab);

      // Assert
      await waitFor(() => {
        expect(screen.getByText('Implement login feature')).toBeInTheDocument();
        expect(screen.queryByText('Fix bug in payment system')).not.toBeInTheDocument();
        expect(screen.queryByText('Update documentation')).not.toBeInTheDocument();
      });
    });

    it('should show completed tasks in completed tab', async () => {
      // Arrange
      mockTaskService.getTasks.mockResolvedValue(mockApiResponse(mockTasks));

      // Act
      renderWithProviders(<Tasks />, {
        preloadedState: createAuthenticatedState(mockUser),
      });

      await waitFor(() => {
        expect(screen.getByText('Completed (1)')).toBeInTheDocument();
      });

      // Click on "Completed" tab
      const completedTab = screen.getByText('Completed (1)');
      await userEvent.click(completedTab);

      // Assert
      await waitFor(() => {
        expect(screen.getByText('Update documentation')).toBeInTheDocument();
        expect(screen.queryByText('Implement login feature')).not.toBeInTheDocument();
        expect(screen.queryByText('Fix bug in payment system')).not.toBeInTheDocument();
      });
    });
  });

  describe('Task Interactions', () => {
    it('should navigate to task detail when task card is clicked', async () => {
      // Arrange
      mockTaskService.getTasks.mockResolvedValue(mockApiResponse(mockTasks));

      // Act
      renderWithProviders(<Tasks />, {
        preloadedState: createAuthenticatedState(mockUser),
      });

      await waitFor(() => {
        expect(screen.getByText('Implement login feature')).toBeInTheDocument();
      });

      const taskCard = screen.getByText('Implement login feature').closest('.MuiCard-root');
      if (taskCard) {
        await userEvent.click(taskCard);
      }

      // Assert
      expect(mockNavigate).toHaveBeenCalledWith('/tasks/task1');
    });

    it('should update task status when action button is clicked', async () => {
      // Arrange
      mockTaskService.getTasks.mockResolvedValue(mockApiResponse(mockTasks));
      mockTaskService.updateTaskStatus.mockResolvedValue(mockApiResponse({}));

      // Act
      renderWithProviders(<Tasks />, {
        preloadedState: createAuthenticatedState(mockUser),
      });

      await waitFor(() => {
        expect(screen.getByText('Implement login feature')).toBeInTheDocument();
      });

      // Find and click the "Start" button for the TODO task
      const startButton = screen.getByText('Start');
      await userEvent.click(startButton);

      // Assert
      await waitFor(() => {
        expect(mockTaskService.updateTaskStatus).toHaveBeenCalledWith('task1', 'IN_PROGRESS');
      });
    });

    it('should show different action buttons based on task status', async () => {
      // Arrange
      mockTaskService.getTasks.mockResolvedValue(mockApiResponse(mockTasks));

      // Act
      renderWithProviders(<Tasks />, {
        preloadedState: createAuthenticatedState(mockUser),
      });

      // Assert
      await waitFor(() => {
        expect(screen.getByText('Start')).toBeInTheDocument(); // For TODO task
        expect(screen.getByText('Review')).toBeInTheDocument(); // For IN_PROGRESS task
        // Completed task should not have action button
      });
    });
  });

  describe('Create Task Dialog', () => {
    it('should open create task dialog when FAB is clicked', async () => {
      // Arrange
      mockTaskService.getTasks.mockResolvedValue(mockApiResponse(mockTasks));
      mockTeamService.getTeams.mockResolvedValue(mockApiResponse(mockTeams));

      // Act
      renderWithProviders(<Tasks />, {
        preloadedState: createAuthenticatedState(mockUser),
      });

      await waitFor(() => {
        const fab = screen.getByRole('button', { name: /create task/i });
        expect(fab).toBeInTheDocument();
      });

      const fab = screen.getByRole('button', { name: /create task/i });
      await userEvent.click(fab);

      // Assert
      expect(screen.getByRole('dialog')).toBeInTheDocument();
      expect(screen.getByText('Create New Task')).toBeInTheDocument();
      expect(screen.getByLabelText(/task title/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/description/i)).toBeInTheDocument();
    });

    it('should create task successfully with AI analysis', async () => {
      // Arrange
      const newTask = createMockTask({ id: 'new-task', title: 'New Task' });
      mockTaskService.getTasks.mockResolvedValue(mockApiResponse(mockTasks));
      mockTeamService.getTeams.mockResolvedValue(mockApiResponse(mockTeams));
      mockTaskService.createTask.mockResolvedValue(mockApiResponse(newTask));
      mockAiService.analyzeTaskPriority.mockResolvedValue({
        priority: 'HIGH',
        reasoning: 'AI determined this is high priority',
        estimatedHours: 12,
        dependencies: [],
      });

      // Act
      renderWithProviders(<Tasks />, {
        preloadedState: createAuthenticatedState(mockUser),
      });

      await waitFor(() => {
        const fab = screen.getByRole('button', { name: /create task/i });
        expect(fab).toBeInTheDocument();
      });

      // Open dialog
      const fab = screen.getByRole('button', { name: /create task/i });
      await userEvent.click(fab);

      // Fill form
      const titleInput = screen.getByLabelText(/task title/i);
      const descriptionInput = screen.getByLabelText(/description/i);
      
      await userEvent.type(titleInput, 'New Task');
      await userEvent.type(descriptionInput, 'A new task for testing');

      // Select project
      const projectSelect = screen.getByLabelText(/project/i);
      await userEvent.click(projectSelect);
      
      await waitFor(() => {
        const projectOption = screen.getByText('Web App (Development Team)');
        expect(projectOption).toBeInTheDocument();
      });

      const projectOption = screen.getByText('Web App (Development Team)');
      await userEvent.click(projectOption);

      // Submit form
      const createButton = screen.getByRole('button', { name: /create task/i });
      await userEvent.click(createButton);

      // Assert
      await waitFor(() => {
        expect(mockTaskService.createTask).toHaveBeenCalledWith(
          expect.objectContaining({
            title: 'New Task',
            description: 'A new task for testing',
            projectId: 'project1',
          })
        );
      });
    });

    it('should show validation errors for invalid input', async () => {
      // Arrange
      mockTaskService.getTasks.mockResolvedValue(mockApiResponse(mockTasks));
      mockTeamService.getTeams.mockResolvedValue(mockApiResponse(mockTeams));

      // Act
      renderWithProviders(<Tasks />, {
        preloadedState: createAuthenticatedState(mockUser),
      });

      await waitFor(() => {
        const fab = screen.getByRole('button', { name: /create task/i });
        expect(fab).toBeInTheDocument();
      });

      // Open dialog
      const fab = screen.getByRole('button', { name: /create task/i });
      await userEvent.click(fab);

      // Try to submit without filling required fields
      const createButton = screen.getByRole('button', { name: /create task/i });
      await userEvent.click(createButton);

      // Assert
      await waitFor(() => {
        expect(screen.getByText(/task title is required/i)).toBeInTheDocument();
      });
    });
  });

  describe('Progress Tracking', () => {
    it('should display progress bars for tasks with estimated hours', async () => {
      // Arrange
      mockTaskService.getTasks.mockResolvedValue(mockApiResponse(mockTasks));

      // Act
      renderWithProviders(<Tasks />, {
        preloadedState: createAuthenticatedState(mockUser),
      });

      // Assert
      await waitFor(() => {
        // Check progress text
        expect(screen.getByText('0h / 16h')).toBeInTheDocument(); // TODO task
        expect(screen.getByText('4h / 8h')).toBeInTheDocument(); // IN_PROGRESS task
        expect(screen.getByText('3h / 4h')).toBeInTheDocument(); // COMPLETED task
      });

      // Check progress bars are present
      const progressBars = screen.getAllByRole('progressbar');
      expect(progressBars.length).toBeGreaterThan(0);
    });
  });

  describe('Error Handling', () => {
    it('should handle API error when loading tasks', async () => {
      // Arrange
      mockTaskService.getTasks.mockRejectedValue(mockApiError('Failed to load tasks'));

      // Act
      renderWithProviders(<Tasks />, {
        preloadedState: createAuthenticatedState(mockUser),
      });

      // Assert
      await waitFor(() => {
        expect(mockTaskService.getTasks).toHaveBeenCalled();
      });
    });

    it('should handle task status update error', async () => {
      // Arrange
      mockTaskService.getTasks.mockResolvedValue(mockApiResponse(mockTasks));
      mockTaskService.updateTaskStatus.mockRejectedValue(mockApiError('Failed to update task'));

      // Act
      renderWithProviders(<Tasks />, {
        preloadedState: createAuthenticatedState(mockUser),
      });

      await waitFor(() => {
        expect(screen.getByText('Start')).toBeInTheDocument();
      });

      const startButton = screen.getByText('Start');
      await userEvent.click(startButton);

      // Assert
      await waitFor(() => {
        expect(mockTaskService.updateTaskStatus).toHaveBeenCalled();
      });
    });
  });
});
