#!/bin/bash

# CollabFlow Backend Test Runner
# This script runs the comprehensive test suite for the CollabFlow backend

set -e

echo "🧪 Starting CollabFlow Backend Test Suite"
echo "========================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    print_error "Node.js is not installed. Please install Node.js 18+ to run tests."
    exit 1
fi

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    print_error "npm is not installed. Please install npm to run tests."
    exit 1
fi

# Set test environment
export NODE_ENV=test
export JWT_SECRET=test-jwt-secret-for-testing-only
export DATABASE_URL=postgresql://test:test@localhost:5432/collabflow_test
export REDIS_URL=redis://localhost:6379/1
export OPENAI_API_KEY=test-openai-key

print_status "Environment set to TEST mode"

# Install dependencies if node_modules doesn't exist
if [ ! -d "node_modules" ]; then
    print_status "Installing dependencies..."
    npm install
    print_success "Dependencies installed"
fi

# Run different test suites based on argument
case "${1:-all}" in
    "unit")
        print_status "Running Unit Tests..."
        npm run test -- --testPathPattern="__tests__/services" --coverage --coverageDirectory=coverage/unit
        print_success "Unit tests completed"
        ;;
    
    "integration")
        print_status "Running Integration Tests..."
        npm run test -- --testPathPattern="__tests__/integration" --coverage --coverageDirectory=coverage/integration
        print_success "Integration tests completed"
        ;;
    
    "coverage")
        print_status "Running All Tests with Coverage Report..."
        npm run test:coverage
        print_success "Coverage report generated in coverage/ directory"
        ;;
    
    "watch")
        print_status "Running Tests in Watch Mode..."
        npm run test:watch
        ;;
    
    "ci")
        print_status "Running CI Test Suite..."
        npm run test -- --ci --coverage --watchAll=false --passWithNoTests
        print_success "CI tests completed"
        ;;
    
    "all"|*)
        print_status "Running Complete Test Suite..."
        
        # Run unit tests
        print_status "1/3 Running Unit Tests..."
        npm run test -- --testPathPattern="__tests__/services" --silent
        print_success "Unit tests passed ✓"
        
        # Run integration tests
        print_status "2/3 Running Integration Tests..."
        npm run test -- --testPathPattern="__tests__/integration" --silent
        print_success "Integration tests passed ✓"
        
        # Generate coverage report
        print_status "3/3 Generating Coverage Report..."
        npm run test:coverage -- --silent
        print_success "Coverage report generated ✓"
        
        print_success "All tests completed successfully!"
        ;;
esac

# Display coverage summary if coverage was generated
if [ -f "coverage/lcov-report/index.html" ]; then
    print_status "Coverage report available at: coverage/lcov-report/index.html"
fi

echo ""
echo "🎉 Test suite execution completed!"
echo "========================================"
