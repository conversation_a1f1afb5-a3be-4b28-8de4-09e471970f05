import React from 'react';
import { Typography, Box } from '@mui/material';
import { useParams } from 'react-router-dom';

const TaskDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();

  return (
    <Box>
      <Typography variant="h4" component="h1" gutterBottom>
        Task Detail
      </Typography>
      <Typography variant="body1" color="text.secondary">
        Task ID: {id} - Coming soon!
      </Typography>
    </Box>
  );
};

export default TaskDetail;
