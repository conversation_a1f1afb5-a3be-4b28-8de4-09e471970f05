import { jest } from '@jest/globals';
import { aiService } from '../../services/aiService';
import OpenAI from 'openai';

// Mock OpenAI
const mockOpenAI = OpenAI as jest.MockedClass<typeof OpenAI>;
const mockCreate = jest.fn();

beforeEach(() => {
  mockOpenAI.mockImplementation(() => ({
    chat: {
      completions: {
        create: mockCreate,
      },
    },
  } as any));
});

describe('AIService', () => {
  describe('analyzeMeetingNecessity', () => {
    it('should analyze meeting necessity with AI when API key is available', async () => {
      // Arrange
      const mockResponse = {
        choices: [{
          message: {
            content: JSON.stringify({
              score: 75,
              reasoning: 'This meeting appears necessary for decision making',
              alternatives: ['Email discussion', 'Slack thread'],
              recommendations: ['Reduce duration to 30 minutes', 'Include agenda']
            })
          }
        }]
      };
      mockCreate.mockResolvedValue(mockResponse);

      // Act
      const result = await aiService.analyzeMeetingNecessity(
        'Sprint Planning',
        'Plan tasks for next sprint',
        ['user1', 'user2', 'user3'],
        60,
        'Development team meeting'
      );

      // Assert
      expect(result.score).toBe(75);
      expect(result.reasoning).toBe('This meeting appears necessary for decision making');
      expect(result.alternatives).toEqual(['Email discussion', 'Slack thread']);
      expect(result.recommendations).toEqual(['Reduce duration to 30 minutes', 'Include agenda']);
      expect(mockCreate).toHaveBeenCalledWith({
        model: 'gpt-3.5-turbo',
        messages: expect.arrayContaining([
          expect.objectContaining({
            role: 'system',
            content: expect.stringContaining('expert in team productivity')
          }),
          expect.objectContaining({
            role: 'user',
            content: expect.stringContaining('Sprint Planning')
          })
        ]),
        temperature: 0.3,
        max_tokens: 500,
      });
    });

    it('should fall back to heuristic analysis when AI fails', async () => {
      // Arrange
      mockCreate.mockRejectedValue(new Error('API Error'));

      // Act
      const result = await aiService.analyzeMeetingNecessity(
        'Quick Update',
        'Status update meeting',
        ['user1', 'user2'],
        30
      );

      // Assert
      expect(result.score).toBeGreaterThanOrEqual(0);
      expect(result.score).toBeLessThanOrEqual(100);
      expect(result.reasoning).toBe('Analysis based on meeting characteristics and heuristics');
      expect(Array.isArray(result.alternatives)).toBe(true);
      expect(Array.isArray(result.recommendations)).toBe(true);
    });

    it('should adjust score based on meeting characteristics in fallback mode', async () => {
      // Arrange
      mockCreate.mockRejectedValue(new Error('API Error'));

      // Act - Test urgent meeting
      const urgentResult = await aiService.analyzeMeetingNecessity(
        'URGENT: Critical Bug Fix',
        'Emergency meeting to address critical production issue',
        ['user1', 'user2'],
        30
      );

      // Test async-friendly meeting
      const asyncResult = await aiService.analyzeMeetingNecessity(
        'Weekly Status Update',
        'Regular status update meeting',
        ['user1', 'user2', 'user3', 'user4', 'user5', 'user6'],
        90
      );

      // Assert
      expect(urgentResult.score).toBeGreaterThan(asyncResult.score);
    });

    it('should handle invalid AI response gracefully', async () => {
      // Arrange
      const mockResponse = {
        choices: [{
          message: {
            content: 'Invalid JSON response'
          }
        }]
      };
      mockCreate.mockResolvedValue(mockResponse);

      // Act
      const result = await aiService.analyzeMeetingNecessity(
        'Test Meeting',
        'Test description',
        ['user1'],
        60
      );

      // Assert - Should fall back to heuristic analysis
      expect(result.reasoning).toBe('Analysis based on meeting characteristics and heuristics');
    });
  });

  describe('analyzeTaskPriority', () => {
    it('should analyze task priority with AI when available', async () => {
      // Arrange
      const mockResponse = {
        choices: [{
          message: {
            content: JSON.stringify({
              priority: 'HIGH',
              reasoning: 'Critical bug fix with customer impact',
              estimatedHours: 12,
              dependencies: ['task1', 'task2']
            })
          }
        }]
      };
      mockCreate.mockResolvedValue(mockResponse);

      // Act
      const result = await aiService.analyzeTaskPriority(
        'Fix critical payment bug',
        'Payment processing is failing for premium users',
        new Date(Date.now() + 24 * 60 * 60 * 1000), // Due tomorrow
        ['task1'],
        'E-commerce Platform'
      );

      // Assert
      expect(result.priority).toBe('HIGH');
      expect(result.reasoning).toBe('Critical bug fix with customer impact');
      expect(result.estimatedHours).toBe(12);
      expect(result.dependencies).toEqual(['task1', 'task2']);
    });

    it('should fall back to heuristic analysis for task priority', async () => {
      // Arrange
      mockCreate.mockRejectedValue(new Error('API Error'));

      // Act - Test urgent task (due tomorrow)
      const urgentResult = await aiService.analyzeTaskPriority(
        'Fix critical bug',
        'Critical production issue',
        new Date(Date.now() + 24 * 60 * 60 * 1000),
        ['dep1', 'dep2']
      );

      // Test low priority task (due in 2 weeks)
      const lowPriorityResult = await aiService.analyzeTaskPriority(
        'Update documentation',
        'Update user guide',
        new Date(Date.now() + 14 * 24 * 60 * 60 * 1000)
      );

      // Assert
      expect(urgentResult.priority).toBe('CRITICAL');
      expect(lowPriorityResult.priority).toBe('LOW');
      expect(urgentResult.estimatedHours).toBeGreaterThan(0);
    });

    it('should adjust priority based on dependencies in fallback mode', async () => {
      // Arrange
      mockCreate.mockRejectedValue(new Error('API Error'));

      // Act
      const taskWithDeps = await aiService.analyzeTaskPriority(
        'Implement feature',
        'New feature implementation',
        new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
        ['dep1', 'dep2', 'dep3']
      );

      const taskWithoutDeps = await aiService.analyzeTaskPriority(
        'Implement feature',
        'New feature implementation',
        new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)
      );

      // Assert
      expect(taskWithDeps.priority).not.toBe('LOW');
      expect(taskWithDeps.dependencies).toEqual(['dep1', 'dep2', 'dep3']);
      expect(taskWithoutDeps.dependencies).toEqual([]);
    });

    it('should estimate hours based on complexity keywords', async () => {
      // Arrange
      mockCreate.mockRejectedValue(new Error('API Error'));

      // Act
      const complexTask = await aiService.analyzeTaskPriority(
        'Implement new authentication system',
        'Develop and implement OAuth2 authentication',
        undefined,
        []
      );

      const simpleTask = await aiService.analyzeTaskPriority(
        'Fix typo in documentation',
        'Update spelling error in README',
        undefined,
        []
      );

      // Assert
      expect(complexTask.estimatedHours).toBeGreaterThan(simpleTask.estimatedHours);
    });
  });

  describe('analyzeTeamCapacity', () => {
    it('should analyze team capacity and identify bottlenecks', async () => {
      // Arrange
      const teamMembers = [
        {
          id: 'user1',
          name: 'John Doe',
          skills: ['JavaScript', 'React'],
          currentTasks: 8,
          availability: 40,
        },
        {
          id: 'user2',
          name: 'Jane Smith',
          skills: ['Python', 'Django'],
          currentTasks: 3,
          availability: 40,
        },
        {
          id: 'user3',
          name: 'Bob Wilson',
          skills: ['Java', 'Spring'],
          currentTasks: 10,
          availability: 30,
        },
      ];

      const upcomingTasks = [
        {
          title: 'Build React component',
          estimatedHours: 16,
          requiredSkills: ['JavaScript', 'React'],
        },
        {
          title: 'API development',
          estimatedHours: 24,
          requiredSkills: ['Python'],
        },
      ];

      // Act
      const result = await aiService.analyzeTeamCapacity(teamMembers, upcomingTasks);

      // Assert
      expect(result.overallUtilization).toBeGreaterThan(0);
      expect(result.overallUtilization).toBeLessThanOrEqual(100);
      expect(result.bottlenecks.length).toBeGreaterThan(0);
      expect(result.bottlenecks[0].utilization).toBeGreaterThan(80);
      expect(Array.isArray(result.recommendations)).toBe(true);
      expect(result.recommendations.length).toBeGreaterThan(0);
    });

    it('should identify skill gaps in team capacity analysis', async () => {
      // Arrange
      const teamMembers = [
        {
          id: 'user1',
          name: 'John Doe',
          skills: ['JavaScript'],
          currentTasks: 2,
          availability: 40,
        },
      ];

      const upcomingTasks = [
        {
          title: 'Python API development',
          estimatedHours: 16,
          requiredSkills: ['Python', 'Django'],
        },
      ];

      // Act
      const result = await aiService.analyzeTeamCapacity(teamMembers, upcomingTasks);

      // Assert
      expect(result.recommendations.some(rec => 
        rec.includes('Missing skills') || rec.includes('Python') || rec.includes('Django')
      )).toBe(true);
    });

    it('should handle empty team gracefully', async () => {
      // Act
      const result = await aiService.analyzeTeamCapacity([], []);

      // Assert
      expect(result.overallUtilization).toBe(0);
      expect(result.bottlenecks).toEqual([]);
      expect(Array.isArray(result.recommendations)).toBe(true);
    });
  });

  describe('detectConflicts', () => {
    it('should detect schedule conflicts between meetings', async () => {
      // Arrange
      const meetings = [
        {
          id: 'meeting1',
          title: 'Team Standup',
          startTime: new Date('2024-01-15T09:00:00Z'),
          endTime: new Date('2024-01-15T09:30:00Z'),
          participants: ['user1', 'user2'],
        },
        {
          id: 'meeting2',
          title: 'Sprint Planning',
          startTime: new Date('2024-01-15T09:15:00Z'),
          endTime: new Date('2024-01-15T10:15:00Z'),
          participants: ['user1', 'user3'],
        },
      ];

      const tasks = [
        {
          id: 'task1',
          title: 'Task 1',
          assigneeId: 'user1',
          dueDate: new Date('2024-01-16T00:00:00Z'),
          priority: 'HIGH',
        },
      ];

      // Act
      const result = await aiService.detectConflicts(meetings, tasks);

      // Assert
      expect(result.conflicts.length).toBeGreaterThan(0);
      expect(result.conflicts[0].type).toBe('schedule');
      expect(result.conflicts[0].severity).toBe('high');
      expect(result.resolutions.length).toBeGreaterThan(0);
    });

    it('should detect resource conflicts with high-priority tasks', async () => {
      // Arrange
      const meetings = [];
      const tasks = [
        {
          id: 'task1',
          title: 'Critical Bug Fix',
          assigneeId: 'user1',
          dueDate: new Date('2024-01-16T00:00:00Z'),
          priority: 'CRITICAL',
        },
        {
          id: 'task2',
          title: 'Another Critical Task',
          assigneeId: 'user1',
          dueDate: new Date('2024-01-16T00:00:00Z'),
          priority: 'HIGH',
        },
        {
          id: 'task3',
          title: 'Third High Priority Task',
          assigneeId: 'user1',
          dueDate: new Date('2024-01-16T00:00:00Z'),
          priority: 'HIGH',
        },
      ];

      // Act
      const result = await aiService.detectConflicts(meetings, tasks);

      // Assert
      expect(result.conflicts.some(c => c.type === 'resource')).toBe(true);
      expect(result.resolutions.some(r => 
        r.solution.includes('Redistribute') || r.solution.includes('priorities')
      )).toBe(true);
    });

    it('should handle empty inputs gracefully', async () => {
      // Act
      const result = await aiService.detectConflicts([], []);

      // Assert
      expect(result.conflicts).toEqual([]);
      expect(result.resolutions).toEqual([]);
    });
  });
});
