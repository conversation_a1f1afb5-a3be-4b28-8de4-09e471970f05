import { apiClient } from './api';

export interface Meeting {
  id: string;
  title: string;
  description?: string;
  organizerId: string;
  startTime: string;
  endTime: string;
  location?: string;
  meetingUrl?: string;
  necessityScore: number;
  status: 'SCHEDULED' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED';
  createdAt: string;
  updatedAt: string;
  organizer?: {
    id: string;
    firstName: string;
    lastName: string;
    avatar?: string;
  };
  participants?: Array<{
    userId: string;
    user: {
      id: string;
      firstName: string;
      lastName: string;
      avatar?: string;
      skills?: string[];
    };
  }>;
  agenda?: AgendaItem[];
}

export interface AgendaItem {
  id: string;
  title: string;
  description?: string;
  duration: number;
  presenterId?: string;
  order: number;
  presenter?: {
    id: string;
    firstName: string;
    lastName: string;
  };
}

export interface CreateMeetingData {
  title: string;
  description?: string;
  participants: string[];
  startTime: string;
  endTime: string;
  location?: string;
  meetingUrl?: string;
  necessityScore?: number;
  agenda?: Array<{
    title: string;
    description?: string;
    duration: number;
    presenterId?: string;
  }>;
}

export interface UpdateMeetingData {
  title?: string;
  description?: string;
  startTime?: string;
  endTime?: string;
  location?: string;
  meetingUrl?: string;
  status?: 'SCHEDULED' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED';
}

export const meetingService = {
  // Get all meetings
  getMeetings: async (params?: {
    page?: number;
    limit?: number;
    status?: string;
    date?: string;
    organizerId?: string;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  }) => {
    return apiClient.get<Meeting[]>('/meetings', { params });
  },

  // Create new meeting
  createMeeting: async (data: CreateMeetingData) => {
    return apiClient.post<{
      meeting: Meeting;
      aiAnalysis: {
        necessityScore: number;
        recommendations: string[];
        conflicts: string[];
      };
    }>('/meetings', data);
  },

  // Get meeting by ID
  getMeetingById: async (meetingId: string) => {
    return apiClient.get<Meeting>(`/meetings/${meetingId}`);
  },

  // Update meeting
  updateMeeting: async (meetingId: string, data: UpdateMeetingData) => {
    return apiClient.put<Meeting>(`/meetings/${meetingId}`, data);
  },

  // Cancel/Delete meeting
  cancelMeeting: async (meetingId: string) => {
    return apiClient.delete(`/meetings/${meetingId}`);
  },

  // Get meetings for a specific date
  getMeetingsByDate: async (date: string) => {
    return apiClient.get<Meeting[]>('/meetings', {
      params: { date },
    });
  },

  // Get upcoming meetings
  getUpcomingMeetings: async (limit: number = 10) => {
    return apiClient.get<Meeting[]>('/meetings/upcoming', {
      params: { limit },
    });
  },

  // Add participant to meeting
  addParticipant: async (meetingId: string, userId: string) => {
    return apiClient.post(`/meetings/${meetingId}/participants`, { userId });
  },

  // Remove participant from meeting
  removeParticipant: async (meetingId: string, userId: string) => {
    return apiClient.delete(`/meetings/${meetingId}/participants/${userId}`);
  },

  // Update meeting status
  updateMeetingStatus: async (
    meetingId: string,
    status: 'SCHEDULED' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED'
  ) => {
    return apiClient.patch(`/meetings/${meetingId}/status`, { status });
  },

  // Add agenda item
  addAgendaItem: async (
    meetingId: string,
    item: {
      title: string;
      description?: string;
      duration: number;
      presenterId?: string;
    }
  ) => {
    return apiClient.post(`/meetings/${meetingId}/agenda`, item);
  },

  // Update agenda item
  updateAgendaItem: async (
    meetingId: string,
    itemId: string,
    data: {
      title?: string;
      description?: string;
      duration?: number;
      presenterId?: string;
    }
  ) => {
    return apiClient.put(`/meetings/${meetingId}/agenda/${itemId}`, data);
  },

  // Remove agenda item
  removeAgendaItem: async (meetingId: string, itemId: string) => {
    return apiClient.delete(`/meetings/${meetingId}/agenda/${itemId}`);
  },

  // Reorder agenda items
  reorderAgenda: async (
    meetingId: string,
    items: Array<{ id: string; order: number }>
  ) => {
    return apiClient.patch(`/meetings/${meetingId}/agenda/reorder`, { items });
  },

  // Get meeting notes
  getMeetingNotes: async (meetingId: string) => {
    return apiClient.get(`/meetings/${meetingId}/notes`);
  },

  // Update meeting notes
  updateMeetingNotes: async (meetingId: string, notes: string) => {
    return apiClient.put(`/meetings/${meetingId}/notes`, { notes });
  },

  // Get meeting recordings
  getMeetingRecordings: async (meetingId: string) => {
    return apiClient.get(`/meetings/${meetingId}/recordings`);
  },

  // Upload meeting recording
  uploadRecording: async (meetingId: string, file: File) => {
    const formData = new FormData();
    formData.append('recording', file);
    
    return apiClient.post(`/meetings/${meetingId}/recordings`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },
};
