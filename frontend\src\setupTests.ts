import '@testing-library/jest-dom';
import { configure } from '@testing-library/react';
import { jest } from '@jest/globals';

// Configure React Testing Library
configure({ testIdAttribute: 'data-testid' });

// Mock environment variables
process.env.REACT_APP_API_URL = 'http://localhost:3001/api';
process.env.REACT_APP_WS_URL = 'ws://localhost:3001';

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
global.localStorage = localStorageMock as any;

// Mock sessionStorage
const sessionStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
global.sessionStorage = sessionStorageMock as any;

// Mock IntersectionObserver
global.IntersectionObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));

// Mock ResizeObserver
global.ResizeObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));

// Mock matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(), // deprecated
    removeListener: jest.fn(), // deprecated
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});

// Mock react-hot-toast
jest.mock('react-hot-toast', () => ({
  __esModule: true,
  default: jest.fn(),
  toast: {
    success: jest.fn(),
    error: jest.fn(),
    loading: jest.fn(),
    dismiss: jest.fn(),
  },
}));

// Mock react-router-dom
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => jest.fn(),
  useLocation: () => ({
    pathname: '/',
    search: '',
    hash: '',
    state: null,
  }),
  useParams: () => ({}),
}));

// Mock Socket.IO client
jest.mock('socket.io-client', () => ({
  io: jest.fn(() => ({
    on: jest.fn(),
    off: jest.fn(),
    emit: jest.fn(),
    connect: jest.fn(),
    disconnect: jest.fn(),
    connected: true,
    id: 'mock-socket-id',
  })),
}));

// Mock WebSocket service
jest.mock('./services/websocketService', () => ({
  websocketService: {
    connect: jest.fn(),
    disconnect: jest.fn(),
    subscribeToTask: jest.fn(),
    unsubscribeFromTask: jest.fn(),
    subscribeToProject: jest.fn(),
    unsubscribeFromProject: jest.fn(),
    joinMeeting: jest.fn(),
    leaveMeeting: jest.fn(),
    startTyping: jest.fn(),
    stopTyping: jest.fn(),
    updatePresence: jest.fn(),
    markNotificationAsRead: jest.fn(),
    markAllNotificationsAsRead: jest.fn(),
    isConnected: jest.fn(() => true),
    getConnectionId: jest.fn(() => 'mock-connection-id'),
  },
}));

// Mock API services
jest.mock('./services/api', () => ({
  apiClient: {
    get: jest.fn(),
    post: jest.fn(),
    put: jest.fn(),
    patch: jest.fn(),
    delete: jest.fn(),
  },
}));

// Global test utilities
global.mockUser = {
  id: 'test-user-id',
  email: '<EMAIL>',
  firstName: 'Test',
  lastName: 'User',
  role: 'MEMBER',
  skills: ['JavaScript', 'TypeScript'],
  timezone: 'UTC',
  avatar: null,
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
};

global.mockTeam = {
  id: 'test-team-id',
  name: 'Test Team',
  description: 'A test team',
  memberCount: 3,
  projectCount: 2,
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  members: [
    {
      userId: 'test-user-id',
      teamId: 'test-team-id',
      role: 'LEAD',
      joinedAt: new Date().toISOString(),
      user: global.mockUser,
    },
  ],
};

global.mockTask = {
  id: 'test-task-id',
  title: 'Test Task',
  description: 'A test task',
  projectId: 'test-project-id',
  assigneeId: 'test-user-id',
  status: 'TODO',
  priority: 'MEDIUM',
  estimatedHours: 8,
  actualHours: 0,
  tags: ['testing'],
  dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  assignee: global.mockUser,
  project: {
    id: 'test-project-id',
    name: 'Test Project',
    team: {
      id: 'test-team-id',
      name: 'Test Team',
    },
  },
};

global.mockMeeting = {
  id: 'test-meeting-id',
  title: 'Test Meeting',
  description: 'A test meeting',
  organizerId: 'test-user-id',
  startTime: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
  endTime: new Date(Date.now() + 24 * 60 * 60 * 1000 + 60 * 60 * 1000).toISOString(),
  location: 'Conference Room A',
  meetingUrl: 'https://zoom.us/j/123456789',
  necessityScore: 75,
  status: 'SCHEDULED',
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  organizer: global.mockUser,
  participants: [
    {
      userId: 'test-user-id',
      user: global.mockUser,
    },
  ],
};

// Reset all mocks before each test
beforeEach(() => {
  jest.clearAllMocks();
  localStorageMock.getItem.mockClear();
  localStorageMock.setItem.mockClear();
  localStorageMock.removeItem.mockClear();
  localStorageMock.clear.mockClear();
  sessionStorageMock.getItem.mockClear();
  sessionStorageMock.setItem.mockClear();
  sessionStorageMock.removeItem.mockClear();
  sessionStorageMock.clear.mockClear();
});
