import { Server as SocketIOServer } from 'socket.io';
import { Server as HTTPServer } from 'http';
import jwt from 'jsonwebtoken';
import { prisma } from '../config/database';
import { logger } from '../config/logger';
import { notificationService } from './notificationService';

export interface AuthenticatedSocket extends Socket {
  userId: string;
  user: {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
    role: string;
  };
}

interface Socket {
  id: string;
  userId?: string;
  user?: any;
  join: (room: string) => void;
  leave: (room: string) => void;
  emit: (event: string, data: any) => void;
  broadcast: {
    to: (room: string) => {
      emit: (event: string, data: any) => void;
    };
  };
  to: (room: string) => {
    emit: (event: string, data: any) => void;
  };
  handshake: {
    auth: {
      token?: string;
    };
  };
  disconnect: () => void;
}

class WebSocketService {
  private io: SocketIOServer | null = null;
  private connectedUsers: Map<string, Set<string>> = new Map(); // userId -> Set of socketIds
  private userRooms: Map<string, Set<string>> = new Map(); // userId -> Set of room names

  initialize(server: HTTPServer) {
    this.io = new SocketIOServer(server, {
      cors: {
        origin: process.env.CORS_ORIGIN || "http://localhost:3000",
        methods: ["GET", "POST"],
        credentials: true,
      },
      transports: ['websocket', 'polling'],
    });

    this.io.use(this.authenticateSocket.bind(this));
    this.io.on('connection', this.handleConnection.bind(this));

    logger.info('WebSocket service initialized');
  }

  private async authenticateSocket(socket: any, next: any) {
    try {
      const token = socket.handshake.auth.token;

      if (!token) {
        return next(new Error('Authentication token required'));
      }

      const decoded = jwt.verify(token, process.env.JWT_SECRET!) as any;

      if (!decoded || !decoded.userId || decoded.type !== 'access') {
        return next(new Error('Invalid token'));
      }

      // Get user details
      const user = await prisma.user.findUnique({
        where: { id: decoded.userId },
        select: {
          id: true,
          email: true,
          firstName: true,
          lastName: true,
          role: true,
        },
      });

      if (!user) {
        return next(new Error('User not found'));
      }

      socket.userId = user.id;
      socket.user = user;

      next();
    } catch (error) {
      logger.error('Socket authentication error:', error);
      next(new Error('Authentication failed'));
    }
  }

  private async handleConnection(socket: AuthenticatedSocket) {
    const userId = socket.userId;
    
    logger.info(`User ${userId} connected via WebSocket`);

    // Track connected user
    if (!this.connectedUsers.has(userId)) {
      this.connectedUsers.set(userId, new Set());
    }
    this.connectedUsers.get(userId)!.add(socket.id);

    // Join user to their personal room
    socket.join(`user:${userId}`);

    // Join user to their team rooms
    await this.joinUserTeamRooms(socket);

    // Set up event handlers
    this.setupEventHandlers(socket);

    // Send initial data
    await this.sendInitialData(socket);

    // Handle disconnection
    socket.on('disconnect', () => {
      this.handleDisconnection(socket);
    });
  }

  private async joinUserTeamRooms(socket: AuthenticatedSocket) {
    try {
      const userTeams = await prisma.teamMember.findMany({
        where: { userId: socket.userId },
        include: {
          user: {
            select: { id: true, name: true },
          },
        },
      });

      for (const teamMember of userTeams) {
        const teamRoom = `team:${teamMember.teamId}`;
        socket.join(teamRoom);

        // Track user rooms
        if (!this.userRooms.has(socket.userId)) {
          this.userRooms.set(socket.userId, new Set());
        }
        this.userRooms.get(socket.userId)!.add(teamRoom);
      }
    } catch (error) {
      logger.error('Error joining team rooms:', error);
    }
  }

  private setupEventHandlers(socket: AuthenticatedSocket) {
    // Task-related events
    socket.on('task:subscribe', (taskId: string) => {
      socket.join(`task:${taskId}`);
    });

    socket.on('task:unsubscribe', (taskId: string) => {
      socket.leave(`task:${taskId}`);
    });

    socket.on('task:update', async (data: { taskId: string; updates: any }) => {
      // Broadcast task update to all subscribers
      socket.broadcast.to(`task:${data.taskId}`).emit('task:updated', {
        taskId: data.taskId,
        updates: data.updates,
        updatedBy: socket.user,
        timestamp: new Date(),
      });
    });

    // Project-related events
    socket.on('project:subscribe', (projectId: string) => {
      socket.join(`project:${projectId}`);
    });

    socket.on('project:unsubscribe', (projectId: string) => {
      socket.leave(`project:${projectId}`);
    });

    // Meeting-related events
    socket.on('meeting:join', (meetingId: string) => {
      socket.join(`meeting:${meetingId}`);
      socket.broadcast.to(`meeting:${meetingId}`).emit('meeting:participant_joined', {
        user: socket.user,
        timestamp: new Date(),
      });
    });

    socket.on('meeting:leave', (meetingId: string) => {
      socket.leave(`meeting:${meetingId}`);
      socket.broadcast.to(`meeting:${meetingId}`).emit('meeting:participant_left', {
        user: socket.user,
        timestamp: new Date(),
      });
    });

    // Typing indicators
    socket.on('typing:start', (data: { room: string; context: string }) => {
      socket.broadcast.to(data.room).emit('typing:user_typing', {
        user: socket.user,
        context: data.context,
      });
    });

    socket.on('typing:stop', (data: { room: string; context: string }) => {
      socket.broadcast.to(data.room).emit('typing:user_stopped', {
        user: socket.user,
        context: data.context,
      });
    });

    // Presence updates
    socket.on('presence:update', (status: 'online' | 'away' | 'busy' | 'offline') => {
      this.broadcastPresenceUpdate(socket.userId, status);
    });

    // Notification events
    socket.on('notifications:mark_read', async (notificationId: string) => {
      try {
        await notificationService.markAsRead(notificationId, socket.userId);
        socket.emit('notifications:marked_read', { notificationId });
      } catch (error) {
        socket.emit('error', { message: 'Failed to mark notification as read' });
      }
    });

    socket.on('notifications:mark_all_read', async () => {
      try {
        await notificationService.markAllAsRead(socket.userId);
        socket.emit('notifications:all_marked_read');
      } catch (error) {
        socket.emit('error', { message: 'Failed to mark all notifications as read' });
      }
    });
  }

  private async sendInitialData(socket: AuthenticatedSocket) {
    try {
      // Send unread notification count
      const unreadCount = await notificationService.getUnreadCount(socket.userId);
      socket.emit('notifications:unread_count', { count: unreadCount });

      // Send online team members
      const onlineTeamMembers = await this.getOnlineTeamMembers(socket.userId);
      socket.emit('presence:team_members', { onlineMembers: onlineTeamMembers });
    } catch (error) {
      logger.error('Error sending initial data:', error);
    }
  }

  private handleDisconnection(socket: AuthenticatedSocket) {
    const userId = socket.userId;
    
    logger.info(`User ${userId} disconnected from WebSocket`);

    // Remove from connected users
    if (this.connectedUsers.has(userId)) {
      this.connectedUsers.get(userId)!.delete(socket.id);
      
      // If no more connections for this user, remove from map
      if (this.connectedUsers.get(userId)!.size === 0) {
        this.connectedUsers.delete(userId);
        this.userRooms.delete(userId);
        
        // Broadcast offline status
        this.broadcastPresenceUpdate(userId, 'offline');
      }
    }
  }

  private async getOnlineTeamMembers(userId: string): Promise<string[]> {
    try {
      const userTeams = await prisma.teamMember.findMany({
        where: { userId },
        select: { teamId: true },
      });

      const teamIds = userTeams.map(t => t.teamId);

      const teamMembers = await prisma.teamMember.findMany({
        where: {
          teamId: { in: teamIds },
          userId: { not: userId },
        },
        select: { userId: true },
      });

      const memberIds = teamMembers.map(m => m.userId);
      return memberIds.filter(id => this.connectedUsers.has(id));
    } catch (error) {
      logger.error('Error getting online team members:', error);
      return [];
    }
  }

  private broadcastPresenceUpdate(userId: string, status: string) {
    if (!this.userRooms.has(userId)) return;

    const userRoomSet = this.userRooms.get(userId)!;
    for (const room of userRoomSet) {
      this.io?.to(room).emit('presence:user_status_changed', {
        userId,
        status,
        timestamp: new Date(),
      });
    }
  }

  // Public methods for other services to emit events

  public emitToUser(userId: string, event: string, data: any) {
    if (!this.io) return;
    this.io.to(`user:${userId}`).emit(event, data);
  }

  public emitToTeam(teamId: string, event: string, data: any) {
    if (!this.io) return;
    this.io.to(`team:${teamId}`).emit(event, data);
  }

  public emitToProject(projectId: string, event: string, data: any) {
    if (!this.io) return;
    this.io.to(`project:${projectId}`).emit(event, data);
  }

  public emitToTask(taskId: string, event: string, data: any) {
    if (!this.io) return;
    this.io.to(`task:${taskId}`).emit(event, data);
  }

  public emitToMeeting(meetingId: string, event: string, data: any) {
    if (!this.io) return;
    this.io.to(`meeting:${meetingId}`).emit(event, data);
  }

  public broadcastNotification(userId: string, notification: any) {
    this.emitToUser(userId, 'notification:new', notification);
  }

  public isUserOnline(userId: string): boolean {
    return this.connectedUsers.has(userId);
  }

  public getOnlineUsersCount(): number {
    return this.connectedUsers.size;
  }

  public getConnectedSocketsForUser(userId: string): string[] {
    return Array.from(this.connectedUsers.get(userId) || []);
  }
}

export const websocketService = new WebSocketService();
