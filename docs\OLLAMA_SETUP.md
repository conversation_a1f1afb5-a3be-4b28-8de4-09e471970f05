# Ollama Setup Guide for CollabFlow

This guide will help you set up <PERSON><PERSON><PERSON> as the AI provider for CollabFlow, replacing OpenAI with a free, open-source alternative that runs locally.

## What is Ollama?

Ollama is a tool that allows you to run large language models locally on your machine. It provides:
- **Zero API costs** - No per-request charges
- **Privacy** - All data stays on your machine
- **Offline capability** - Works without internet connection
- **Multiple model support** - Llama 2, Code Llama, Mistral, and more

## Installation

### Windows

1. Download Ollama from [https://ollama.ai/download](https://ollama.ai/download)
2. Run the installer
3. Ollama will start automatically and run in the background

### macOS

```bash
# Using Homebrew
brew install ollama

# Or download from https://ollama.ai/download
```

### Linux

```bash
curl -fsSL https://ollama.ai/install.sh | sh
```

### Docker

```bash
docker run -d -v ollama:/root/.ollama -p 11434:11434 --name ollama ollama/ollama
```

## Recommended Models for CollabFlow

### For General Use (Recommended)

**Llama 2 7B** - Best balance of performance and resource usage
```bash
ollama pull llama2:7b
```

**Mistral 7B** - Excellent for reasoning tasks
```bash
ollama pull mistral:7b
```

### For Code-Related Tasks

**Code Llama 7B** - Optimized for code analysis
```bash
ollama pull codellama:7b
```

### For Resource-Constrained Systems

**Llama 2 3B** - Lighter model for lower-end hardware
```bash
ollama pull llama2:3b
```

## System Requirements

### Minimum Requirements
- **RAM**: 8GB (for 7B models)
- **Storage**: 4GB free space per model
- **CPU**: Modern multi-core processor

### Recommended Requirements
- **RAM**: 16GB or more
- **Storage**: 10GB+ free space
- **CPU**: 8+ cores
- **GPU**: Optional but significantly improves performance

## Configuration

### Environment Variables

Update your `.env` file with the following configuration:

```env
# AI Provider Configuration
AI_PROVIDER=ollama
AI_TIMEOUT=30000
AI_MAX_RETRIES=3

# Ollama Configuration
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_MODEL=llama2:7b

# Optional: Keep OpenAI as fallback
# OPENAI_API_KEY=your-key-here
# OPENAI_MODEL=gpt-3.5-turbo
```

### Model Selection Guide

Choose your model based on your use case:

| Model | Size | RAM Required | Best For |
|-------|------|--------------|----------|
| llama2:3b | 2GB | 4GB+ | Light usage, testing |
| llama2:7b | 4GB | 8GB+ | General purpose (recommended) |
| llama2:13b | 7GB | 16GB+ | High accuracy needs |
| mistral:7b | 4GB | 8GB+ | Reasoning, analysis |
| codellama:7b | 4GB | 8GB+ | Code-related tasks |

## Setup Steps

### 1. Install Ollama
Follow the installation instructions above for your operating system.

### 2. Pull Required Models
```bash
# Pull the recommended model
ollama pull llama2:7b

# Optional: Pull additional models
ollama pull mistral:7b
ollama pull codellama:7b
```

### 3. Verify Installation
```bash
# Check if Ollama is running
curl http://localhost:11434/api/tags

# Test a simple completion
curl http://localhost:11434/api/generate -d '{
  "model": "llama2:7b",
  "prompt": "Hello, how are you?",
  "stream": false
}'
```

### 4. Update CollabFlow Configuration
1. Copy `.env.example` to `.env` if you haven't already
2. Update the AI provider settings as shown above
3. Restart your CollabFlow backend

### 5. Test Integration
Visit `http://localhost:3001/api/health/ai` to check if the AI service is working properly.

## Performance Optimization

### GPU Acceleration (Optional)

If you have an NVIDIA GPU, you can enable GPU acceleration:

1. Install NVIDIA Container Toolkit (for Docker)
2. Use GPU-enabled Ollama:
```bash
docker run -d --gpus=all -v ollama:/root/.ollama -p 11434:11434 --name ollama ollama/ollama
```

### Memory Management

- **Concurrent requests**: Ollama handles one request at a time by default
- **Model switching**: Models are loaded on-demand and cached in memory
- **Memory cleanup**: Unused models are automatically unloaded after inactivity

### Performance Tuning

You can adjust these parameters in your environment:

```env
# Increase timeout for slower hardware
AI_TIMEOUT=60000

# Reduce retries for faster failure detection
AI_MAX_RETRIES=1
```

## Troubleshooting

### Common Issues

**1. "Connection refused" error**
- Ensure Ollama is running: `ollama serve`
- Check if port 11434 is available
- Verify firewall settings

**2. "Model not found" error**
- Pull the required model: `ollama pull llama2:7b`
- Check available models: `ollama list`

**3. Slow responses**
- Use a smaller model (3b instead of 7b)
- Increase AI_TIMEOUT in your .env file
- Consider GPU acceleration

**4. High memory usage**
- Use smaller models
- Limit concurrent requests
- Monitor system resources

### Health Checks

CollabFlow provides several health check endpoints:

```bash
# Overall health
curl http://localhost:3001/api/health

# Detailed health including AI
curl http://localhost:3001/api/health/detailed

# AI-specific health
curl http://localhost:3001/api/health/ai
```

### Logs

Check CollabFlow logs for AI-related issues:
```bash
# In development
npm run dev

# Check specific AI logs
grep -i "ollama\|ai" logs/app.log
```

## Model Management

### Updating Models
```bash
# Update to latest version
ollama pull llama2:7b

# List installed models
ollama list

# Remove unused models
ollama rm old-model-name
```

### Switching Models
Update your `.env` file and restart the application:
```env
OLLAMA_MODEL=mistral:7b
```

## Security Considerations

- Ollama runs locally, so no data leaves your machine
- Default configuration only accepts local connections
- For production deployments, consider network security
- Regular model updates for security patches

## Next Steps

1. **Test the integration** with CollabFlow's AI features
2. **Monitor performance** and adjust model selection if needed
3. **Set up monitoring** for production deployments
4. **Consider GPU acceleration** for better performance
5. **Implement backup strategies** for critical deployments

## Support

- **Ollama Documentation**: [https://github.com/jmorganca/ollama](https://github.com/jmorganca/ollama)
- **Model Library**: [https://ollama.ai/library](https://ollama.ai/library)
- **CollabFlow Issues**: Check the project's GitHub issues page

---

**Note**: This setup eliminates OpenAI API costs while providing powerful AI capabilities for your team coordination platform. The initial setup may take some time to download models, but subsequent usage will be fast and cost-free.
