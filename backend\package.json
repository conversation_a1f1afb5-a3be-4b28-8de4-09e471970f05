{"name": "collabflow-backend", "version": "1.0.0", "description": "CollabFlow Backend API - AI-powered team coordination platform with open-source AI integration", "main": "dist/app.js", "scripts": {"dev": "nodemon src/app.ts", "build": "tsc", "start": "node dist/app.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:unit": "jest --testPathPattern=\"__tests__/services\"", "test:integration": "jest --testPathPattern=\"__tests__/integration\"", "test:ci": "jest --ci --coverage --watchAll=false --passWithNoTests", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "db:migrate": "npx prisma migrate dev", "db:seed": "npx prisma db seed", "db:studio": "npx prisma studio", "db:generate": "npx prisma generate"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "compression": "^1.7.4", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "@prisma/client": "^5.7.1", "redis": "^4.6.11", "socket.io": "^4.7.4", "node-cron": "^3.0.3", "nodemailer": "^6.9.7", "multer": "^1.4.5-lts.1", "sharp": "^0.33.1", "winston": "^3.11.0", "dotenv": "^16.3.1", "axios": "^1.6.2", "moment": "^2.29.4", "moment-timezone": "^0.5.43", "uuid": "^9.0.1"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/morgan": "^1.9.9", "@types/compression": "^1.7.5", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/passport": "^1.0.16", "@types/passport-jwt": "^3.0.13", "@types/passport-local": "^1.0.38", "@types/node-cron": "^3.0.11", "@types/nodemailer": "^6.4.14", "@types/multer": "^1.4.11", "@types/uuid": "^9.0.7", "@types/node": "^20.10.4", "@types/jest": "^29.5.8", "@types/supertest": "^2.0.16", "typescript": "^5.3.3", "ts-node": "^10.9.1", "nodemon": "^3.0.2", "jest": "^29.7.0", "ts-jest": "^29.1.1", "supertest": "^6.3.3", "jest-mock-extended": "^3.0.5", "eslint": "^8.55.0", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "prisma": "^5.7.1"}, "optionalDependencies": {"openai": "^4.20.1"}, "prisma": {"seed": "ts-node prisma/seed.ts"}, "keywords": ["api", "backend", "team-coordination", "ai-powered", "collaboration"], "author": "HectorTa1989", "license": "MIT"}