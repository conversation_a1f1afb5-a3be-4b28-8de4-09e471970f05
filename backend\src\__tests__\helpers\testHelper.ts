import request from 'supertest';
import jwt from 'jsonwebtoken';
import { Express } from 'express';

export interface TestUser {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  role: 'ADMIN' | 'MEMBER';
  skills?: string[];
}

export interface TestTeam {
  id: string;
  name: string;
  description?: string;
  memberCount: number;
  projectCount: number;
}

export interface TestProject {
  id: string;
  name: string;
  description?: string;
  teamId: string;
  status: string;
  priority: string;
}

export interface TestTask {
  id: string;
  title: string;
  description?: string;
  projectId: string;
  assigneeId?: string;
  status: string;
  priority: string;
  estimatedHours: number;
}

export interface TestMeeting {
  id: string;
  title: string;
  description?: string;
  organizerId: string;
  startTime: Date;
  endTime: Date;
  status: string;
}

export class TestHelper {
  private app: Express;

  constructor(app: Express) {
    this.app = app;
  }

  // Authentication helpers
  generateAccessToken(user: Partial<TestUser>): string {
    return jwt.sign(
      {
        userId: user.id || 'test-user-id',
        email: user.email || '<EMAIL>',
        type: 'access',
      },
      process.env.JWT_SECRET || 'test-secret',
      { expiresIn: '1h' }
    );
  }

  generateRefreshToken(user: Partial<TestUser>): string {
    return jwt.sign(
      {
        userId: user.id || 'test-user-id',
        type: 'refresh',
      },
      process.env.JWT_SECRET || 'test-secret',
      { expiresIn: '7d' }
    );
  }

  // Request helpers with authentication
  authenticatedRequest(user: Partial<TestUser> = {}) {
    const token = this.generateAccessToken(user);
    return request(this.app).set('Authorization', `Bearer ${token}`);
  }

  unauthenticatedRequest() {
    return request(this.app);
  }

  // Mock data generators
  createMockUser(overrides: Partial<TestUser> = {}): TestUser {
    return {
      id: 'test-user-id',
      email: '<EMAIL>',
      firstName: 'Test',
      lastName: 'User',
      role: 'MEMBER',
      skills: ['JavaScript', 'TypeScript'],
      ...overrides,
    };
  }

  createMockAdmin(overrides: Partial<TestUser> = {}): TestUser {
    return this.createMockUser({
      id: 'admin-user-id',
      email: '<EMAIL>',
      firstName: 'Admin',
      lastName: 'User',
      role: 'ADMIN',
      ...overrides,
    });
  }

  createMockTeam(overrides: Partial<TestTeam> = {}): TestTeam {
    return {
      id: 'test-team-id',
      name: 'Test Team',
      description: 'A test team for integration tests',
      memberCount: 3,
      projectCount: 2,
      ...overrides,
    };
  }

  createMockProject(overrides: Partial<TestProject> = {}): TestProject {
    return {
      id: 'test-project-id',
      name: 'Test Project',
      description: 'A test project for integration tests',
      teamId: 'test-team-id',
      status: 'ACTIVE',
      priority: 'HIGH',
      ...overrides,
    };
  }

  createMockTask(overrides: Partial<TestTask> = {}): TestTask {
    return {
      id: 'test-task-id',
      title: 'Test Task',
      description: 'A test task for integration tests',
      projectId: 'test-project-id',
      assigneeId: 'test-user-id',
      status: 'TODO',
      priority: 'MEDIUM',
      estimatedHours: 8,
      ...overrides,
    };
  }

  createMockMeeting(overrides: Partial<TestMeeting> = {}): TestMeeting {
    return {
      id: 'test-meeting-id',
      title: 'Test Meeting',
      description: 'A test meeting for integration tests',
      organizerId: 'test-user-id',
      startTime: new Date(Date.now() + 24 * 60 * 60 * 1000), // Tomorrow
      endTime: new Date(Date.now() + 24 * 60 * 60 * 1000 + 60 * 60 * 1000), // Tomorrow + 1 hour
      status: 'SCHEDULED',
      ...overrides,
    };
  }

  // Validation helpers
  expectValidationError(response: request.Response, field?: string) {
    expect(response.status).toBe(400);
    expect(response.body).toHaveProperty('success', false);
    expect(response.body).toHaveProperty('error', 'Validation failed');
    expect(response.body).toHaveProperty('details');
    expect(Array.isArray(response.body.details)).toBe(true);
    
    if (field) {
      expect(response.body.details.some((detail: any) => 
        detail.path === field || detail.param === field
      )).toBe(true);
    }
  }

  expectAuthenticationError(response: request.Response) {
    expect(response.status).toBe(401);
    expect(response.body).toHaveProperty('success', false);
    expect(response.body.error).toMatch(/unauthorized|authentication/i);
  }

  expectAuthorizationError(response: request.Response) {
    expect(response.status).toBe(403);
    expect(response.body).toHaveProperty('success', false);
    expect(response.body.error).toMatch(/forbidden|authorization|permission/i);
  }

  expectNotFoundError(response: request.Response) {
    expect(response.status).toBe(404);
    expect(response.body).toHaveProperty('success', false);
    expect(response.body.error).toMatch(/not found/i);
  }

  expectSuccessResponse(response: request.Response, data?: any) {
    expect(response.status).toBeLessThan(400);
    expect(response.body).toHaveProperty('success', true);
    
    if (data) {
      expect(response.body).toHaveProperty('data');
      if (typeof data === 'object') {
        expect(response.body.data).toMatchObject(data);
      }
    }
  }

  expectPaginatedResponse(response: request.Response) {
    this.expectSuccessResponse(response);
    expect(response.body).toHaveProperty('meta');
    expect(response.body.meta).toHaveProperty('page');
    expect(response.body.meta).toHaveProperty('limit');
    expect(response.body.meta).toHaveProperty('total');
    expect(response.body.meta).toHaveProperty('totalPages');
  }

  // Database helpers (mocked)
  async cleanupDatabase() {
    // In a real test environment, this would clean up test data
    // For now, we're using mocked data, so this is a no-op
    return Promise.resolve();
  }

  async seedDatabase() {
    // In a real test environment, this would seed test data
    // For now, we're using mocked data, so this is a no-op
    return Promise.resolve();
  }

  // Utility methods
  delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  randomString(length: number = 10): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }

  randomEmail(): string {
    return `test-${this.randomString(8)}@example.com`;
  }

  futureDate(daysFromNow: number = 1): Date {
    return new Date(Date.now() + daysFromNow * 24 * 60 * 60 * 1000);
  }

  pastDate(daysAgo: number = 1): Date {
    return new Date(Date.now() - daysAgo * 24 * 60 * 60 * 1000);
  }
}
