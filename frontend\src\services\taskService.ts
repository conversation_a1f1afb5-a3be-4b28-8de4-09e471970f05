import { apiClient } from './api';

export interface Task {
  id: string;
  title: string;
  description?: string;
  projectId: string;
  assigneeId?: string;
  status: 'TODO' | 'IN_PROGRESS' | 'REVIEW' | 'COMPLETED' | 'CANCELLED';
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  estimatedHours: number;
  actualHours?: number;
  tags: string[];
  dueDate?: string;
  createdAt: string;
  updatedAt: string;
  project?: {
    id: string;
    name: string;
    team: {
      id: string;
      name: string;
    };
  };
  assignee?: {
    id: string;
    firstName: string;
    lastName: string;
    avatar?: string;
    skills?: string[];
  };
  dependencies?: Array<{
    dependency: {
      id: string;
      title: string;
      status: string;
    };
  }>;
  dependents?: Array<{
    task: {
      id: string;
      title: string;
      status: string;
    };
  }>;
}

export interface CreateTaskData {
  title: string;
  description?: string;
  projectId: string;
  assigneeId?: string;
  priority?: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  status?: 'TODO' | 'IN_PROGRESS' | 'REVIEW' | 'COMPLETED' | 'CANCELLED';
  estimatedHours?: number;
  tags?: string[];
  dueDate?: string;
  dependencies?: string[];
}

export interface UpdateTaskData {
  title?: string;
  description?: string;
  assigneeId?: string;
  status?: 'TODO' | 'IN_PROGRESS' | 'REVIEW' | 'COMPLETED' | 'CANCELLED';
  priority?: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  estimatedHours?: number;
  actualHours?: number;
  tags?: string[];
  dueDate?: string;
}

export const taskService = {
  // Get all tasks
  getTasks: async (params?: {
    page?: number;
    limit?: number;
    status?: string;
    priority?: string;
    assigneeId?: string;
    projectId?: string;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  }) => {
    return apiClient.get<Task[]>('/tasks', { params });
  },

  // Create new task
  createTask: async (data: CreateTaskData) => {
    return apiClient.post<Task>('/tasks', data);
  },

  // Get task by ID
  getTaskById: async (taskId: string) => {
    return apiClient.get<Task>(`/tasks/${taskId}`);
  },

  // Update task
  updateTask: async (taskId: string, data: UpdateTaskData) => {
    return apiClient.put<Task>(`/tasks/${taskId}`, data);
  },

  // Delete task
  deleteTask: async (taskId: string) => {
    return apiClient.delete(`/tasks/${taskId}`);
  },

  // Get tasks by project
  getTasksByProject: async (projectId: string, params?: {
    status?: string;
    assigneeId?: string;
    priority?: string;
  }) => {
    return apiClient.get<Task[]>('/tasks', {
      params: { projectId, ...params },
    });
  },

  // Get tasks assigned to user
  getMyTasks: async (params?: {
    status?: string;
    priority?: string;
    dueDate?: string;
  }) => {
    return apiClient.get<Task[]>('/tasks/my-tasks', { params });
  },

  // Update task status
  updateTaskStatus: async (
    taskId: string,
    status: 'TODO' | 'IN_PROGRESS' | 'REVIEW' | 'COMPLETED' | 'CANCELLED'
  ) => {
    return apiClient.patch(`/tasks/${taskId}/status`, { status });
  },

  // Add task dependency
  addDependency: async (taskId: string, dependencyId: string) => {
    return apiClient.post(`/tasks/${taskId}/dependencies`, { dependencyId });
  },

  // Remove task dependency
  removeDependency: async (taskId: string, dependencyId: string) => {
    return apiClient.delete(`/tasks/${taskId}/dependencies/${dependencyId}`);
  },

  // Log time on task
  logTime: async (taskId: string, hours: number, description?: string) => {
    return apiClient.post(`/tasks/${taskId}/time-log`, { hours, description });
  },

  // Get task time logs
  getTimeLogs: async (taskId: string) => {
    return apiClient.get(`/tasks/${taskId}/time-logs`);
  },
};
