import { Router, Request, Response } from 'express';
import { validationResult } from 'express-validator';
import { teamService } from '../services/teamService';
import { asyncHandler, CustomError } from '../middleware/errorHandler';
import { AuthenticatedRequest, requireTeamMembership } from '../middleware/auth';
import { prisma } from '../config/database';
import {
  commonValidations,
  teamValidations,
  paginationValidation
} from '../utils/validation';

const router = Router();

// Helper function to handle validation errors
const handleValidationErrors = (req: Request, res: Response, next: any) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errors.array(),
    });
  }
  next();
};

// Get all teams
router.get('/',
  paginationValidation,
  handleValidationErrors,
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const { page, limit, sortBy, sortOrder, search } = req.query;
    const userId = req.user!.id;

    const result = await teamService.getTeams({
      page: page ? parseInt(page as string) : undefined,
      limit: limit ? parseInt(limit as string) : undefined,
      sortBy: sortBy as string,
      sortOrder: sortOrder as 'asc' | 'desc',
      search: search as string,
      userId, // Only show teams the user is a member of
    });

    res.json(result);
  })
);

// Create team
router.post('/',
  teamValidations.create,
  handleValidationErrors,
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const { name, description } = req.body;
    const creatorId = req.user!.id;

    const result = await teamService.createTeam({
      name,
      description,
      creatorId,
    });

    res.status(201).json(result);
  })
);

// Get team by ID
router.get('/:id',
  commonValidations.id,
  requireTeamMembership,
  handleValidationErrors,
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const { id } = req.params;

    const result = await teamService.getTeamById(id);
    res.json(result);
  })
);

// Update team
router.put('/:id',
  commonValidations.id,
  teamValidations.update,
  requireTeamMembership,
  handleValidationErrors,
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const { id } = req.params;
    const { name, description } = req.body;

    // Check if user is team lead or admin
    const teamMember = await prisma.teamMember.findFirst({
      where: {
        teamId: id,
        userId: req.user!.id,
        role: { in: ['LEAD'] },
      },
    });

    if (!teamMember && req.user!.role !== 'ADMIN') {
      throw new CustomError('Only team leads can update team details', 403);
    }

    const result = await teamService.updateTeam(id, {
      name,
      description,
    });

    res.json(result);
  })
);

// Delete team
router.delete('/:id',
  commonValidations.id,
  requireTeamMembership,
  handleValidationErrors,
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const { id } = req.params;

    // Check if user is team lead or admin
    const teamMember = await prisma.teamMember.findFirst({
      where: {
        teamId: id,
        userId: req.user!.id,
        role: { in: ['LEAD'] },
      },
    });

    if (!teamMember && req.user!.role !== 'ADMIN') {
      throw new CustomError('Only team leads can delete teams', 403);
    }

    const result = await teamService.deleteTeam(id);
    res.json(result);
  })
);

// Add team member
router.post('/:id/members',
  commonValidations.id,
  teamValidations.addMember,
  requireTeamMembership,
  handleValidationErrors,
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const { id } = req.params;
    const { userId, role } = req.body;

    // Check if user is team lead or admin
    const teamMember = await prisma.teamMember.findFirst({
      where: {
        teamId: id,
        userId: req.user!.id,
        role: { in: ['LEAD'] },
      },
    });

    if (!teamMember && req.user!.role !== 'ADMIN') {
      throw new CustomError('Only team leads can add members', 403);
    }

    const result = await teamService.addMember(id, { userId, role });
    res.status(201).json(result);
  })
);

// Remove team member
router.delete('/:id/members/:userId',
  commonValidations.id,
  [commonValidations.id.customSanitizer((value, { req }) => {
    req.params.userId = value;
    return value;
  })],
  requireTeamMembership,
  handleValidationErrors,
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const { id, userId } = req.params;

    // Check if user is team lead or admin, or removing themselves
    const teamMember = await prisma.teamMember.findFirst({
      where: {
        teamId: id,
        userId: req.user!.id,
        role: { in: ['LEAD'] },
      },
    });

    if (!teamMember && req.user!.role !== 'ADMIN' && userId !== req.user!.id) {
      throw new CustomError('Only team leads can remove members', 403);
    }

    const result = await teamService.removeMember(id, userId);
    res.json(result);
  })
);

// Update member role
router.patch('/:id/members/:userId/role',
  commonValidations.id,
  [
    commonValidations.id.customSanitizer((value, { req }) => {
      req.params.userId = value;
      return value;
    }),
    commonValidations.status('role', ['LEAD', 'SENIOR', 'MEMBER'])
  ],
  requireTeamMembership,
  handleValidationErrors,
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const { id, userId } = req.params;
    const { role } = req.body;

    // Check if user is team lead or admin
    const teamMember = await prisma.teamMember.findFirst({
      where: {
        teamId: id,
        userId: req.user!.id,
        role: { in: ['LEAD'] },
      },
    });

    if (!teamMember && req.user!.role !== 'ADMIN') {
      throw new CustomError('Only team leads can update member roles', 403);
    }

    const result = await teamService.updateMemberRole(id, userId, role);
    res.json(result);
  })
);

export default router;
