import { logger } from '../config/logger';
import { CustomError } from '../middleware/errorHandler';
import { aiProviderManager } from './ai/AIProviderFactory';
import {
  MeetingNecessityAnalysis,
  TaskPriorityAnalysis,
  TeamCapacityAnalysis,
  ConflictResolution,
} from './ai/types';

// Re-export types for backward compatibility
export {
  MeetingNecessityAnalysis,
  TaskPriorityAnalysis,
  TeamCapacityAnalysis,
  ConflictResolution,
};

class AIService {
  private initialized = false;

  async initialize(): Promise<void> {
    if (!this.initialized) {
      await aiProviderManager.initialize();
      this.initialized = true;
      logger.info('AI Service initialized');
    }
  }

  private async ensureInitialized(): Promise<void> {
    if (!this.initialized) {
      await this.initialize();
    }
  }

  async healthCheck(): Promise<any> {
    await this.ensureInitialized();
    return await aiProviderManager.healthCheck();
  }

  async analyzeMeetingNecessity(
    title: string,
    description: string,
    participants: string[],
    duration: number,
    context?: string
  ): Promise<MeetingNecessityAnalysis> {
    await this.ensureInitialized();

    try {
      return await aiProviderManager.analyzeMeetingNecessity(
        title,
        description,
        participants,
        duration,
        context
      );
    } catch (error) {
      logger.error('AI meeting analysis failed:', error);
      throw new CustomError('Meeting analysis failed', 500);
    }
  }

  async analyzeTaskPriority(
    title: string,
    description: string,
    dueDate?: Date,
    dependencies?: string[],
    projectContext?: string
  ): Promise<TaskPriorityAnalysis> {
    await this.ensureInitialized();

    try {
      return await aiProviderManager.analyzeTaskPriority(
        title,
        description,
        dueDate,
        dependencies,
        projectContext
      );
    } catch (error) {
      logger.error('AI task analysis failed:', error);
      throw new CustomError('Task analysis failed', 500);
    }
  }

  async analyzeTeamCapacity(
    teamMembers: Array<{
      id: string;
      name: string;
      skills: string[];
      currentTasks: number;
      availability: number; // hours per week
    }>,
    upcomingTasks: Array<{
      title: string;
      estimatedHours: number;
      requiredSkills: string[];
    }>
  ): Promise<TeamCapacityAnalysis> {
    await this.ensureInitialized();

    try {
      return await aiProviderManager.analyzeTeamCapacity(teamMembers, upcomingTasks);
    } catch (error) {
      logger.error('Team capacity analysis failed:', error);
      throw new CustomError('Failed to analyze team capacity', 500);
    }
  }

  async detectConflicts(
    teamId: string,
    startDate: Date,
    endDate: Date,
    meetings: any[],
    tasks: any[]
  ): Promise<ConflictResolution> {
    await this.ensureInitialized();

    try {
      return await aiProviderManager.detectConflicts(teamId, startDate, endDate, meetings, tasks);
    } catch (error) {
      logger.error('Conflict detection failed:', error);
      throw new CustomError('Failed to detect conflicts', 500);
    }
  }

}

export const aiService = new AIService();
