import OpenAI from 'openai';
import { logger } from '../config/logger';
import { CustomError } from '../middleware/errorHandler';

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

export interface MeetingNecessityAnalysis {
  score: number; // 0-100
  reasoning: string;
  alternatives: string[];
  recommendations: string[];
}

export interface TaskPriorityAnalysis {
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  reasoning: string;
  estimatedHours: number;
  dependencies: string[];
}

export interface TeamCapacityAnalysis {
  overallUtilization: number; // 0-100
  bottlenecks: Array<{
    userId: string;
    utilization: number;
    skills: string[];
  }>;
  recommendations: string[];
}

export interface ConflictResolution {
  conflicts: Array<{
    type: 'schedule' | 'resource' | 'priority';
    description: string;
    severity: 'low' | 'medium' | 'high';
  }>;
  resolutions: Array<{
    conflictType: string;
    solution: string;
    impact: string;
  }>;
}

class AIService {
  private isEnabled(): boolean {
    return !!process.env.OPENAI_API_KEY;
  }

  async analyzeMeetingNecessity(
    title: string,
    description: string,
    participants: string[],
    duration: number,
    context?: string
  ): Promise<MeetingNecessityAnalysis> {
    if (!this.isEnabled()) {
      // Fallback algorithm when AI is not available
      return this.fallbackMeetingAnalysis(title, description, participants, duration);
    }

    try {
      const prompt = `
Analyze the necessity of this meeting and provide a score from 0-100:

Meeting Details:
- Title: ${title}
- Description: ${description}
- Participants: ${participants.length} people
- Duration: ${duration} minutes
- Context: ${context || 'None provided'}

Consider:
1. Can this be resolved asynchronously?
2. Is real-time discussion necessary?
3. Are all participants essential?
4. Is the duration appropriate?

Provide your analysis in JSON format:
{
  "score": number (0-100),
  "reasoning": "detailed explanation",
  "alternatives": ["alternative 1", "alternative 2"],
  "recommendations": ["recommendation 1", "recommendation 2"]
}
`;

      const response = await openai.chat.completions.create({
        model: 'gpt-3.5-turbo',
        messages: [
          {
            role: 'system',
            content: 'You are an expert in team productivity and meeting optimization. Provide practical, actionable insights.',
          },
          {
            role: 'user',
            content: prompt,
          },
        ],
        temperature: 0.3,
        max_tokens: 500,
      });

      const content = response.choices[0]?.message?.content;
      if (!content) {
        throw new Error('No response from AI service');
      }

      const analysis = JSON.parse(content);
      
      // Validate response structure
      if (typeof analysis.score !== 'number' || analysis.score < 0 || analysis.score > 100) {
        throw new Error('Invalid score in AI response');
      }

      return analysis;
    } catch (error) {
      logger.error('AI meeting analysis failed:', error);
      // Fallback to rule-based analysis
      return this.fallbackMeetingAnalysis(title, description, participants, duration);
    }
  }

  async analyzeTaskPriority(
    title: string,
    description: string,
    dueDate?: Date,
    dependencies?: string[],
    projectContext?: string
  ): Promise<TaskPriorityAnalysis> {
    if (!this.isEnabled()) {
      return this.fallbackTaskAnalysis(title, description, dueDate, dependencies);
    }

    try {
      const prompt = `
Analyze this task and determine its priority level:

Task Details:
- Title: ${title}
- Description: ${description}
- Due Date: ${dueDate ? dueDate.toISOString() : 'Not specified'}
- Dependencies: ${dependencies?.length || 0} tasks
- Project Context: ${projectContext || 'None provided'}

Provide analysis in JSON format:
{
  "priority": "LOW|MEDIUM|HIGH|CRITICAL",
  "reasoning": "detailed explanation",
  "estimatedHours": number,
  "dependencies": ["dependency 1", "dependency 2"]
}
`;

      const response = await openai.chat.completions.create({
        model: 'gpt-3.5-turbo',
        messages: [
          {
            role: 'system',
            content: 'You are an expert project manager. Analyze tasks objectively based on impact, urgency, and complexity.',
          },
          {
            role: 'user',
            content: prompt,
          },
        ],
        temperature: 0.2,
        max_tokens: 400,
      });

      const content = response.choices[0]?.message?.content;
      if (!content) {
        throw new Error('No response from AI service');
      }

      return JSON.parse(content);
    } catch (error) {
      logger.error('AI task analysis failed:', error);
      return this.fallbackTaskAnalysis(title, description, dueDate, dependencies);
    }
  }

  async analyzeTeamCapacity(
    teamMembers: Array<{
      id: string;
      name: string;
      skills: string[];
      currentTasks: number;
      availability: number; // hours per week
    }>,
    upcomingTasks: Array<{
      title: string;
      estimatedHours: number;
      requiredSkills: string[];
    }>
  ): Promise<TeamCapacityAnalysis> {
    try {
      // Calculate utilization for each team member
      const analysis: TeamCapacityAnalysis = {
        overallUtilization: 0,
        bottlenecks: [],
        recommendations: [],
      };

      let totalUtilization = 0;

      for (const member of teamMembers) {
        // Simple utilization calculation (can be enhanced)
        const utilization = Math.min((member.currentTasks * 8) / member.availability * 100, 100);
        totalUtilization += utilization;

        if (utilization > 80) {
          analysis.bottlenecks.push({
            userId: member.id,
            utilization,
            skills: member.skills,
          });
        }
      }

      analysis.overallUtilization = totalUtilization / teamMembers.length;

      // Generate recommendations
      if (analysis.overallUtilization > 85) {
        analysis.recommendations.push('Team is at high capacity. Consider redistributing tasks or extending deadlines.');
      }

      if (analysis.bottlenecks.length > 0) {
        analysis.recommendations.push(`${analysis.bottlenecks.length} team members are overloaded. Consider task reallocation.`);
      }

      // Skill gap analysis
      const teamSkills = new Set(teamMembers.flatMap(m => m.skills));
      const requiredSkills = new Set(upcomingTasks.flatMap(t => t.requiredSkills));
      const missingSkills = [...requiredSkills].filter(skill => !teamSkills.has(skill));

      if (missingSkills.length > 0) {
        analysis.recommendations.push(`Missing skills: ${missingSkills.join(', ')}. Consider training or hiring.`);
      }

      return analysis;
    } catch (error) {
      logger.error('Team capacity analysis failed:', error);
      throw new CustomError('Failed to analyze team capacity', 500);
    }
  }

  async detectConflicts(
    meetings: Array<{
      id: string;
      title: string;
      startTime: Date;
      endTime: Date;
      participants: string[];
    }>,
    tasks: Array<{
      id: string;
      title: string;
      assigneeId: string;
      dueDate: Date;
      priority: string;
    }>
  ): Promise<ConflictResolution> {
    try {
      const conflicts: ConflictResolution['conflicts'] = [];
      const resolutions: ConflictResolution['resolutions'] = [];

      // Schedule conflicts
      for (let i = 0; i < meetings.length; i++) {
        for (let j = i + 1; j < meetings.length; j++) {
          const meeting1 = meetings[i];
          const meeting2 = meetings[j];

          // Check for overlapping times and shared participants
          const hasOverlap = meeting1.startTime < meeting2.endTime && meeting2.startTime < meeting1.endTime;
          const sharedParticipants = meeting1.participants.filter(p => meeting2.participants.includes(p));

          if (hasOverlap && sharedParticipants.length > 0) {
            conflicts.push({
              type: 'schedule',
              description: `Meetings "${meeting1.title}" and "${meeting2.title}" overlap with shared participants`,
              severity: 'high',
            });

            resolutions.push({
              conflictType: 'schedule',
              solution: `Reschedule one of the meetings or remove shared participants`,
              impact: 'Prevents double-booking and ensures full participation',
            });
          }
        }
      }

      // Resource conflicts (multiple high-priority tasks for same person)
      const tasksByAssignee = tasks.reduce((acc, task) => {
        if (!acc[task.assigneeId]) acc[task.assigneeId] = [];
        acc[task.assigneeId].push(task);
        return acc;
      }, {} as Record<string, typeof tasks>);

      Object.entries(tasksByAssignee).forEach(([assigneeId, assigneeTasks]) => {
        const highPriorityTasks = assigneeTasks.filter(t => t.priority === 'HIGH' || t.priority === 'CRITICAL');
        
        if (highPriorityTasks.length > 2) {
          conflicts.push({
            type: 'resource',
            description: `Assignee has ${highPriorityTasks.length} high-priority tasks`,
            severity: 'medium',
          });

          resolutions.push({
            conflictType: 'resource',
            solution: 'Redistribute some tasks or adjust priorities',
            impact: 'Prevents burnout and ensures quality delivery',
          });
        }
      });

      return { conflicts, resolutions };
    } catch (error) {
      logger.error('Conflict detection failed:', error);
      throw new CustomError('Failed to detect conflicts', 500);
    }
  }

  // Fallback methods when AI is not available
  private fallbackMeetingAnalysis(
    title: string,
    description: string,
    participants: string[],
    duration: number
  ): MeetingNecessityAnalysis {
    let score = 50; // Base score

    // Adjust based on participants
    if (participants.length > 5) score -= 10;
    if (participants.length < 3) score += 10;

    // Adjust based on duration
    if (duration > 60) score -= 15;
    if (duration < 30) score += 10;

    // Adjust based on keywords
    const urgentKeywords = ['urgent', 'critical', 'emergency', 'asap'];
    const asyncKeywords = ['update', 'status', 'report', 'information'];

    if (urgentKeywords.some(keyword => 
      title.toLowerCase().includes(keyword) || description.toLowerCase().includes(keyword)
    )) {
      score += 20;
    }

    if (asyncKeywords.some(keyword => 
      title.toLowerCase().includes(keyword) || description.toLowerCase().includes(keyword)
    )) {
      score -= 15;
    }

    score = Math.max(0, Math.min(100, score));

    return {
      score,
      reasoning: 'Analysis based on meeting characteristics and heuristics',
      alternatives: score < 60 ? ['Email update', 'Slack discussion', 'Shared document'] : [],
      recommendations: [
        score > 80 ? 'Meeting is highly necessary' : 'Consider if meeting is essential',
        participants.length > 5 ? 'Consider reducing participants' : 'Participant count is appropriate',
      ],
    };
  }

  private fallbackTaskAnalysis(
    title: string,
    description: string,
    dueDate?: Date,
    dependencies?: string[]
  ): TaskPriorityAnalysis {
    let priority: TaskPriorityAnalysis['priority'] = 'MEDIUM';
    let estimatedHours = 8; // Default estimate

    // Analyze urgency based on due date
    if (dueDate) {
      const daysUntilDue = Math.ceil((dueDate.getTime() - Date.now()) / (1000 * 60 * 60 * 24));
      if (daysUntilDue <= 1) priority = 'CRITICAL';
      else if (daysUntilDue <= 3) priority = 'HIGH';
      else if (daysUntilDue <= 7) priority = 'MEDIUM';
      else priority = 'LOW';
    }

    // Adjust based on dependencies
    if (dependencies && dependencies.length > 0) {
      if (priority === 'LOW') priority = 'MEDIUM';
      else if (priority === 'MEDIUM') priority = 'HIGH';
    }

    // Estimate hours based on complexity keywords
    const complexKeywords = ['implement', 'develop', 'create', 'build', 'design'];
    const simpleKeywords = ['update', 'fix', 'review', 'test'];

    if (complexKeywords.some(keyword => 
      title.toLowerCase().includes(keyword) || description.toLowerCase().includes(keyword)
    )) {
      estimatedHours = 16;
    } else if (simpleKeywords.some(keyword => 
      title.toLowerCase().includes(keyword) || description.toLowerCase().includes(keyword)
    )) {
      estimatedHours = 4;
    }

    return {
      priority,
      reasoning: 'Priority determined based on due date, dependencies, and task complexity',
      estimatedHours,
      dependencies: dependencies || [],
    };
  }
}

export const aiService = new AIService();
