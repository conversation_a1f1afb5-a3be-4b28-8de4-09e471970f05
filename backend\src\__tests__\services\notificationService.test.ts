import { jest } from '@jest/globals';
import { notificationService } from '../../services/notificationService';
import { prisma } from '../../config/database';
import { redisClient } from '../../config/redis';

const mockPrisma = prisma as jest.Mocked<typeof prisma>;
const mockRedis = redisClient as jest.Mocked<typeof redisClient>;

describe('NotificationService', () => {
  describe('createNotification', () => {
    it('should create a notification successfully', async () => {
      // Arrange
      const notificationData = {
        userId: 'user1',
        type: 'TASK_ASSIGNED' as const,
        title: 'Task Assigned',
        message: 'You have been assigned a new task',
        data: { taskId: 'task1' },
      };

      const mockNotification = {
        id: 'notification1',
        ...notificationData,
        read: false,
        createdAt: new Date(),
      };

      mockPrisma.notification.create.mockResolvedValue(mockNotification);

      // Act
      const result = await notificationService.createNotification(notificationData);

      // Assert
      expect(result).toEqual(mockNotification);
      expect(mockPrisma.notification.create).toHaveBeenCalledWith({
        data: notificationData,
      });
    });

    it('should handle database errors gracefully', async () => {
      // Arrange
      const notificationData = {
        userId: 'user1',
        type: 'TASK_ASSIGNED' as const,
        title: 'Task Assigned',
        message: 'You have been assigned a new task',
      };

      mockPrisma.notification.create.mockRejectedValue(new Error('Database error'));

      // Act & Assert
      await expect(notificationService.createNotification(notificationData))
        .rejects.toThrow('Failed to create notification');
    });
  });

  describe('createBulkNotifications', () => {
    it('should create multiple notifications successfully', async () => {
      // Arrange
      const bulkData = {
        userIds: ['user1', 'user2', 'user3'],
        type: 'MEETING_REMINDER' as const,
        title: 'Meeting Reminder',
        message: 'Meeting starts in 15 minutes',
        data: { meetingId: 'meeting1' },
      };

      mockPrisma.notification.createMany.mockResolvedValue({ count: 3 });
      mockRedis.cacheSet.mockResolvedValue(undefined);

      // Act
      const result = await notificationService.createBulkNotifications(bulkData);

      // Assert
      expect(result.count).toBe(3);
      expect(mockPrisma.notification.createMany).toHaveBeenCalledWith({
        data: bulkData.userIds.map(userId => ({
          userId,
          type: bulkData.type,
          title: bulkData.title,
          message: bulkData.message,
          data: bulkData.data,
        })),
      });
    });
  });

  describe('getUserNotifications', () => {
    it('should return paginated notifications with metadata', async () => {
      // Arrange
      const userId = 'user1';
      const mockNotifications = [
        { ...global.mockNotification, id: 'notif1' },
        { ...global.mockNotification, id: 'notif2' },
      ];

      mockPrisma.notification.findMany.mockResolvedValue(mockNotifications);
      mockPrisma.notification.count
        .mockResolvedValueOnce(25) // total count
        .mockResolvedValueOnce(5); // unread count

      // Act
      const result = await notificationService.getUserNotifications(userId, 1, 10);

      // Assert
      expect(result.notifications).toEqual(mockNotifications);
      expect(result.pagination).toEqual({
        page: 1,
        limit: 10,
        total: 25,
        totalPages: 3,
        hasNextPage: true,
        hasPrevPage: false,
      });
      expect(result.unreadCount).toBe(5);
    });

    it('should handle pagination correctly', async () => {
      // Arrange
      const userId = 'user1';
      mockPrisma.notification.findMany.mockResolvedValue([]);
      mockPrisma.notification.count.mockResolvedValue(0);

      // Act
      const result = await notificationService.getUserNotifications(userId, 2, 5);

      // Assert
      expect(mockPrisma.notification.findMany).toHaveBeenCalledWith({
        where: { userId },
        orderBy: { createdAt: 'desc' },
        skip: 5, // (page - 1) * limit
        take: 5,
      });
    });
  });

  describe('markAsRead', () => {
    it('should mark notification as read successfully', async () => {
      // Arrange
      const notificationId = 'notif1';
      const userId = 'user1';

      mockPrisma.notification.updateMany.mockResolvedValue({ count: 1 });
      mockPrisma.notification.count.mockResolvedValue(3);
      mockRedis.cacheSet.mockResolvedValue(undefined);

      // Act
      const result = await notificationService.markAsRead(notificationId, userId);

      // Assert
      expect(result.count).toBe(1);
      expect(mockPrisma.notification.updateMany).toHaveBeenCalledWith({
        where: { id: notificationId, userId },
        data: { read: true },
      });
    });

    it('should throw error if notification not found', async () => {
      // Arrange
      const notificationId = 'nonexistent';
      const userId = 'user1';

      mockPrisma.notification.updateMany.mockResolvedValue({ count: 0 });

      // Act & Assert
      await expect(notificationService.markAsRead(notificationId, userId))
        .rejects.toThrow('Notification not found');
    });
  });

  describe('markAllAsRead', () => {
    it('should mark all unread notifications as read', async () => {
      // Arrange
      const userId = 'user1';

      mockPrisma.notification.updateMany.mockResolvedValue({ count: 5 });
      mockPrisma.notification.count.mockResolvedValue(0);
      mockRedis.cacheSet.mockResolvedValue(undefined);

      // Act
      const result = await notificationService.markAllAsRead(userId);

      // Assert
      expect(result.count).toBe(5);
      expect(mockPrisma.notification.updateMany).toHaveBeenCalledWith({
        where: { userId, read: false },
        data: { read: true },
      });
    });
  });

  describe('getUnreadCount', () => {
    it('should return cached unread count if available', async () => {
      // Arrange
      const userId = 'user1';
      mockRedis.cacheGet.mockResolvedValue('7');

      // Act
      const result = await notificationService.getUnreadCount(userId);

      // Assert
      expect(result).toBe(7);
      expect(mockRedis.cacheGet).toHaveBeenCalledWith(`user:${userId}:unread_notifications`);
      expect(mockPrisma.notification.count).not.toHaveBeenCalled();
    });

    it('should fetch from database and cache if not in cache', async () => {
      // Arrange
      const userId = 'user1';
      mockRedis.cacheGet.mockResolvedValue(null);
      mockPrisma.notification.count.mockResolvedValue(3);
      mockRedis.cacheSet.mockResolvedValue(undefined);

      // Act
      const result = await notificationService.getUnreadCount(userId);

      // Assert
      expect(result).toBe(3);
      expect(mockPrisma.notification.count).toHaveBeenCalledWith({
        where: { userId, read: false },
      });
      expect(mockRedis.cacheSet).toHaveBeenCalledWith(
        `user:${userId}:unread_notifications`,
        '3',
        3600
      );
    });

    it('should return 0 on error to prevent UI issues', async () => {
      // Arrange
      const userId = 'user1';
      mockRedis.cacheGet.mockRejectedValue(new Error('Redis error'));

      // Act
      const result = await notificationService.getUnreadCount(userId);

      // Assert
      expect(result).toBe(0);
    });
  });

  describe('notifyTaskAssigned', () => {
    it('should create task assignment notification', async () => {
      // Arrange
      const taskId = 'task1';
      const assigneeId = 'user2';
      const assignedBy = 'user1';

      const mockTask = {
        id: taskId,
        title: 'Test Task',
        projectId: 'project1',
        project: { name: 'Test Project' },
      };

      const mockAssigner = {
        id: assignedBy,
        firstName: 'John',
        lastName: 'Doe',
      };

      mockPrisma.task.findUnique.mockResolvedValue(mockTask as any);
      mockPrisma.user.findUnique.mockResolvedValue(mockAssigner as any);
      mockPrisma.notification.create.mockResolvedValue(global.mockNotification as any);

      // Act
      await notificationService.notifyTaskAssigned(taskId, assigneeId, assignedBy);

      // Assert
      expect(mockPrisma.notification.create).toHaveBeenCalledWith({
        data: {
          userId: assigneeId,
          type: 'TASK_ASSIGNED',
          title: 'New Task Assigned',
          message: 'John Doe assigned you a task: "Test Task" in project Test Project',
          data: {
            taskId,
            projectId: 'project1',
            assignedBy,
          },
        },
      });
    });

    it('should handle missing task gracefully', async () => {
      // Arrange
      mockPrisma.task.findUnique.mockResolvedValue(null);

      // Act
      await notificationService.notifyTaskAssigned('nonexistent', 'user1', 'user2');

      // Assert
      expect(mockPrisma.notification.create).not.toHaveBeenCalled();
    });
  });

  describe('notifyMeetingReminder', () => {
    it('should create meeting reminder notifications for all participants', async () => {
      // Arrange
      const meetingId = 'meeting1';
      const mockMeeting = {
        id: meetingId,
        title: 'Team Standup',
        startTime: new Date(),
        participants: [
          { userId: 'user1' },
          { userId: 'user2' },
          { userId: 'user3' },
        ],
      };

      mockPrisma.meeting.findUnique.mockResolvedValue(mockMeeting as any);
      mockPrisma.notification.createMany.mockResolvedValue({ count: 3 });
      mockRedis.cacheSet.mockResolvedValue(undefined);

      // Act
      await notificationService.notifyMeetingReminder(meetingId, 15);

      // Assert
      expect(mockPrisma.notification.createMany).toHaveBeenCalledWith({
        data: [
          {
            userId: 'user1',
            type: 'MEETING_REMINDER',
            title: 'Meeting Reminder',
            message: 'Meeting "Team Standup" starts in 15 minutes',
            data: expect.objectContaining({
              meetingId,
              startTime: mockMeeting.startTime,
            }),
          },
          {
            userId: 'user2',
            type: 'MEETING_REMINDER',
            title: 'Meeting Reminder',
            message: 'Meeting "Team Standup" starts in 15 minutes',
            data: expect.objectContaining({
              meetingId,
              startTime: mockMeeting.startTime,
            }),
          },
          {
            userId: 'user3',
            type: 'MEETING_REMINDER',
            title: 'Meeting Reminder',
            message: 'Meeting "Team Standup" starts in 15 minutes',
            data: expect.objectContaining({
              meetingId,
              startTime: mockMeeting.startTime,
            }),
          },
        ],
      });
    });
  });

  describe('notifyDependencyCompleted', () => {
    it('should notify assignees of dependent tasks', async () => {
      // Arrange
      const completedTaskId = 'task1';
      const mockDependencies = [
        {
          task: {
            id: 'task2',
            title: 'Dependent Task',
            assignee: { id: 'user2' },
          },
          dependency: {
            title: 'Completed Task',
          },
        },
      ];

      mockPrisma.taskDependency.findMany.mockResolvedValue(mockDependencies as any);
      mockPrisma.notification.create.mockResolvedValue(global.mockNotification as any);

      // Act
      await notificationService.notifyDependencyCompleted(completedTaskId);

      // Assert
      expect(mockPrisma.notification.create).toHaveBeenCalledWith({
        data: {
          userId: 'user2',
          type: 'DEPENDENCY_COMPLETED',
          title: 'Dependency Completed',
          message: 'Task "Completed Task" has been completed. You can now work on "Dependent Task"',
          data: {
            taskId: 'task2',
            dependencyId: completedTaskId,
          },
        },
      });
    });

    it('should handle tasks without assignees', async () => {
      // Arrange
      const completedTaskId = 'task1';
      const mockDependencies = [
        {
          task: {
            id: 'task2',
            title: 'Dependent Task',
            assignee: null,
          },
          dependency: {
            title: 'Completed Task',
          },
        },
      ];

      mockPrisma.taskDependency.findMany.mockResolvedValue(mockDependencies as any);

      // Act
      await notificationService.notifyDependencyCompleted(completedTaskId);

      // Assert
      expect(mockPrisma.notification.create).not.toHaveBeenCalled();
    });
  });
});
