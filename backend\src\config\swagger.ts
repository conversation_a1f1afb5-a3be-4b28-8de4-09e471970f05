import swaggerJSDoc from 'swagger-jsdoc';
import swaggerUi from 'swagger-ui-express';
import { Express } from 'express';

const options: swaggerJSDoc.Options = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: 'CollabFlow API',
      version: '1.0.0',
      description: 'AI-powered team coordination platform with open-source AI integration',
      contact: {
        name: 'CollabFlow Team',
        email: '<EMAIL>',
      },
      license: {
        name: 'MIT',
        url: 'https://opensource.org/licenses/MIT',
      },
    },
    servers: [
      {
        url: process.env.API_BASE_URL || 'http://localhost:3001',
        description: 'Development server',
      },
      {
        url: 'https://api.collabflow.dev',
        description: 'Production server',
      },
    ],
    components: {
      securitySchemes: {
        bearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
          description: 'JWT token for authentication',
        },
      },
      schemas: {
        Error: {
          type: 'object',
          properties: {
            success: {
              type: 'boolean',
              example: false,
            },
            error: {
              type: 'string',
              example: 'Error message',
            },
          },
        },
        Success: {
          type: 'object',
          properties: {
            success: {
              type: 'boolean',
              example: true,
            },
            message: {
              type: 'string',
              example: 'Operation completed successfully',
            },
            data: {
              type: 'object',
              description: 'Response data',
            },
          },
        },
        User: {
          type: 'object',
          properties: {
            id: {
              type: 'string',
              example: 'clp123abc456',
            },
            email: {
              type: 'string',
              format: 'email',
              example: '<EMAIL>',
            },
            firstName: {
              type: 'string',
              example: 'John',
            },
            lastName: {
              type: 'string',
              example: 'Doe',
            },
            role: {
              type: 'string',
              enum: ['ADMIN', 'MANAGER', 'MEMBER', 'VIEWER'],
              example: 'MEMBER',
            },
            skills: {
              type: 'array',
              items: {
                type: 'string',
              },
              example: ['JavaScript', 'React', 'Node.js'],
            },
            timezone: {
              type: 'string',
              example: 'UTC',
            },
            createdAt: {
              type: 'string',
              format: 'date-time',
            },
            updatedAt: {
              type: 'string',
              format: 'date-time',
            },
          },
        },
        Team: {
          type: 'object',
          properties: {
            id: {
              type: 'string',
              example: 'clp123abc456',
            },
            name: {
              type: 'string',
              example: 'Development Team',
            },
            description: {
              type: 'string',
              example: 'Frontend and backend development team',
            },
            createdAt: {
              type: 'string',
              format: 'date-time',
            },
            updatedAt: {
              type: 'string',
              format: 'date-time',
            },
          },
        },
        Project: {
          type: 'object',
          properties: {
            id: {
              type: 'string',
              example: 'clp123abc456',
            },
            name: {
              type: 'string',
              example: 'CollabFlow Platform',
            },
            description: {
              type: 'string',
              example: 'AI-powered team coordination platform',
            },
            status: {
              type: 'string',
              enum: ['PLANNING', 'ACTIVE', 'ON_HOLD', 'COMPLETED', 'CANCELLED'],
              example: 'ACTIVE',
            },
            startDate: {
              type: 'string',
              format: 'date-time',
            },
            endDate: {
              type: 'string',
              format: 'date-time',
            },
            createdAt: {
              type: 'string',
              format: 'date-time',
            },
            updatedAt: {
              type: 'string',
              format: 'date-time',
            },
          },
        },
        Task: {
          type: 'object',
          properties: {
            id: {
              type: 'string',
              example: 'clp123abc456',
            },
            title: {
              type: 'string',
              example: 'Implement user authentication',
            },
            description: {
              type: 'string',
              example: 'Add JWT-based authentication system',
            },
            status: {
              type: 'string',
              enum: ['TODO', 'IN_PROGRESS', 'IN_REVIEW', 'BLOCKED', 'COMPLETED'],
              example: 'IN_PROGRESS',
            },
            priority: {
              type: 'string',
              enum: ['LOW', 'MEDIUM', 'HIGH', 'CRITICAL'],
              example: 'HIGH',
            },
            dueDate: {
              type: 'string',
              format: 'date-time',
            },
            estimatedHours: {
              type: 'number',
              example: 8,
            },
            tags: {
              type: 'array',
              items: {
                type: 'string',
              },
              example: ['backend', 'authentication'],
            },
            createdAt: {
              type: 'string',
              format: 'date-time',
            },
            updatedAt: {
              type: 'string',
              format: 'date-time',
            },
          },
        },
        Meeting: {
          type: 'object',
          properties: {
            id: {
              type: 'string',
              example: 'clp123abc456',
            },
            title: {
              type: 'string',
              example: 'Sprint Planning',
            },
            description: {
              type: 'string',
              example: 'Plan tasks for the next sprint',
            },
            startTime: {
              type: 'string',
              format: 'date-time',
            },
            endTime: {
              type: 'string',
              format: 'date-time',
            },
            status: {
              type: 'string',
              enum: ['SCHEDULED', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED'],
              example: 'SCHEDULED',
            },
            meetingUrl: {
              type: 'string',
              format: 'uri',
              example: 'https://zoom.us/j/123456789',
            },
            createdAt: {
              type: 'string',
              format: 'date-time',
            },
            updatedAt: {
              type: 'string',
              format: 'date-time',
            },
          },
        },
        MeetingNecessityAnalysis: {
          type: 'object',
          properties: {
            score: {
              type: 'number',
              minimum: 0,
              maximum: 100,
              example: 75,
              description: 'Necessity score from 0-100',
            },
            reasoning: {
              type: 'string',
              example: 'This meeting appears necessary for decision making and coordination',
            },
            alternatives: {
              type: 'array',
              items: {
                type: 'string',
              },
              example: ['Email discussion', 'Slack thread', 'Shared document'],
            },
            recommendations: {
              type: 'array',
              items: {
                type: 'string',
              },
              example: ['Reduce duration to 30 minutes', 'Include clear agenda'],
            },
          },
        },
        TaskPriorityAnalysis: {
          type: 'object',
          properties: {
            priority: {
              type: 'string',
              enum: ['LOW', 'MEDIUM', 'HIGH', 'CRITICAL'],
              example: 'HIGH',
            },
            reasoning: {
              type: 'string',
              example: 'Critical bug fix with customer impact',
            },
            estimatedHours: {
              type: 'number',
              example: 12,
            },
            dependencies: {
              type: 'array',
              items: {
                type: 'string',
              },
              example: ['task1', 'task2'],
            },
          },
        },
        HealthCheck: {
          type: 'object',
          properties: {
            success: {
              type: 'boolean',
              example: true,
            },
            health: {
              type: 'object',
              properties: {
                api: {
                  type: 'boolean',
                  example: true,
                },
                database: {
                  type: 'boolean',
                  example: true,
                },
                redis: {
                  type: 'boolean',
                  example: true,
                },
                ai: {
                  type: 'object',
                  properties: {
                    currentProvider: {
                      type: 'string',
                      example: 'ollama',
                    },
                    providers: {
                      type: 'array',
                      items: {
                        type: 'object',
                        properties: {
                          name: {
                            type: 'string',
                            example: 'ollama',
                          },
                          available: {
                            type: 'boolean',
                            example: true,
                          },
                          healthy: {
                            type: 'boolean',
                            example: true,
                          },
                        },
                      },
                    },
                  },
                },
              },
            },
            timestamp: {
              type: 'string',
              format: 'date-time',
            },
          },
        },
      },
    },
    security: [
      {
        bearerAuth: [],
      },
    ],
    tags: [
      {
        name: 'Authentication',
        description: 'User authentication and authorization',
      },
      {
        name: 'Users',
        description: 'User management operations',
      },
      {
        name: 'Teams',
        description: 'Team management operations',
      },
      {
        name: 'Projects',
        description: 'Project management operations',
      },
      {
        name: 'Tasks',
        description: 'Task management operations',
      },
      {
        name: 'Meetings',
        description: 'Meeting management operations',
      },
      {
        name: 'AI',
        description: 'AI-powered analysis and recommendations',
      },
      {
        name: 'Analytics',
        description: 'Analytics and reporting',
      },
      {
        name: 'Notifications',
        description: 'Notification management',
      },
      {
        name: 'Health',
        description: 'System health checks',
      },
    ],
  },
  apis: [
    './src/routes/*.ts', // Path to the API files
    './src/models/*.ts', // Path to the model files
  ],
};

const specs = swaggerJSDoc(options);

export const setupSwagger = (app: Express): void => {
  // Swagger UI setup
  app.use('/api/docs', swaggerUi.serve, swaggerUi.setup(specs, {
    explorer: true,
    customCss: '.swagger-ui .topbar { display: none }',
    customSiteTitle: 'CollabFlow API Documentation',
    swaggerOptions: {
      persistAuthorization: true,
      displayRequestDuration: true,
      filter: true,
      showExtensions: true,
      showCommonExtensions: true,
    },
  }));

  // JSON endpoint for the OpenAPI spec
  app.get('/api/docs.json', (req, res) => {
    res.setHeader('Content-Type', 'application/json');
    res.send(specs);
  });
};

export { specs };
