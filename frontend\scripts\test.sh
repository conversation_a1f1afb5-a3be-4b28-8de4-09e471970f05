#!/bin/bash

# CollabFlow Frontend Test Runner
# This script runs the comprehensive test suite for the CollabFlow frontend

set -e

echo "🧪 Starting CollabFlow Frontend Test Suite"
echo "========================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    print_error "Node.js is not installed. Please install Node.js 18+ to run tests."
    exit 1
fi

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    print_error "npm is not installed. Please install npm to run tests."
    exit 1
fi

# Set test environment
export NODE_ENV=test
export REACT_APP_API_URL=http://localhost:3001/api
export REACT_APP_WS_URL=ws://localhost:3001

print_status "Environment set to TEST mode"

# Install dependencies if node_modules doesn't exist
if [ ! -d "node_modules" ]; then
    print_status "Installing dependencies..."
    npm install
    print_success "Dependencies installed"
fi

# Run different test suites based on argument
case "${1:-all}" in
    "components")
        print_status "Running Component Tests..."
        npm run test -- --testPathPattern="__tests__/components" --coverage --coverageDirectory=coverage/components
        print_success "Component tests completed"
        ;;
    
    "hooks")
        print_status "Running Hook Tests..."
        npm run test -- --testPathPattern="__tests__/hooks" --coverage --coverageDirectory=coverage/hooks
        print_success "Hook tests completed"
        ;;
    
    "services")
        print_status "Running Service Tests..."
        npm run test -- --testPathPattern="__tests__/services" --coverage --coverageDirectory=coverage/services
        print_success "Service tests completed"
        ;;
    
    "utils")
        print_status "Running Utility Tests..."
        npm run test -- --testPathPattern="__tests__/utils" --coverage --coverageDirectory=coverage/utils
        print_success "Utility tests completed"
        ;;
    
    "coverage")
        print_status "Running All Tests with Coverage Report..."
        npm run test:coverage
        print_success "Coverage report generated in coverage/ directory"
        ;;
    
    "watch")
        print_status "Running Tests in Watch Mode..."
        npm run test:watch
        ;;
    
    "ci")
        print_status "Running CI Test Suite..."
        npm run test -- --ci --coverage --watchAll=false --passWithNoTests
        print_success "CI tests completed"
        ;;
    
    "all"|*)
        print_status "Running Complete Test Suite..."
        
        # Run component tests
        print_status "1/4 Running Component Tests..."
        npm run test -- --testPathPattern="__tests__/components" --silent
        print_success "Component tests passed ✓"
        
        # Run hook tests
        print_status "2/4 Running Hook Tests..."
        npm run test -- --testPathPattern="__tests__/hooks" --silent
        print_success "Hook tests passed ✓"
        
        # Run service tests
        print_status "3/4 Running Service Tests..."
        npm run test -- --testPathPattern="__tests__/services" --silent
        print_success "Service tests passed ✓"
        
        # Generate coverage report
        print_status "4/4 Generating Coverage Report..."
        npm run test:coverage -- --silent
        print_success "Coverage report generated ✓"
        
        print_success "All tests completed successfully!"
        ;;
esac

# Display coverage summary if coverage was generated
if [ -f "coverage/lcov-report/index.html" ]; then
    print_status "Coverage report available at: coverage/lcov-report/index.html"
fi

echo ""
echo "🎉 Frontend test suite execution completed!"
echo "=========================================="
