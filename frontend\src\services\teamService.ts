import { apiClient } from './api';

export interface Team {
  id: string;
  name: string;
  description?: string;
  memberCount: number;
  projectCount: number;
  createdAt: string;
  updatedAt: string;
  members?: TeamMember[];
  projects?: Project[];
  settings?: TeamSettings;
}

export interface TeamMember {
  userId: string;
  teamId: string;
  role: 'LEAD' | 'SENIOR' | 'MEMBER';
  joinedAt: string;
  team: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
    avatar?: string;
    skills: string[];
  };
}

export interface TeamSettings {
  workingHoursStart: string;
  workingHoursEnd: string;
  timezone: string;
  maxDailyMeetings: number;
  preferredMeetingLength: number;
  bufferTime: number;
  noMeetingDays: string[];
}

export interface Project {
  id: string;
  name: string;
  status: string;
  priority: string;
  startDate: string;
  endDate?: string;
  taskCount: number;
}

export interface CreateTeamData {
  name: string;
  description?: string;
}

export interface UpdateTeamData {
  name?: string;
  description?: string;
}

export interface AddMemberData {
  userId: string;
  role?: 'LEAD' | 'SENIOR' | 'MEMBER';
}

export const teamService = {
  // Get all teams for current user
  getTeams: async (params?: {
    page?: number;
    limit?: number;
    search?: string;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  }) => {
    return apiClient.get<Team[]>('/teams', { params });
  },

  // Create new team
  createTeam: async (data: CreateTeamData) => {
    return apiClient.post<Team>('/teams', data);
  },

  // Get team by ID
  getTeamById: async (teamId: string) => {
    return apiClient.get<Team>(`/teams/${teamId}`);
  },

  // Update team
  updateTeam: async (teamId: string, data: UpdateTeamData) => {
    return apiClient.put<Team>(`/teams/${teamId}`, data);
  },

  // Delete team
  deleteTeam: async (teamId: string) => {
    return apiClient.delete(`/teams/${teamId}`);
  },

  // Add team member
  addMember: async (teamId: string, data: AddMemberData) => {
    return apiClient.post<TeamMember>(`/teams/${teamId}/members`, data);
  },

  // Remove team member
  removeMember: async (teamId: string, userId: string) => {
    return apiClient.delete(`/teams/${teamId}/members/${userId}`);
  },

  // Update member role
  updateMemberRole: async (
    teamId: string,
    userId: string,
    role: 'LEAD' | 'SENIOR' | 'MEMBER'
  ) => {
    return apiClient.patch(`/teams/${teamId}/members/${userId}/role`, { role });
  },

  // Get team settings
  getTeamSettings: async (teamId: string) => {
    return apiClient.get<TeamSettings>(`/teams/${teamId}/settings`);
  },

  // Update team settings
  updateTeamSettings: async (teamId: string, settings: Partial<TeamSettings>) => {
    return apiClient.put<TeamSettings>(`/teams/${teamId}/settings`, settings);
  },
};
