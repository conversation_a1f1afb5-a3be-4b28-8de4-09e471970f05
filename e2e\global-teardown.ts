import { chromium, FullConfig } from '@playwright/test';

async function globalTeardown(config: FullConfig) {
  console.log('🧹 Starting E2E test environment cleanup...');

  const browser = await chromium.launch();
  const context = await browser.newContext();
  const page = await context.newPage();

  try {
    // Clean up test data if needed
    console.log('🗑️ Cleaning up test data...');
    
    // You could add cleanup API calls here if your backend supports it
    // For example:
    // await page.request.delete('http://localhost:3001/api/test/cleanup');

    console.log('✅ E2E test environment cleanup completed');

  } catch (error) {
    console.error('❌ Failed to cleanup E2E test environment:', error);
    // Don't throw error in teardown to avoid masking test failures
  } finally {
    await context.close();
    await browser.close();
  }
}

export default globalTeardown;
