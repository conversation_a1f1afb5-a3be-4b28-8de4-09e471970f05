import * as Sentry from '@sentry/node';
import { nodeProfilingIntegration } from '@sentry/profiling-node';
import { logger } from './logger';

export class MonitoringService {
  private static instance: MonitoringService;
  private initialized = false;

  private constructor() {}

  public static getInstance(): MonitoringService {
    if (!MonitoringService.instance) {
      MonitoringService.instance = new MonitoringService();
    }
    return MonitoringService.instance;
  }

  public initialize(): void {
    if (this.initialized) {
      return;
    }

    const sentryDsn = process.env.SENTRY_DSN;
    const environment = process.env.NODE_ENV || 'development';

    if (sentryDsn && environment !== 'test') {
      try {
        Sentry.init({
          dsn: sentryDsn,
          environment,
          integrations: [
            // Add profiling integration
            nodeProfilingIntegration(),
            // Add performance monitoring
            Sentry.httpIntegration(),
            Sentry.expressIntegration(),
          ],
          // Performance monitoring
          tracesSampleRate: environment === 'production' ? 0.1 : 1.0,
          // Profiling
          profilesSampleRate: environment === 'production' ? 0.1 : 1.0,
          // Release tracking
          release: process.env.APP_VERSION || '1.0.0',
          // Error filtering
          beforeSend(event, hint) {
            // Filter out certain errors in development
            if (environment === 'development') {
              const error = hint.originalException;
              if (error instanceof Error) {
                // Skip common development errors
                if (error.message.includes('ECONNREFUSED') || 
                    error.message.includes('ENOTFOUND')) {
                  return null;
                }
              }
            }
            return event;
          },
          // Additional context
          initialScope: {
            tags: {
              component: 'backend',
              service: 'collabflow',
            },
          },
        });

        logger.info('Sentry monitoring initialized', {
          environment,
          release: process.env.APP_VERSION || '1.0.0',
        });
      } catch (error) {
        logger.error('Failed to initialize Sentry:', error);
      }
    } else {
      logger.info('Sentry monitoring disabled (no DSN provided or test environment)');
    }

    this.initialized = true;
  }

  public captureException(error: Error, context?: Record<string, any>): void {
    if (context) {
      Sentry.withScope((scope) => {
        Object.entries(context).forEach(([key, value]) => {
          scope.setContext(key, value);
        });
        Sentry.captureException(error);
      });
    } else {
      Sentry.captureException(error);
    }

    // Also log to our regular logger
    logger.error('Exception captured:', {
      error: error.message,
      stack: error.stack,
      context,
    });
  }

  public captureMessage(message: string, level: 'info' | 'warning' | 'error' = 'info', context?: Record<string, any>): void {
    const sentryLevel = level === 'warning' ? 'warning' : level === 'error' ? 'error' : 'info';
    
    if (context) {
      Sentry.withScope((scope) => {
        Object.entries(context).forEach(([key, value]) => {
          scope.setContext(key, value);
        });
        Sentry.captureMessage(message, sentryLevel);
      });
    } else {
      Sentry.captureMessage(message, sentryLevel);
    }

    // Also log to our regular logger
    logger[level]('Message captured:', { message, context });
  }

  public setUser(user: { id: string; email?: string; role?: string }): void {
    Sentry.setUser(user);
  }

  public setTag(key: string, value: string): void {
    Sentry.setTag(key, value);
  }

  public setContext(key: string, context: Record<string, any>): void {
    Sentry.setContext(key, context);
  }

  public addBreadcrumb(message: string, category?: string, level?: 'info' | 'warning' | 'error'): void {
    Sentry.addBreadcrumb({
      message,
      category: category || 'custom',
      level: level || 'info',
      timestamp: Date.now() / 1000,
    });
  }

  public startTransaction(name: string, op: string): any {
    return Sentry.startTransaction({ name, op });
  }

  public async flush(timeout = 2000): Promise<boolean> {
    return Sentry.flush(timeout);
  }

  public close(timeout = 2000): Promise<boolean> {
    return Sentry.close(timeout);
  }
}

// Performance monitoring utilities
export class PerformanceMonitor {
  private static metrics: Map<string, { count: number; totalTime: number; avgTime: number }> = new Map();

  public static startTimer(name: string): () => void {
    const start = Date.now();
    
    return () => {
      const duration = Date.now() - start;
      this.recordMetric(name, duration);
    };
  }

  public static recordMetric(name: string, duration: number): void {
    const existing = this.metrics.get(name) || { count: 0, totalTime: 0, avgTime: 0 };
    existing.count++;
    existing.totalTime += duration;
    existing.avgTime = existing.totalTime / existing.count;
    
    this.metrics.set(name, existing);

    // Log slow operations
    if (duration > 5000) { // 5 seconds
      logger.warn('Slow operation detected', {
        operation: name,
        duration,
        avgTime: existing.avgTime,
      });
    }
  }

  public static getMetrics(): Record<string, { count: number; totalTime: number; avgTime: number }> {
    return Object.fromEntries(this.metrics);
  }

  public static resetMetrics(): void {
    this.metrics.clear();
  }
}

// Error classification utilities
export class ErrorClassifier {
  public static classifyError(error: Error): {
    type: 'user' | 'system' | 'external' | 'unknown';
    severity: 'low' | 'medium' | 'high' | 'critical';
    category: string;
  } {
    const message = error.message.toLowerCase();
    const stack = error.stack?.toLowerCase() || '';

    // User errors (4xx equivalent)
    if (message.includes('validation') || 
        message.includes('invalid') || 
        message.includes('unauthorized') ||
        message.includes('forbidden') ||
        message.includes('not found')) {
      return {
        type: 'user',
        severity: 'low',
        category: 'validation',
      };
    }

    // External service errors
    if (message.includes('econnrefused') ||
        message.includes('enotfound') ||
        message.includes('timeout') ||
        message.includes('network') ||
        stack.includes('axios') ||
        stack.includes('fetch')) {
      return {
        type: 'external',
        severity: 'medium',
        category: 'network',
      };
    }

    // Database errors
    if (stack.includes('prisma') ||
        message.includes('database') ||
        message.includes('connection') ||
        message.includes('query')) {
      return {
        type: 'system',
        severity: 'high',
        category: 'database',
      };
    }

    // AI service errors
    if (message.includes('ai') ||
        message.includes('ollama') ||
        message.includes('openai') ||
        stack.includes('aiservice')) {
      return {
        type: 'external',
        severity: 'medium',
        category: 'ai',
      };
    }

    // System errors
    if (message.includes('out of memory') ||
        message.includes('enospc') ||
        message.includes('emfile')) {
      return {
        type: 'system',
        severity: 'critical',
        category: 'resources',
      };
    }

    // Default classification
    return {
      type: 'unknown',
      severity: 'medium',
      category: 'general',
    };
  }
}

// Health check utilities
export class HealthChecker {
  private static checks: Map<string, () => Promise<boolean>> = new Map();

  public static registerCheck(name: string, check: () => Promise<boolean>): void {
    this.checks.set(name, check);
  }

  public static async runAllChecks(): Promise<Record<string, { healthy: boolean; error?: string; duration: number }>> {
    const results: Record<string, { healthy: boolean; error?: string; duration: number }> = {};

    for (const [name, check] of this.checks) {
      const start = Date.now();
      try {
        const healthy = await check();
        results[name] = {
          healthy,
          duration: Date.now() - start,
        };
      } catch (error) {
        results[name] = {
          healthy: false,
          error: error instanceof Error ? error.message : 'Unknown error',
          duration: Date.now() - start,
        };
      }
    }

    return results;
  }
}

// Export singleton instance
export const monitoring = MonitoringService.getInstance();
