import { chromium, FullConfig } from '@playwright/test';

async function globalSetup(config: FullConfig) {
  console.log('🚀 Starting E2E test environment setup...');

  // Set environment variables for testing
  process.env.NODE_ENV = 'test';
  process.env.DATABASE_URL = 'postgresql://test:test@localhost:5432/collabflow_e2e_test';
  process.env.REDIS_URL = 'redis://localhost:6379/2';
  process.env.JWT_SECRET = 'e2e-test-jwt-secret';
  process.env.OPENAI_API_KEY = 'test-openai-key';

  // Create a browser instance for setup tasks
  const browser = await chromium.launch();
  const context = await browser.newContext();
  const page = await context.newPage();

  try {
    // Wait for the frontend to be ready
    console.log('⏳ Waiting for frontend server...');
    await page.goto('http://localhost:3000', { waitUntil: 'networkidle' });
    console.log('✅ Frontend server is ready');

    // Wait for the backend to be ready
    console.log('⏳ Waiting for backend server...');
    const response = await page.request.get('http://localhost:3001/api/health');
    if (response.ok()) {
      console.log('✅ Backend server is ready');
    } else {
      throw new Error('Backend server is not responding');
    }

    // Create test user for authentication tests
    console.log('👤 Creating test user...');
    try {
      const registerResponse = await page.request.post('http://localhost:3001/api/auth/register', {
        data: {
          email: '<EMAIL>',
          password: 'TestPassword123!',
          firstName: 'E2E',
          lastName: 'Test',
          skills: ['Testing', 'Automation'],
          timezone: 'UTC',
        },
      });

      if (registerResponse.ok()) {
        const userData = await registerResponse.json();
        // Store test user credentials for tests
        process.env.E2E_TEST_EMAIL = '<EMAIL>';
        process.env.E2E_TEST_PASSWORD = 'TestPassword123!';
        process.env.E2E_TEST_USER_ID = userData.data.user.id;
        console.log('✅ Test user created successfully');
      } else {
        console.log('ℹ️ Test user might already exist, continuing...');
        process.env.E2E_TEST_EMAIL = '<EMAIL>';
        process.env.E2E_TEST_PASSWORD = 'TestPassword123!';
      }
    } catch (error) {
      console.log('⚠️ Could not create test user, tests may need to handle registration');
    }

    console.log('🎉 E2E test environment setup completed');

  } catch (error) {
    console.error('❌ Failed to setup E2E test environment:', error);
    throw error;
  } finally {
    await context.close();
    await browser.close();
  }
}

export default globalSetup;
