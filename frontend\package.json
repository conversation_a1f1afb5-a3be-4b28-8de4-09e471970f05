{"name": "collabflow-frontend", "version": "1.0.0", "description": "CollabFlow Frontend - AI-powered team coordination platform", "private": true, "dependencies": {"@reduxjs/toolkit": "^2.0.1", "@types/node": "^20.10.4", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "react": "^18.2.0", "react-dom": "^18.2.0", "react-redux": "^9.0.4", "react-router-dom": "^6.20.1", "react-scripts": "5.0.1", "typescript": "^5.3.3", "web-vitals": "^3.5.0", "@mui/material": "^5.15.1", "@mui/icons-material": "^5.15.1", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/x-date-pickers": "^6.18.3", "@mui/x-data-grid": "^6.18.3", "axios": "^1.6.2", "socket.io-client": "^4.7.4", "date-fns": "^2.30.0", "react-hook-form": "^7.48.2", "@hookform/resolvers": "^3.3.2", "yup": "^1.4.0", "react-query": "^3.39.3", "react-hot-toast": "^2.4.1", "recharts": "^2.8.0", "framer-motion": "^10.16.16", "react-beautiful-dnd": "^13.1.1", "react-dropzone": "^14.2.3", "lodash": "^4.17.21", "moment": "^2.29.4", "moment-timezone": "^0.5.43"}, "devDependencies": {"@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.1", "@types/jest": "^29.5.8", "@types/lodash": "^4.14.202", "@types/react-beautiful-dnd": "^13.1.8", "eslint": "^8.55.0", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "prettier": "^3.1.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.0.1", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint src/**/*.{ts,tsx}", "lint:fix": "eslint src/**/*.{ts,tsx} --fix", "format": "prettier --write src/**/*.{ts,tsx,css,scss,json}", "type-check": "tsc --noEmit"}, "eslintConfig": {"extends": ["react-app", "react-app/jest", "prettier"], "plugins": ["prettier"], "rules": {"prettier/prettier": "error"}}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:3001"}