import { Router, Request, Response } from 'express';
import { validationResult } from 'express-validator';
import { userService } from '../services/userService';
import { asyncHandler, CustomError } from '../middleware/errorHandler';
import { AuthenticatedRequest, requireRole } from '../middleware/auth';
import {
  commonValidations,
  userValidations,
  paginationValidation
} from '../utils/validation';

const router = Router();

// Helper function to handle validation errors
const handleValidationErrors = (req: Request, res: Response, next: any) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errors.array(),
    });
  }
  next();
};

// Get all users (admin/manager only)
router.get('/',
  requireRole(['ADMIN', 'MANAGER']),
  paginationValidation,
  handleValidationErrors,
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const { page, limit, sortBy, sortOrder, search, role } = req.query;

    const result = await userService.getUsers({
      page: page ? parseInt(page as string) : undefined,
      limit: limit ? parseInt(limit as string) : undefined,
      sortBy: sortBy as string,
      sortOrder: sortOrder as 'asc' | 'desc',
      search: search as string,
      role: role as any,
    });

    res.json(result);
  })
);

// Get user by ID
router.get('/:id',
  commonValidations.id,
  handleValidationErrors,
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const { id } = req.params;
    const currentUserId = req.user!.id;
    const currentUserRole = req.user!.role;

    // Users can only view their own profile unless they're admin/manager
    if (id !== currentUserId && !['ADMIN', 'MANAGER'].includes(currentUserRole)) {
      throw new CustomError('Access denied', 403);
    }

    const result = await userService.getUserById(id);
    res.json(result);
  })
);

// Update user
router.put('/:id',
  commonValidations.id,
  userValidations.update,
  handleValidationErrors,
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const { id } = req.params;
    const currentUserId = req.user!.id;
    const currentUserRole = req.user!.role;

    // Users can only update their own profile unless they're admin
    if (id !== currentUserId && currentUserRole !== 'ADMIN') {
      throw new CustomError('Access denied', 403);
    }

    const { firstName, lastName, avatar, skills, timezone } = req.body;

    const result = await userService.updateUser(id, {
      firstName,
      lastName,
      avatar,
      skills,
      timezone,
    });

    res.json(result);
  })
);

// Delete user (admin only)
router.delete('/:id',
  requireRole(['ADMIN']),
  commonValidations.id,
  handleValidationErrors,
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const { id } = req.params;
    const currentUserId = req.user!.id;

    // Prevent self-deletion
    if (id === currentUserId) {
      throw new CustomError('Cannot delete your own account', 400);
    }

    const result = await userService.deleteUser(id);
    res.json(result);
  })
);

// Update user role (admin only)
router.patch('/:id/role',
  requireRole(['ADMIN']),
  commonValidations.id,
  [
    commonValidations.status('role', ['ADMIN', 'MANAGER', 'MEMBER', 'VIEWER'])
  ],
  handleValidationErrors,
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const { id } = req.params;
    const { role } = req.body;
    const currentUserId = req.user!.id;

    const result = await userService.updateUserRole(id, role, currentUserId);
    res.json(result);
  })
);

// Get user availability
router.get('/:id/availability',
  commonValidations.id,
  handleValidationErrors,
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const { id } = req.params;
    const currentUserId = req.user!.id;
    const currentUserRole = req.user!.role;

    // Users can only view their own availability unless they're admin/manager
    if (id !== currentUserId && !['ADMIN', 'MANAGER'].includes(currentUserRole)) {
      throw new CustomError('Access denied', 403);
    }

    const result = await userService.getUserAvailability(id);
    res.json(result);
  })
);

// Update user availability
router.put('/:id/availability',
  commonValidations.id,
  [
    commonValidations.array('availability'),
  ],
  handleValidationErrors,
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const { id } = req.params;
    const { availability } = req.body;
    const currentUserId = req.user!.id;

    // Users can only update their own availability
    if (id !== currentUserId) {
      throw new CustomError('Access denied', 403);
    }

    const result = await userService.updateUserAvailability(id, availability);
    res.json(result);
  })
);

// Get user teams
router.get('/:id/teams',
  commonValidations.id,
  handleValidationErrors,
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const { id } = req.params;
    const currentUserId = req.user!.id;
    const currentUserRole = req.user!.role;

    // Users can only view their own teams unless they're admin/manager
    if (id !== currentUserId && !['ADMIN', 'MANAGER'].includes(currentUserRole)) {
      throw new CustomError('Access denied', 403);
    }

    const result = await userService.getUserTeams(id);
    res.json(result);
  })
);

// Get user tasks
router.get('/:id/tasks',
  commonValidations.id,
  paginationValidation,
  handleValidationErrors,
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const { id } = req.params;
    const { page, limit, sortBy, sortOrder, status } = req.query;
    const currentUserId = req.user!.id;
    const currentUserRole = req.user!.role;

    // Users can only view their own tasks unless they're admin/manager
    if (id !== currentUserId && !['ADMIN', 'MANAGER'].includes(currentUserRole)) {
      throw new CustomError('Access denied', 403);
    }

    const result = await userService.getUserTasks(id, {
      page: page ? parseInt(page as string) : undefined,
      limit: limit ? parseInt(limit as string) : undefined,
      sortBy: sortBy as string,
      sortOrder: sortOrder as 'asc' | 'desc',
      status: status as string,
    });

    res.json(result);
  })
);

export default router;
