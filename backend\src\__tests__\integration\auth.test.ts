import { jest } from '@jest/globals';
import app from '../../app';
import { TestHelper } from '../helpers/testHelper';
import { prisma } from '../../config/database';
import bcrypt from 'bcryptjs';

const mockPrisma = prisma as jest.Mocked<typeof prisma>;
const testHelper = new TestHelper(app.app);

describe('Auth API Integration Tests', () => {
  beforeEach(async () => {
    await testHelper.cleanupDatabase();
    jest.clearAllMocks();
  });

  afterAll(async () => {
    await testHelper.cleanupDatabase();
  });

  describe('POST /api/auth/register', () => {
    it('should register a new user successfully', async () => {
      // Arrange
      const userData = {
        email: testHelper.randomEmail(),
        password: 'Password123!',
        firstName: 'John',
        lastName: 'Doe',
        skills: ['JavaScript', 'React'],
        timezone: 'UTC',
      };

      const mockUser = testHelper.createMockUser({
        email: userData.email,
        firstName: userData.firstName,
        lastName: userData.lastName,
      });

      mockPrisma.user.findUnique.mockResolvedValue(null); // User doesn't exist
      mockPrisma.user.create.mockResolvedValue(mockUser as any);

      // Act
      const response = await testHelper.unauthenticatedRequest()
        .post('/api/auth/register')
        .send(userData);

      // Assert
      testHelper.expectSuccessResponse(response);
      expect(response.status).toBe(201);
      expect(response.body.data).toHaveProperty('user');
      expect(response.body.data).toHaveProperty('tokens');
      expect(response.body.data.user.email).toBe(userData.email);
      expect(response.body.data.user).not.toHaveProperty('password');
      expect(response.body.data.tokens).toHaveProperty('accessToken');
      expect(response.body.data.tokens).toHaveProperty('refreshToken');
    });

    it('should reject registration with existing email', async () => {
      // Arrange
      const userData = {
        email: '<EMAIL>',
        password: 'Password123!',
        firstName: 'John',
        lastName: 'Doe',
      };

      mockPrisma.user.findUnique.mockResolvedValue(testHelper.createMockUser() as any);

      // Act
      const response = await testHelper.unauthenticatedRequest()
        .post('/api/auth/register')
        .send(userData);

      // Assert
      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.error).toMatch(/already exists/i);
    });

    it('should validate required fields', async () => {
      // Act
      const response = await testHelper.unauthenticatedRequest()
        .post('/api/auth/register')
        .send({});

      // Assert
      testHelper.expectValidationError(response);
      expect(response.body.details.some((d: any) => d.path === 'email')).toBe(true);
      expect(response.body.details.some((d: any) => d.path === 'password')).toBe(true);
      expect(response.body.details.some((d: any) => d.path === 'firstName')).toBe(true);
      expect(response.body.details.some((d: any) => d.path === 'lastName')).toBe(true);
    });

    it('should validate email format', async () => {
      // Act
      const response = await testHelper.unauthenticatedRequest()
        .post('/api/auth/register')
        .send({
          email: 'invalid-email',
          password: 'Password123!',
          firstName: 'John',
          lastName: 'Doe',
        });

      // Assert
      testHelper.expectValidationError(response, 'email');
    });

    it('should validate password strength', async () => {
      // Act
      const response = await testHelper.unauthenticatedRequest()
        .post('/api/auth/register')
        .send({
          email: testHelper.randomEmail(),
          password: 'weak',
          firstName: 'John',
          lastName: 'Doe',
        });

      // Assert
      testHelper.expectValidationError(response, 'password');
    });
  });

  describe('POST /api/auth/login', () => {
    it('should login with valid credentials', async () => {
      // Arrange
      const password = 'Password123!';
      const hashedPassword = await bcrypt.hash(password, 10);
      const mockUser = {
        ...testHelper.createMockUser(),
        password: hashedPassword,
      };

      mockPrisma.user.findUnique.mockResolvedValue(mockUser as any);

      // Act
      const response = await testHelper.unauthenticatedRequest()
        .post('/api/auth/login')
        .send({
          email: mockUser.email,
          password: password,
        });

      // Assert
      testHelper.expectSuccessResponse(response);
      expect(response.body.data).toHaveProperty('user');
      expect(response.body.data).toHaveProperty('tokens');
      expect(response.body.data.user.email).toBe(mockUser.email);
      expect(response.body.data.user).not.toHaveProperty('password');
    });

    it('should reject login with invalid email', async () => {
      // Arrange
      mockPrisma.user.findUnique.mockResolvedValue(null);

      // Act
      const response = await testHelper.unauthenticatedRequest()
        .post('/api/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'Password123!',
        });

      // Assert
      expect(response.status).toBe(401);
      expect(response.body.success).toBe(false);
      expect(response.body.error).toMatch(/invalid credentials/i);
    });

    it('should reject login with invalid password', async () => {
      // Arrange
      const hashedPassword = await bcrypt.hash('correctpassword', 10);
      const mockUser = {
        ...testHelper.createMockUser(),
        password: hashedPassword,
      };

      mockPrisma.user.findUnique.mockResolvedValue(mockUser as any);

      // Act
      const response = await testHelper.unauthenticatedRequest()
        .post('/api/auth/login')
        .send({
          email: mockUser.email,
          password: 'wrongpassword',
        });

      // Assert
      expect(response.status).toBe(401);
      expect(response.body.success).toBe(false);
      expect(response.body.error).toMatch(/invalid credentials/i);
    });

    it('should validate required fields for login', async () => {
      // Act
      const response = await testHelper.unauthenticatedRequest()
        .post('/api/auth/login')
        .send({});

      // Assert
      testHelper.expectValidationError(response);
      expect(response.body.details.some((d: any) => d.path === 'email')).toBe(true);
      expect(response.body.details.some((d: any) => d.path === 'password')).toBe(true);
    });
  });

  describe('POST /api/auth/refresh', () => {
    it('should refresh tokens with valid refresh token', async () => {
      // Arrange
      const mockUser = testHelper.createMockUser();
      const refreshToken = testHelper.generateRefreshToken(mockUser);

      mockPrisma.user.findUnique.mockResolvedValue(mockUser as any);

      // Act
      const response = await testHelper.unauthenticatedRequest()
        .post('/api/auth/refresh')
        .send({ refreshToken });

      // Assert
      testHelper.expectSuccessResponse(response);
      expect(response.body.data).toHaveProperty('tokens');
      expect(response.body.data.tokens).toHaveProperty('accessToken');
      expect(response.body.data.tokens).toHaveProperty('refreshToken');
    });

    it('should reject invalid refresh token', async () => {
      // Act
      const response = await testHelper.unauthenticatedRequest()
        .post('/api/auth/refresh')
        .send({ refreshToken: 'invalid-token' });

      // Assert
      testHelper.expectAuthenticationError(response);
    });

    it('should reject refresh token for non-existent user', async () => {
      // Arrange
      const refreshToken = testHelper.generateRefreshToken({ id: 'nonexistent' });
      mockPrisma.user.findUnique.mockResolvedValue(null);

      // Act
      const response = await testHelper.unauthenticatedRequest()
        .post('/api/auth/refresh')
        .send({ refreshToken });

      // Assert
      testHelper.expectAuthenticationError(response);
    });
  });

  describe('POST /api/auth/logout', () => {
    it('should logout successfully with valid token', async () => {
      // Arrange
      const mockUser = testHelper.createMockUser();

      // Act
      const response = await testHelper.authenticatedRequest(mockUser)
        .post('/api/auth/logout');

      // Assert
      testHelper.expectSuccessResponse(response);
      expect(response.body.message).toMatch(/logged out/i);
    });

    it('should require authentication for logout', async () => {
      // Act
      const response = await testHelper.unauthenticatedRequest()
        .post('/api/auth/logout');

      // Assert
      testHelper.expectAuthenticationError(response);
    });
  });

  describe('GET /api/auth/me', () => {
    it('should return current user profile', async () => {
      // Arrange
      const mockUser = testHelper.createMockUser();
      mockPrisma.user.findUnique.mockResolvedValue(mockUser as any);

      // Act
      const response = await testHelper.authenticatedRequest(mockUser)
        .get('/api/auth/me');

      // Assert
      testHelper.expectSuccessResponse(response);
      expect(response.body.data).toMatchObject({
        id: mockUser.id,
        email: mockUser.email,
        firstName: mockUser.firstName,
        lastName: mockUser.lastName,
      });
      expect(response.body.data).not.toHaveProperty('password');
    });

    it('should require authentication', async () => {
      // Act
      const response = await testHelper.unauthenticatedRequest()
        .get('/api/auth/me');

      // Assert
      testHelper.expectAuthenticationError(response);
    });

    it('should handle non-existent user', async () => {
      // Arrange
      const mockUser = testHelper.createMockUser();
      mockPrisma.user.findUnique.mockResolvedValue(null);

      // Act
      const response = await testHelper.authenticatedRequest(mockUser)
        .get('/api/auth/me');

      // Assert
      testHelper.expectNotFoundError(response);
    });
  });

  describe('PUT /api/auth/me', () => {
    it('should update user profile successfully', async () => {
      // Arrange
      const mockUser = testHelper.createMockUser();
      const updateData = {
        firstName: 'Updated',
        lastName: 'Name',
        skills: ['Python', 'Django'],
        timezone: 'America/New_York',
      };

      const updatedUser = { ...mockUser, ...updateData };

      mockPrisma.user.findUnique.mockResolvedValue(mockUser as any);
      mockPrisma.user.update.mockResolvedValue(updatedUser as any);

      // Act
      const response = await testHelper.authenticatedRequest(mockUser)
        .put('/api/auth/me')
        .send(updateData);

      // Assert
      testHelper.expectSuccessResponse(response);
      expect(response.body.data.firstName).toBe(updateData.firstName);
      expect(response.body.data.lastName).toBe(updateData.lastName);
      expect(response.body.data.skills).toEqual(updateData.skills);
      expect(response.body.data.timezone).toBe(updateData.timezone);
    });

    it('should require authentication for profile update', async () => {
      // Act
      const response = await testHelper.unauthenticatedRequest()
        .put('/api/auth/me')
        .send({ firstName: 'Updated' });

      // Assert
      testHelper.expectAuthenticationError(response);
    });

    it('should validate update data', async () => {
      // Arrange
      const mockUser = testHelper.createMockUser();

      // Act
      const response = await testHelper.authenticatedRequest(mockUser)
        .put('/api/auth/me')
        .send({
          firstName: '', // Invalid: empty string
          email: 'invalid-email', // Invalid: bad email format
        });

      // Assert
      testHelper.expectValidationError(response);
    });
  });

  describe('POST /api/auth/change-password', () => {
    it('should change password successfully', async () => {
      // Arrange
      const currentPassword = 'CurrentPassword123!';
      const newPassword = 'NewPassword123!';
      const hashedCurrentPassword = await bcrypt.hash(currentPassword, 10);
      
      const mockUser = {
        ...testHelper.createMockUser(),
        password: hashedCurrentPassword,
      };

      mockPrisma.user.findUnique.mockResolvedValue(mockUser as any);
      mockPrisma.user.update.mockResolvedValue(mockUser as any);

      // Act
      const response = await testHelper.authenticatedRequest(mockUser)
        .post('/api/auth/change-password')
        .send({
          currentPassword,
          newPassword,
        });

      // Assert
      testHelper.expectSuccessResponse(response);
      expect(response.body.message).toMatch(/password changed/i);
    });

    it('should reject incorrect current password', async () => {
      // Arrange
      const hashedPassword = await bcrypt.hash('correctpassword', 10);
      const mockUser = {
        ...testHelper.createMockUser(),
        password: hashedPassword,
      };

      mockPrisma.user.findUnique.mockResolvedValue(mockUser as any);

      // Act
      const response = await testHelper.authenticatedRequest(mockUser)
        .post('/api/auth/change-password')
        .send({
          currentPassword: 'wrongpassword',
          newPassword: 'NewPassword123!',
        });

      // Assert
      expect(response.status).toBe(400);
      expect(response.body.error).toMatch(/current password is incorrect/i);
    });

    it('should validate new password strength', async () => {
      // Arrange
      const mockUser = testHelper.createMockUser();

      // Act
      const response = await testHelper.authenticatedRequest(mockUser)
        .post('/api/auth/change-password')
        .send({
          currentPassword: 'CurrentPassword123!',
          newPassword: 'weak',
        });

      // Assert
      testHelper.expectValidationError(response, 'newPassword');
    });
  });
});
