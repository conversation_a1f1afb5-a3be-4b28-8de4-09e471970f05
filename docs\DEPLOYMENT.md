# CollabFlow Deployment Guide

## Overview

This guide covers deploying CollabFlow to various environments including development, staging, and production.

## Prerequisites

- <PERSON>er and Docker Compose
- Node.js 18+ (for local development)
- PostgreSQL 14+ (if not using Docker)
- Redis 6+ (if not using Docker)

## Environment Variables

### Backend Environment Variables

Create a `.env` file in the `backend` directory:

```env
# Server Configuration
NODE_ENV=production
PORT=3001
HOST=0.0.0.0

# Database Configuration
DATABASE_URL=postgresql://user:password@localhost:5432/collabflow

# Redis Configuration
REDIS_URL=redis://localhost:6379

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=7d
JWT_REFRESH_EXPIRES_IN=30d

# OpenAI Configuration (Optional)
OPENAI_API_KEY=your-openai-api-key

# Email Configuration (Optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
FROM_EMAIL=<EMAIL>

# CORS Configuration
CORS_ORIGIN=https://your-frontend-domain.com
```

### Frontend Environment Variables

Create a `.env` file in the `frontend` directory:

```env
# API Configuration
REACT_APP_API_URL=https://api.collabflow.dev/api
REACT_APP_WS_URL=wss://api.collabflow.dev

# App Configuration
REACT_APP_NAME=CollabFlow
REACT_APP_VERSION=1.0.0

# Feature Flags
REACT_APP_ENABLE_ANALYTICS=true
REACT_APP_ENABLE_NOTIFICATIONS=true
```

## Deployment Methods

### 1. Docker Compose (Recommended)

#### Development Deployment

```bash
# Clone the repository
git clone https://github.com/HectorTa1989/CollabFlow.git
cd CollabFlow

# Run setup script
chmod +x scripts/setup.sh
./scripts/setup.sh

# Start development environment
docker-compose up -d
```

#### Production Deployment

```bash
# Set environment variables
export POSTGRES_PASSWORD=your-secure-password
export JWT_SECRET=your-secure-jwt-secret
export OPENAI_API_KEY=your-openai-key

# Deploy to production
docker-compose -f docker-compose.prod.yml up -d
```

### 2. Manual Deployment

#### Backend Deployment

```bash
cd backend

# Install dependencies
npm ci --only=production

# Build the application
npm run build

# Run database migrations
npx prisma migrate deploy

# Seed the database (optional)
npx prisma db seed

# Start the application
npm start
```

#### Frontend Deployment

```bash
cd frontend

# Install dependencies
npm ci --only=production

# Build the application
npm run build

# Serve with a web server (nginx, apache, etc.)
# Copy build/ contents to your web server directory
```

### 3. Cloud Deployment

#### AWS Deployment

1. **ECS with Fargate**
   ```bash
   # Build and push images to ECR
   aws ecr get-login-password --region us-east-1 | docker login --username AWS --password-stdin <account-id>.dkr.ecr.us-east-1.amazonaws.com
   
   docker build -t collabflow-backend ./backend
   docker tag collabflow-backend:latest <account-id>.dkr.ecr.us-east-1.amazonaws.com/collabflow-backend:latest
   docker push <account-id>.dkr.ecr.us-east-1.amazonaws.com/collabflow-backend:latest
   
   docker build -t collabflow-frontend ./frontend
   docker tag collabflow-frontend:latest <account-id>.dkr.ecr.us-east-1.amazonaws.com/collabflow-frontend:latest
   docker push <account-id>.dkr.ecr.us-east-1.amazonaws.com/collabflow-frontend:latest
   ```

2. **RDS for PostgreSQL**
   - Create RDS PostgreSQL instance
   - Update DATABASE_URL in environment variables

3. **ElastiCache for Redis**
   - Create ElastiCache Redis cluster
   - Update REDIS_URL in environment variables

#### Google Cloud Platform

1. **Cloud Run**
   ```bash
   # Build and deploy backend
   gcloud builds submit --tag gcr.io/PROJECT-ID/collabflow-backend ./backend
   gcloud run deploy collabflow-backend --image gcr.io/PROJECT-ID/collabflow-backend --platform managed
   
   # Build and deploy frontend
   gcloud builds submit --tag gcr.io/PROJECT-ID/collabflow-frontend ./frontend
   gcloud run deploy collabflow-frontend --image gcr.io/PROJECT-ID/collabflow-frontend --platform managed
   ```

2. **Cloud SQL for PostgreSQL**
3. **Memorystore for Redis**

#### Azure Deployment

1. **Container Instances**
2. **Azure Database for PostgreSQL**
3. **Azure Cache for Redis**

## Database Setup

### Initial Setup

```bash
# Run migrations
npx prisma migrate deploy

# Generate Prisma client
npx prisma generate

# Seed database with initial data
npx prisma db seed
```

### Backup and Restore

```bash
# Create backup
pg_dump -h localhost -U collabflow_user collabflow > backup.sql

# Restore backup
psql -h localhost -U collabflow_user collabflow < backup.sql
```

## SSL/TLS Configuration

### Using Let's Encrypt with Nginx

```nginx
server {
    listen 80;
    server_name collabflow.dev www.collabflow.dev;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name collabflow.dev www.collabflow.dev;

    ssl_certificate /etc/letsencrypt/live/collabflow.dev/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/collabflow.dev/privkey.pem;

    location / {
        proxy_pass http://frontend:80;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /api {
        proxy_pass http://backend:3001;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /socket.io/ {
        proxy_pass http://backend:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
    }
}
```

## Monitoring and Logging

### Health Checks

- Backend: `GET /api/health`
- Frontend: `GET /`
- Database: Built into health check endpoint

### Logging

Logs are written to:
- Backend: `backend/logs/`
- Docker: Use `docker logs <container-name>`

### Monitoring Tools

- **Prometheus + Grafana**: For metrics and dashboards
- **Sentry**: For error tracking
- **New Relic**: For application performance monitoring

## Scaling

### Horizontal Scaling

```yaml
# docker-compose.prod.yml
services:
  backend:
    deploy:
      replicas: 3
    
  frontend:
    deploy:
      replicas: 2
```

### Load Balancing

Use nginx or cloud load balancers to distribute traffic across multiple instances.

## Security Considerations

1. **Environment Variables**: Never commit sensitive data to version control
2. **JWT Secrets**: Use strong, unique secrets for each environment
3. **Database**: Use strong passwords and restrict network access
4. **HTTPS**: Always use SSL/TLS in production
5. **CORS**: Configure appropriate CORS origins
6. **Rate Limiting**: Implement rate limiting to prevent abuse

## Backup Strategy

1. **Database Backups**: Daily automated backups
2. **File Uploads**: Regular backup of uploaded files
3. **Configuration**: Version control all configuration files

## Rollback Procedure

1. **Docker**: Use previous image tags
2. **Database**: Restore from backup if schema changes
3. **Frontend**: Deploy previous build

## Troubleshooting

### Common Issues

1. **Database Connection**: Check DATABASE_URL and network connectivity
2. **Redis Connection**: Verify REDIS_URL and Redis server status
3. **CORS Errors**: Check CORS_ORIGIN configuration
4. **JWT Errors**: Verify JWT_SECRET matches between services

### Debug Commands

```bash
# Check container logs
docker logs collabflow-backend
docker logs collabflow-frontend

# Check container status
docker ps

# Access container shell
docker exec -it collabflow-backend sh

# Check database connection
docker exec -it collabflow-postgres psql -U collabflow_user -d collabflow

# Check Redis connection
docker exec -it collabflow-redis redis-cli ping
```

## Performance Optimization

1. **Database Indexing**: Ensure proper indexes on frequently queried columns
2. **Caching**: Use Redis for session storage and caching
3. **CDN**: Use CDN for static assets
4. **Compression**: Enable gzip compression
5. **Image Optimization**: Optimize Docker images for size

## Maintenance

### Regular Tasks

1. **Update Dependencies**: Regularly update npm packages
2. **Security Patches**: Apply security updates promptly
3. **Database Maintenance**: Regular VACUUM and ANALYZE
4. **Log Rotation**: Implement log rotation to manage disk space
5. **Backup Verification**: Regularly test backup restoration
