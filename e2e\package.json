{"name": "collabflow-e2e-tests", "version": "1.0.0", "description": "End-to-end tests for CollabFlow application", "main": "index.js", "scripts": {"test": "playwright test", "test:headed": "playwright test --headed", "test:debug": "playwright test --debug", "test:ui": "playwright test --ui", "test:chromium": "playwright test --project=chromium", "test:firefox": "playwright test --project=firefox", "test:webkit": "playwright test --project=webkit", "test:mobile": "playwright test --project='Mobile Chrome' --project='Mobile Safari'", "test:auth": "playwright test tests/auth.spec.ts", "test:teams": "playwright test tests/team-management.spec.ts", "test:tasks": "playwright test tests/task-management.spec.ts", "test:meetings": "playwright test tests/meeting-scheduling.spec.ts", "test:realtime": "playwright test tests/realtime-collaboration.spec.ts", "test:ci": "playwright test --reporter=github", "report": "playwright show-report", "install-browsers": "playwright install", "install-deps": "playwright install-deps"}, "keywords": ["e2e", "testing", "playwright", "collabflow"], "author": "CollabFlow Team", "license": "MIT", "devDependencies": {"@playwright/test": "^1.40.0", "@types/node": "^20.10.5", "typescript": "^5.3.3"}, "engines": {"node": ">=18.0.0"}}