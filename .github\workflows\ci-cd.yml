name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

env:
  NODE_VERSION: '18'
  POSTGRES_PASSWORD: test_password
  JWT_SECRET: test_jwt_secret

jobs:
  # Backend Tests
  backend-test:
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: ${{ env.POSTGRES_PASSWORD }}
          POSTGRES_DB: collabflow_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: backend/package-lock.json

    - name: Install backend dependencies
      working-directory: ./backend
      run: npm ci

    - name: Run backend linting
      working-directory: ./backend
      run: npm run lint

    - name: Run backend type checking
      working-directory: ./backend
      run: npx tsc --noEmit

    - name: Setup test database
      working-directory: ./backend
      env:
        DATABASE_URL: postgresql://postgres:${{ env.POSTGRES_PASSWORD }}@localhost:5432/collabflow_test
        REDIS_URL: redis://localhost:6379
        JWT_SECRET: ${{ env.JWT_SECRET }}
      run: |
        npx prisma migrate deploy
        npx prisma db seed

    - name: Run backend tests
      working-directory: ./backend
      env:
        DATABASE_URL: postgresql://postgres:${{ env.POSTGRES_PASSWORD }}@localhost:5432/collabflow_test
        REDIS_URL: redis://localhost:6379
        JWT_SECRET: ${{ env.JWT_SECRET }}
      run: npm test

    - name: Upload backend coverage
      uses: codecov/codecov-action@v3
      with:
        file: ./backend/coverage/lcov.info
        flags: backend

  # Frontend Tests
  frontend-test:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: frontend/package-lock.json

    - name: Install frontend dependencies
      working-directory: ./frontend
      run: npm ci

    - name: Run frontend linting
      working-directory: ./frontend
      run: npm run lint

    - name: Run frontend type checking
      working-directory: ./frontend
      run: npm run type-check

    - name: Run frontend tests
      working-directory: ./frontend
      run: npm test -- --coverage --watchAll=false

    - name: Upload frontend coverage
      uses: codecov/codecov-action@v3
      with:
        file: ./frontend/coverage/lcov.info
        flags: frontend

  # Build and Security Scan
  build-and-scan:
    runs-on: ubuntu-latest
    needs: [backend-test, frontend-test]

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}

    - name: Build backend
      working-directory: ./backend
      run: |
        npm ci
        npm run build

    - name: Build frontend
      working-directory: ./frontend
      run: |
        npm ci
        npm run build

    - name: Run security audit
      run: |
        cd backend && npm audit --audit-level moderate
        cd ../frontend && npm audit --audit-level moderate

    - name: Setup Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Build Docker images
      run: |
        docker build -t collabflow-backend:test ./backend
        docker build -t collabflow-frontend:test ./frontend

    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        image-ref: 'collabflow-backend:test'
        format: 'sarif'
        output: 'trivy-results.sarif'

    - name: Upload Trivy scan results
      uses: github/codeql-action/upload-sarif@v2
      with:
        sarif_file: 'trivy-results.sarif'

  # Deploy to staging
  deploy-staging:
    runs-on: ubuntu-latest
    needs: [build-and-scan]
    if: github.ref == 'refs/heads/develop'
    environment: staging

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Login to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ghcr.io
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}

    - name: Build and push images
      run: |
        docker buildx build --platform linux/amd64,linux/arm64 \
          -t ghcr.io/${{ github.repository }}/backend:staging \
          --push ./backend
        
        docker buildx build --platform linux/amd64,linux/arm64 \
          -t ghcr.io/${{ github.repository }}/frontend:staging \
          --push ./frontend

    - name: Deploy to staging
      run: |
        echo "Deploying to staging environment..."
        # Add your staging deployment commands here

  # Deploy to production
  deploy-production:
    runs-on: ubuntu-latest
    needs: [build-and-scan]
    if: github.ref == 'refs/heads/main'
    environment: production

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Login to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ghcr.io
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}

    - name: Build and push images
      run: |
        docker buildx build --platform linux/amd64,linux/arm64 \
          -t ghcr.io/${{ github.repository }}/backend:latest \
          -t ghcr.io/${{ github.repository }}/backend:${{ github.sha }} \
          --push ./backend
        
        docker buildx build --platform linux/amd64,linux/arm64 \
          -t ghcr.io/${{ github.repository }}/frontend:latest \
          -t ghcr.io/${{ github.repository }}/frontend:${{ github.sha }} \
          --push ./frontend

    - name: Deploy to production
      run: |
        echo "Deploying to production environment..."
        # Add your production deployment commands here

  # Notify on deployment
  notify:
    runs-on: ubuntu-latest
    needs: [deploy-staging, deploy-production]
    if: always()

    steps:
    - name: Notify deployment status
      run: |
        if [ "${{ needs.deploy-staging.result }}" == "success" ] || [ "${{ needs.deploy-production.result }}" == "success" ]; then
          echo "Deployment successful!"
        else
          echo "Deployment failed!"
        fi
