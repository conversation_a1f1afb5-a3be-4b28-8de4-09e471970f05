import { jest } from '@jest/globals';

// Mock environment variables
process.env.NODE_ENV = 'test';
process.env.JWT_SECRET = 'test-jwt-secret';
process.env.DATABASE_URL = 'postgresql://test:test@localhost:5432/collabflow_test';
process.env.REDIS_URL = 'redis://localhost:6379/1';
process.env.OPENAI_API_KEY = 'test-openai-key';

// Mock external dependencies
jest.mock('../config/database', () => ({
  prisma: {
    user: {
      findUnique: jest.fn(),
      findMany: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      count: jest.fn(),
    },
    team: {
      findUnique: jest.fn(),
      findMany: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      count: jest.fn(),
    },
    project: {
      findUnique: jest.fn(),
      findMany: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      count: jest.fn(),
    },
    task: {
      findUnique: jest.fn(),
      findMany: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      count: jest.fn(),
      updateMany: jest.fn(),
    },
    meeting: {
      findUnique: jest.fn(),
      findMany: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      count: jest.fn(),
    },
    notification: {
      findUnique: jest.fn(),
      findMany: jest.fn(),
      create: jest.fn(),
      createMany: jest.fn(),
      update: jest.fn(),
      updateMany: jest.fn(),
      delete: jest.fn(),
      deleteMany: jest.fn(),
      count: jest.fn(),
    },
    teamMember: {
      findMany: jest.fn(),
      create: jest.fn(),
      delete: jest.fn(),
      update: jest.fn(),
    },
    userAvailability: {
      findMany: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    },
    taskDependency: {
      findMany: jest.fn(),
      create: jest.fn(),
      createMany: jest.fn(),
      delete: jest.fn(),
      deleteMany: jest.fn(),
    },
    meetingParticipant: {
      findMany: jest.fn(),
      create: jest.fn(),
      delete: jest.fn(),
      deleteMany: jest.fn(),
    },
    agendaItem: {
      findMany: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      deleteMany: jest.fn(),
    },
    $transaction: jest.fn(),
  },
}));

jest.mock('../config/redis', () => ({
  redisClient: {
    cacheGet: jest.fn(),
    cacheSet: jest.fn(),
    cacheDel: jest.fn(),
    cacheFlush: jest.fn(),
    isConnected: jest.fn().mockReturnValue(true),
  },
}));

jest.mock('openai', () => {
  return {
    __esModule: true,
    default: jest.fn().mockImplementation(() => ({
      chat: {
        completions: {
          create: jest.fn(),
        },
      },
    })),
  };
});

jest.mock('../config/logger', () => ({
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
  },
}));

// Mock Socket.IO
jest.mock('socket.io', () => ({
  Server: jest.fn().mockImplementation(() => ({
    use: jest.fn(),
    on: jest.fn(),
    to: jest.fn().mockReturnThis(),
    emit: jest.fn(),
  })),
}));

// Global test utilities
global.mockUser = {
  id: 'test-user-id',
  email: '<EMAIL>',
  firstName: 'Test',
  lastName: 'User',
  role: 'MEMBER',
  skills: ['JavaScript', 'TypeScript'],
  timezone: 'UTC',
  createdAt: new Date(),
  updatedAt: new Date(),
};

global.mockTeam = {
  id: 'test-team-id',
  name: 'Test Team',
  description: 'A test team',
  memberCount: 3,
  projectCount: 2,
  createdAt: new Date(),
  updatedAt: new Date(),
};

global.mockProject = {
  id: 'test-project-id',
  name: 'Test Project',
  description: 'A test project',
  teamId: 'test-team-id',
  status: 'ACTIVE',
  priority: 'HIGH',
  startDate: new Date(),
  endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
  createdAt: new Date(),
  updatedAt: new Date(),
};

global.mockTask = {
  id: 'test-task-id',
  title: 'Test Task',
  description: 'A test task',
  projectId: 'test-project-id',
  assigneeId: 'test-user-id',
  status: 'TODO',
  priority: 'MEDIUM',
  estimatedHours: 8,
  actualHours: 0,
  tags: ['testing'],
  dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
  createdAt: new Date(),
  updatedAt: new Date(),
};

global.mockMeeting = {
  id: 'test-meeting-id',
  title: 'Test Meeting',
  description: 'A test meeting',
  organizerId: 'test-user-id',
  startTime: new Date(Date.now() + 24 * 60 * 60 * 1000),
  endTime: new Date(Date.now() + 24 * 60 * 60 * 1000 + 60 * 60 * 1000),
  location: 'Conference Room A',
  meetingUrl: 'https://zoom.us/j/123456789',
  necessityScore: 75,
  status: 'SCHEDULED',
  createdAt: new Date(),
  updatedAt: new Date(),
};

global.mockNotification = {
  id: 'test-notification-id',
  userId: 'test-user-id',
  type: 'TASK_ASSIGNED',
  title: 'Task Assigned',
  message: 'You have been assigned a new task',
  data: { taskId: 'test-task-id' },
  read: false,
  createdAt: new Date(),
};

// Reset all mocks before each test
beforeEach(() => {
  jest.clearAllMocks();
});
