import { prisma } from '../config/database';
import { redisClient } from '../config/redis';
import { logger } from '../config/logger';
import { CustomError } from '../middleware/errorHandler';
import { NotificationType } from '@prisma/client';

export interface NotificationData {
  userId: string;
  type: NotificationType;
  title: string;
  message: string;
  data?: any;
}

export interface BulkNotificationData {
  userIds: string[];
  type: NotificationType;
  title: string;
  message: string;
  data?: any;
}

class NotificationService {
  private readonly CACHE_TTL = 3600; // 1 hour

  async createNotification(notificationData: NotificationData) {
    try {
      const notification = await prisma.notification.create({
        data: notificationData,
      });

      // Cache the notification for real-time delivery
      await this.cacheNotification(notification);

      logger.info(`Notification created for user ${notificationData.userId}: ${notificationData.title}`);

      return notification;
    } catch (error) {
      logger.error('Error creating notification:', error);
      throw new CustomError('Failed to create notification', 500);
    }
  }

  async createBulkNotifications(bulkData: BulkNotificationData) {
    try {
      const notifications = await prisma.notification.createMany({
        data: bulkData.userIds.map(userId => ({
          userId,
          type: bulkData.type,
          title: bulkData.title,
          message: bulkData.message,
          data: bulkData.data,
        })),
      });

      // Cache notifications for real-time delivery
      for (const userId of bulkData.userIds) {
        await this.cacheUserNotificationCount(userId);
      }

      logger.info(`${notifications.count} bulk notifications created`);

      return notifications;
    } catch (error) {
      logger.error('Error creating bulk notifications:', error);
      throw new CustomError('Failed to create bulk notifications', 500);
    }
  }

  async getUserNotifications(userId: string, page: number = 1, limit: number = 20) {
    try {
      const skip = (page - 1) * limit;

      const [notifications, total, unreadCount] = await Promise.all([
        prisma.notification.findMany({
          where: { userId },
          orderBy: { createdAt: 'desc' },
          skip,
          take: limit,
        }),
        prisma.notification.count({ where: { userId } }),
        prisma.notification.count({ where: { userId, read: false } }),
      ]);

      return {
        notifications,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
          hasNextPage: page < Math.ceil(total / limit),
          hasPrevPage: page > 1,
        },
        unreadCount,
      };
    } catch (error) {
      logger.error('Error getting user notifications:', error);
      throw new CustomError('Failed to get notifications', 500);
    }
  }

  async markAsRead(notificationId: string, userId: string) {
    try {
      const notification = await prisma.notification.updateMany({
        where: {
          id: notificationId,
          userId,
        },
        data: {
          read: true,
        },
      });

      if (notification.count === 0) {
        throw new CustomError('Notification not found', 404);
      }

      // Update cached unread count
      await this.cacheUserNotificationCount(userId);

      logger.info(`Notification ${notificationId} marked as read for user ${userId}`);

      return notification;
    } catch (error) {
      logger.error('Error marking notification as read:', error);
      throw error;
    }
  }

  async markAllAsRead(userId: string) {
    try {
      const result = await prisma.notification.updateMany({
        where: {
          userId,
          read: false,
        },
        data: {
          read: true,
        },
      });

      // Update cached unread count
      await this.cacheUserNotificationCount(userId);

      logger.info(`${result.count} notifications marked as read for user ${userId}`);

      return result;
    } catch (error) {
      logger.error('Error marking all notifications as read:', error);
      throw new CustomError('Failed to mark notifications as read', 500);
    }
  }

  async deleteNotification(notificationId: string, userId: string) {
    try {
      const notification = await prisma.notification.deleteMany({
        where: {
          id: notificationId,
          userId,
        },
      });

      if (notification.count === 0) {
        throw new CustomError('Notification not found', 404);
      }

      // Update cached unread count
      await this.cacheUserNotificationCount(userId);

      logger.info(`Notification ${notificationId} deleted for user ${userId}`);

      return notification;
    } catch (error) {
      logger.error('Error deleting notification:', error);
      throw error;
    }
  }

  async getUnreadCount(userId: string): Promise<number> {
    try {
      // Try to get from cache first
      const cacheKey = `user:${userId}:unread_notifications`;
      const cachedCount = await redisClient.cacheGet(cacheKey);

      if (cachedCount !== null) {
        return parseInt(cachedCount as string, 10);
      }

      // Get from database and cache
      const count = await prisma.notification.count({
        where: {
          userId,
          read: false,
        },
      });

      await redisClient.cacheSet(cacheKey, count.toString(), this.CACHE_TTL);

      return count;
    } catch (error) {
      logger.error('Error getting unread count:', error);
      return 0; // Return 0 on error to prevent UI issues
    }
  }

  // Notification templates for common scenarios
  async notifyTaskAssigned(taskId: string, assigneeId: string, assignedBy: string) {
    const task = await prisma.task.findUnique({
      where: { id: taskId },
      include: {
        project: {
          select: { name: true },
        },
      },
    });

    if (!task) return;

    const assigner = await prisma.user.findUnique({
      where: { id: assignedBy },
      select: { firstName: true, lastName: true },
    });

    await this.createNotification({
      userId: assigneeId,
      type: 'TASK_ASSIGNED',
      title: 'New Task Assigned',
      message: `${assigner?.firstName} ${assigner?.lastName} assigned you a task: "${task.title}" in project ${task.project.name}`,
      data: {
        taskId,
        projectId: task.projectId,
        assignedBy,
      },
    });
  }

  async notifyTaskDue(taskId: string) {
    const task = await prisma.task.findUnique({
      where: { id: taskId },
      include: {
        assignee: {
          select: { id: true },
        },
        project: {
          select: { name: true },
        },
      },
    });

    if (!task || !task.assignee) return;

    await this.createNotification({
      userId: task.assignee.id,
      type: 'TASK_DUE',
      title: 'Task Due Soon',
      message: `Task "${task.title}" in project ${task.project.name} is due soon`,
      data: {
        taskId,
        projectId: task.projectId,
        dueDate: task.dueDate,
      },
    });
  }

  async notifyMeetingReminder(meetingId: string, reminderMinutes: number = 15) {
    const meeting = await prisma.meeting.findUnique({
      where: { id: meetingId },
      include: {
        participants: {
          select: { userId: true },
        },
      },
    });

    if (!meeting) return;

    const participantIds = meeting.participants.map(p => p.userId);

    await this.createBulkNotifications({
      userIds: participantIds,
      type: 'MEETING_REMINDER',
      title: 'Meeting Reminder',
      message: `Meeting "${meeting.title}" starts in ${reminderMinutes} minutes`,
      data: {
        meetingId,
        startTime: meeting.startTime,
        location: meeting.location,
        meetingUrl: meeting.meetingUrl,
      },
    });
  }

  async notifyTeamInvitation(teamId: string, invitedUserId: string, invitedBy: string) {
    const [team, inviter] = await Promise.all([
      prisma.team.findUnique({
        where: { id: teamId },
        select: { name: true },
      }),
      prisma.user.findUnique({
        where: { id: invitedBy },
        select: { firstName: true, lastName: true },
      }),
    ]);

    if (!team || !inviter) return;

    await this.createNotification({
      userId: invitedUserId,
      type: 'TEAM_INVITATION',
      title: 'Team Invitation',
      message: `${inviter.firstName} ${inviter.lastName} invited you to join team "${team.name}"`,
      data: {
        teamId,
        invitedBy,
      },
    });
  }

  async notifyDependencyCompleted(taskId: string) {
    // Find tasks that depend on the completed task
    const dependentTasks = await prisma.taskDependency.findMany({
      where: { dependencyId: taskId },
      include: {
        task: {
          include: {
            assignee: {
              select: { id: true },
            },
          },
        },
        dependency: {
          select: { title: true },
        },
      },
    });

    for (const dep of dependentTasks) {
      if (dep.task.assignee) {
        await this.createNotification({
          userId: dep.task.assignee.id,
          type: 'DEPENDENCY_COMPLETED',
          title: 'Dependency Completed',
          message: `Task "${dep.dependency.title}" has been completed. You can now work on "${dep.task.title}"`,
          data: {
            taskId: dep.task.id,
            dependencyId: taskId,
          },
        });
      }
    }
  }

  private async cacheNotification(notification: any) {
    const cacheKey = `user:${notification.userId}:latest_notification`;
    await redisClient.cacheSet(cacheKey, notification, 300); // 5 minutes
  }

  private async cacheUserNotificationCount(userId: string) {
    const count = await prisma.notification.count({
      where: {
        userId,
        read: false,
      },
    });

    const cacheKey = `user:${userId}:unread_notifications`;
    await redisClient.cacheSet(cacheKey, count.toString(), this.CACHE_TTL);

    return count;
  }
}

export const notificationService = new NotificationService();
