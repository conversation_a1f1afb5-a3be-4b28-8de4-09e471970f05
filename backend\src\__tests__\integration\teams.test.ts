import { jest } from '@jest/globals';
import app from '../../app';
import { TestHelper } from '../helpers/testHelper';
import { prisma } from '../../config/database';

const mockPrisma = prisma as jest.Mocked<typeof prisma>;
const testHelper = new TestHelper(app.app);

describe('Teams API Integration Tests', () => {
  let mockUser: any;
  let mockAdmin: any;
  let mockTeam: any;

  beforeEach(async () => {
    await testHelper.cleanupDatabase();
    jest.clearAllMocks();

    mockUser = testHelper.createMockUser();
    mockAdmin = testHelper.createMockAdmin();
    mockTeam = testHelper.createMockTeam();
  });

  afterAll(async () => {
    await testHelper.cleanupDatabase();
  });

  describe('GET /api/teams', () => {
    it('should return user teams successfully', async () => {
      // Arrange
      const mockTeams = [mockTeam, testHelper.createMockTeam({ id: 'team2', name: 'Team 2' })];
      mockPrisma.team.findMany.mockResolvedValue(mockTeams as any);

      // Act
      const response = await testHelper.authenticatedRequest(mockUser)
        .get('/api/teams');

      // Assert
      testHelper.expectSuccessResponse(response);
      expect(Array.isArray(response.body.data)).toBe(true);
      expect(response.body.data).toHaveLength(2);
      expect(response.body.data[0]).toMatchObject({
        id: mockTeam.id,
        name: mockTeam.name,
      });
    });

    it('should support pagination', async () => {
      // Arrange
      const mockTeams = Array.from({ length: 5 }, (_, i) => 
        testHelper.createMockTeam({ id: `team${i}`, name: `Team ${i}` })
      );
      mockPrisma.team.findMany.mockResolvedValue(mockTeams.slice(0, 2) as any);
      mockPrisma.team.count.mockResolvedValue(5);

      // Act
      const response = await testHelper.authenticatedRequest(mockUser)
        .get('/api/teams')
        .query({ page: 1, limit: 2 });

      // Assert
      testHelper.expectPaginatedResponse(response);
      expect(response.body.data).toHaveLength(2);
      expect(response.body.meta.total).toBe(5);
      expect(response.body.meta.totalPages).toBe(3);
    });

    it('should support search functionality', async () => {
      // Arrange
      const searchTerm = 'Development';
      const mockTeams = [testHelper.createMockTeam({ name: 'Development Team' })];
      mockPrisma.team.findMany.mockResolvedValue(mockTeams as any);

      // Act
      const response = await testHelper.authenticatedRequest(mockUser)
        .get('/api/teams')
        .query({ search: searchTerm });

      // Assert
      testHelper.expectSuccessResponse(response);
      expect(mockPrisma.team.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            name: expect.objectContaining({
              contains: searchTerm,
              mode: 'insensitive',
            }),
          }),
        })
      );
    });

    it('should require authentication', async () => {
      // Act
      const response = await testHelper.unauthenticatedRequest()
        .get('/api/teams');

      // Assert
      testHelper.expectAuthenticationError(response);
    });
  });

  describe('POST /api/teams', () => {
    it('should create team successfully', async () => {
      // Arrange
      const teamData = {
        name: 'New Team',
        description: 'A new team for testing',
      };

      const createdTeam = testHelper.createMockTeam({
        name: teamData.name,
        description: teamData.description,
      });

      mockPrisma.team.create.mockResolvedValue(createdTeam as any);

      // Act
      const response = await testHelper.authenticatedRequest(mockUser)
        .post('/api/teams')
        .send(teamData);

      // Assert
      expect(response.status).toBe(201);
      testHelper.expectSuccessResponse(response);
      expect(response.body.data).toMatchObject({
        name: teamData.name,
        description: teamData.description,
      });
      expect(mockPrisma.team.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          name: teamData.name,
          description: teamData.description,
          members: {
            create: {
              userId: mockUser.id,
              role: 'LEAD',
            },
          },
        }),
        include: expect.any(Object),
      });
    });

    it('should validate required fields', async () => {
      // Act
      const response = await testHelper.authenticatedRequest(mockUser)
        .post('/api/teams')
        .send({});

      // Assert
      testHelper.expectValidationError(response, 'name');
    });

    it('should validate team name length', async () => {
      // Act
      const response = await testHelper.authenticatedRequest(mockUser)
        .post('/api/teams')
        .send({ name: 'A' }); // Too short

      // Assert
      testHelper.expectValidationError(response, 'name');
    });

    it('should validate description length', async () => {
      // Act
      const response = await testHelper.authenticatedRequest(mockUser)
        .post('/api/teams')
        .send({
          name: 'Valid Team Name',
          description: 'A'.repeat(1001), // Too long
        });

      // Assert
      testHelper.expectValidationError(response, 'description');
    });

    it('should require authentication', async () => {
      // Act
      const response = await testHelper.unauthenticatedRequest()
        .post('/api/teams')
        .send({ name: 'Test Team' });

      // Assert
      testHelper.expectAuthenticationError(response);
    });
  });

  describe('GET /api/teams/:id', () => {
    it('should return team details successfully', async () => {
      // Arrange
      const teamWithDetails = {
        ...mockTeam,
        members: [
          {
            userId: mockUser.id,
            role: 'LEAD',
            user: mockUser,
          },
        ],
        projects: [
          testHelper.createMockProject(),
        ],
      };

      mockPrisma.team.findFirst.mockResolvedValue(teamWithDetails as any);

      // Act
      const response = await testHelper.authenticatedRequest(mockUser)
        .get(`/api/teams/${mockTeam.id}`);

      // Assert
      testHelper.expectSuccessResponse(response);
      expect(response.body.data).toMatchObject({
        id: mockTeam.id,
        name: mockTeam.name,
      });
      expect(response.body.data).toHaveProperty('members');
      expect(response.body.data).toHaveProperty('projects');
    });

    it('should return 404 for non-existent team', async () => {
      // Arrange
      mockPrisma.team.findFirst.mockResolvedValue(null);

      // Act
      const response = await testHelper.authenticatedRequest(mockUser)
        .get('/api/teams/nonexistent-id');

      // Assert
      testHelper.expectNotFoundError(response);
    });

    it('should return 404 for team user is not member of', async () => {
      // Arrange
      mockPrisma.team.findFirst.mockResolvedValue(null); // No team found with user membership

      // Act
      const response = await testHelper.authenticatedRequest(mockUser)
        .get(`/api/teams/${mockTeam.id}`);

      // Assert
      testHelper.expectNotFoundError(response);
    });

    it('should validate team ID format', async () => {
      // Act
      const response = await testHelper.authenticatedRequest(mockUser)
        .get('/api/teams/invalid-id');

      // Assert
      testHelper.expectValidationError(response, 'id');
    });
  });

  describe('PUT /api/teams/:id', () => {
    it('should update team successfully as team lead', async () => {
      // Arrange
      const updateData = {
        name: 'Updated Team Name',
        description: 'Updated description',
      };

      const teamWithMembership = {
        ...mockTeam,
        members: [{ userId: mockUser.id, role: 'LEAD' }],
      };

      const updatedTeam = { ...teamWithMembership, ...updateData };

      mockPrisma.team.findFirst.mockResolvedValue(teamWithMembership as any);
      mockPrisma.team.update.mockResolvedValue(updatedTeam as any);

      // Act
      const response = await testHelper.authenticatedRequest(mockUser)
        .put(`/api/teams/${mockTeam.id}`)
        .send(updateData);

      // Assert
      testHelper.expectSuccessResponse(response);
      expect(response.body.data).toMatchObject(updateData);
      expect(mockPrisma.team.update).toHaveBeenCalledWith({
        where: { id: mockTeam.id },
        data: updateData,
        include: expect.any(Object),
      });
    });

    it('should allow admin to update any team', async () => {
      // Arrange
      const updateData = { name: 'Admin Updated Team' };
      const teamWithoutAdminMembership = {
        ...mockTeam,
        members: [{ userId: 'other-user', role: 'LEAD' }],
      };

      mockPrisma.team.findFirst.mockResolvedValue(teamWithoutAdminMembership as any);
      mockPrisma.team.update.mockResolvedValue({ ...teamWithoutAdminMembership, ...updateData } as any);

      // Act
      const response = await testHelper.authenticatedRequest(mockAdmin)
        .put(`/api/teams/${mockTeam.id}`)
        .send(updateData);

      // Assert
      testHelper.expectSuccessResponse(response);
    });

    it('should reject update from non-lead member', async () => {
      // Arrange
      const teamWithMembership = {
        ...mockTeam,
        members: [{ userId: mockUser.id, role: 'MEMBER' }],
      };

      mockPrisma.team.findFirst.mockResolvedValue(teamWithMembership as any);

      // Act
      const response = await testHelper.authenticatedRequest(mockUser)
        .put(`/api/teams/${mockTeam.id}`)
        .send({ name: 'Updated Name' });

      // Assert
      testHelper.expectAuthorizationError(response);
    });

    it('should return 404 for non-existent team', async () => {
      // Arrange
      mockPrisma.team.findFirst.mockResolvedValue(null);

      // Act
      const response = await testHelper.authenticatedRequest(mockUser)
        .put(`/api/teams/${mockTeam.id}`)
        .send({ name: 'Updated Name' });

      // Assert
      testHelper.expectNotFoundError(response);
    });
  });

  describe('DELETE /api/teams/:id', () => {
    it('should delete team successfully as team lead', async () => {
      // Arrange
      const teamWithMembership = {
        ...mockTeam,
        members: [{ userId: mockUser.id, role: 'LEAD' }],
      };

      mockPrisma.team.findFirst.mockResolvedValue(teamWithMembership as any);
      mockPrisma.team.delete.mockResolvedValue(teamWithMembership as any);

      // Act
      const response = await testHelper.authenticatedRequest(mockUser)
        .delete(`/api/teams/${mockTeam.id}`);

      // Assert
      testHelper.expectSuccessResponse(response);
      expect(mockPrisma.team.delete).toHaveBeenCalledWith({
        where: { id: mockTeam.id },
      });
    });

    it('should allow admin to delete any team', async () => {
      // Arrange
      const teamWithoutAdminMembership = {
        ...mockTeam,
        members: [{ userId: 'other-user', role: 'LEAD' }],
      };

      mockPrisma.team.findFirst.mockResolvedValue(teamWithoutAdminMembership as any);
      mockPrisma.team.delete.mockResolvedValue(teamWithoutAdminMembership as any);

      // Act
      const response = await testHelper.authenticatedRequest(mockAdmin)
        .delete(`/api/teams/${mockTeam.id}`);

      // Assert
      testHelper.expectSuccessResponse(response);
    });

    it('should reject deletion from non-lead member', async () => {
      // Arrange
      const teamWithMembership = {
        ...mockTeam,
        members: [{ userId: mockUser.id, role: 'MEMBER' }],
      };

      mockPrisma.team.findFirst.mockResolvedValue(teamWithMembership as any);

      // Act
      const response = await testHelper.authenticatedRequest(mockUser)
        .delete(`/api/teams/${mockTeam.id}`);

      // Assert
      testHelper.expectAuthorizationError(response);
    });
  });

  describe('POST /api/teams/:id/members', () => {
    it('should add team member successfully as team lead', async () => {
      // Arrange
      const newMemberData = {
        userId: 'new-user-id',
        role: 'MEMBER' as const,
      };

      const teamWithMembership = {
        ...mockTeam,
        members: [{ userId: mockUser.id, role: 'LEAD' }],
      };

      const newMember = {
        userId: newMemberData.userId,
        teamId: mockTeam.id,
        role: newMemberData.role,
        user: testHelper.createMockUser({ id: newMemberData.userId }),
      };

      mockPrisma.team.findFirst.mockResolvedValue(teamWithMembership as any);
      mockPrisma.user.findUnique.mockResolvedValue(newMember.user as any);
      mockPrisma.teamMember.create.mockResolvedValue(newMember as any);

      // Act
      const response = await testHelper.authenticatedRequest(mockUser)
        .post(`/api/teams/${mockTeam.id}/members`)
        .send(newMemberData);

      // Assert
      expect(response.status).toBe(201);
      testHelper.expectSuccessResponse(response);
      expect(response.body.data).toMatchObject({
        userId: newMemberData.userId,
        role: newMemberData.role,
      });
    });

    it('should reject adding member as non-lead', async () => {
      // Arrange
      const teamWithMembership = {
        ...mockTeam,
        members: [{ userId: mockUser.id, role: 'MEMBER' }],
      };

      mockPrisma.team.findFirst.mockResolvedValue(teamWithMembership as any);

      // Act
      const response = await testHelper.authenticatedRequest(mockUser)
        .post(`/api/teams/${mockTeam.id}/members`)
        .send({ userId: 'new-user-id', role: 'MEMBER' });

      // Assert
      testHelper.expectAuthorizationError(response);
    });

    it('should reject adding non-existent user', async () => {
      // Arrange
      const teamWithMembership = {
        ...mockTeam,
        members: [{ userId: mockUser.id, role: 'LEAD' }],
      };

      mockPrisma.team.findFirst.mockResolvedValue(teamWithMembership as any);
      mockPrisma.user.findUnique.mockResolvedValue(null);

      // Act
      const response = await testHelper.authenticatedRequest(mockUser)
        .post(`/api/teams/${mockTeam.id}/members`)
        .send({ userId: 'nonexistent-user', role: 'MEMBER' });

      // Assert
      testHelper.expectNotFoundError(response);
    });
  });

  describe('DELETE /api/teams/:id/members/:userId', () => {
    it('should remove team member successfully as team lead', async () => {
      // Arrange
      const memberToRemove = 'member-to-remove-id';
      const teamWithMembership = {
        ...mockTeam,
        members: [
          { userId: mockUser.id, role: 'LEAD' },
          { userId: memberToRemove, role: 'MEMBER' },
        ],
      };

      mockPrisma.team.findFirst.mockResolvedValue(teamWithMembership as any);
      mockPrisma.teamMember.deleteMany.mockResolvedValue({ count: 1 });

      // Act
      const response = await testHelper.authenticatedRequest(mockUser)
        .delete(`/api/teams/${mockTeam.id}/members/${memberToRemove}`);

      // Assert
      testHelper.expectSuccessResponse(response);
      expect(mockPrisma.teamMember.deleteMany).toHaveBeenCalledWith({
        where: {
          teamId: mockTeam.id,
          userId: memberToRemove,
        },
      });
    });

    it('should allow member to remove themselves', async () => {
      // Arrange
      const teamWithMembership = {
        ...mockTeam,
        members: [{ userId: mockUser.id, role: 'MEMBER' }],
      };

      mockPrisma.team.findFirst.mockResolvedValue(teamWithMembership as any);
      mockPrisma.teamMember.deleteMany.mockResolvedValue({ count: 1 });

      // Act
      const response = await testHelper.authenticatedRequest(mockUser)
        .delete(`/api/teams/${mockTeam.id}/members/${mockUser.id}`);

      // Assert
      testHelper.expectSuccessResponse(response);
    });

    it('should reject removing other members as non-lead', async () => {
      // Arrange
      const teamWithMembership = {
        ...mockTeam,
        members: [{ userId: mockUser.id, role: 'MEMBER' }],
      };

      mockPrisma.team.findFirst.mockResolvedValue(teamWithMembership as any);

      // Act
      const response = await testHelper.authenticatedRequest(mockUser)
        .delete(`/api/teams/${mockTeam.id}/members/other-member-id`);

      // Assert
      testHelper.expectAuthorizationError(response);
    });
  });
});

// Continue with additional integration tests for tasks, meetings, AI, and notifications...
