#!/bin/bash

# CollabFlow Ollama Setup Script
# This script sets up Ollama with the recommended models for CollabFlow

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check system requirements
check_system_requirements() {
    print_status "Checking system requirements..."
    
    # Check available RAM
    if command_exists free; then
        TOTAL_RAM=$(free -g | awk '/^Mem:/{print $2}')
        if [ "$TOTAL_RAM" -lt 8 ]; then
            print_warning "System has ${TOTAL_RAM}GB RAM. 8GB+ recommended for optimal performance."
            print_warning "Consider using smaller models (3b instead of 7b)."
        else
            print_success "System has ${TOTAL_RAM}GB RAM - sufficient for recommended models."
        fi
    fi
    
    # Check available disk space
    AVAILABLE_SPACE=$(df -BG . | awk 'NR==2 {print $4}' | sed 's/G//')
    if [ "$AVAILABLE_SPACE" -lt 10 ]; then
        print_warning "Available disk space: ${AVAILABLE_SPACE}GB. 10GB+ recommended."
    else
        print_success "Available disk space: ${AVAILABLE_SPACE}GB - sufficient."
    fi
}

# Function to install Ollama
install_ollama() {
    if command_exists ollama; then
        print_success "Ollama is already installed."
        ollama --version
        return 0
    fi
    
    print_status "Installing Ollama..."
    
    # Detect OS and install accordingly
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        # Linux
        print_status "Detected Linux. Installing Ollama..."
        curl -fsSL https://ollama.ai/install.sh | sh
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        if command_exists brew; then
            print_status "Detected macOS with Homebrew. Installing Ollama..."
            brew install ollama
        else
            print_error "Homebrew not found. Please install Homebrew or download Ollama manually from https://ollama.ai/download"
            exit 1
        fi
    else
        print_error "Unsupported operating system. Please install Ollama manually from https://ollama.ai/download"
        exit 1
    fi
    
    print_success "Ollama installed successfully!"
}

# Function to start Ollama service
start_ollama() {
    print_status "Starting Ollama service..."
    
    # Check if Ollama is already running
    if curl -s http://localhost:11434/api/tags >/dev/null 2>&1; then
        print_success "Ollama is already running."
        return 0
    fi
    
    # Start Ollama in background
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        # Linux - use systemd if available
        if command_exists systemctl; then
            sudo systemctl start ollama 2>/dev/null || ollama serve &
        else
            ollama serve &
        fi
    else
        # macOS or other
        ollama serve &
    fi
    
    # Wait for Ollama to start
    print_status "Waiting for Ollama to start..."
    for i in {1..30}; do
        if curl -s http://localhost:11434/api/tags >/dev/null 2>&1; then
            print_success "Ollama is running!"
            return 0
        fi
        sleep 1
    done
    
    print_error "Failed to start Ollama service."
    exit 1
}

# Function to pull models
pull_models() {
    print_status "Pulling recommended models for CollabFlow..."
    
    # Default model for general use
    print_status "Pulling Llama 2 7B (recommended for general use)..."
    if ollama pull llama2:7b; then
        print_success "Successfully pulled llama2:7b"
    else
        print_error "Failed to pull llama2:7b"
        exit 1
    fi
    
    # Ask user if they want additional models
    echo
    read -p "Do you want to pull additional models? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        # Mistral for reasoning tasks
        print_status "Pulling Mistral 7B (excellent for reasoning)..."
        if ollama pull mistral:7b; then
            print_success "Successfully pulled mistral:7b"
        else
            print_warning "Failed to pull mistral:7b - continuing..."
        fi
        
        # Code Llama for code tasks
        print_status "Pulling Code Llama 7B (optimized for code)..."
        if ollama pull codellama:7b; then
            print_success "Successfully pulled codellama:7b"
        else
            print_warning "Failed to pull codellama:7b - continuing..."
        fi
    fi
    
    # For systems with limited RAM, offer smaller model
    if [ "${TOTAL_RAM:-8}" -lt 8 ]; then
        echo
        read -p "Your system has limited RAM. Pull Llama 2 3B (lighter model)? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            print_status "Pulling Llama 2 3B (lighter model)..."
            if ollama pull llama2:3b; then
                print_success "Successfully pulled llama2:3b"
            else
                print_warning "Failed to pull llama2:3b - continuing..."
            fi
        fi
    fi
}

# Function to test installation
test_installation() {
    print_status "Testing Ollama installation..."
    
    # Test API endpoint
    if ! curl -s http://localhost:11434/api/tags >/dev/null; then
        print_error "Ollama API is not responding"
        exit 1
    fi
    
    # Test model completion
    print_status "Testing model completion..."
    RESPONSE=$(curl -s http://localhost:11434/api/generate -d '{
        "model": "llama2:7b",
        "prompt": "Hello",
        "stream": false
    }' | grep -o '"response":"[^"]*"' | cut -d'"' -f4)
    
    if [ -n "$RESPONSE" ]; then
        print_success "Model test completed successfully!"
        print_status "Test response: $RESPONSE"
    else
        print_error "Model test failed"
        exit 1
    fi
}

# Function to show next steps
show_next_steps() {
    echo
    print_success "🎉 Ollama setup completed successfully!"
    echo
    print_status "Next steps:"
    echo "1. Update your CollabFlow .env file with:"
    echo "   AI_PROVIDER=ollama"
    echo "   OLLAMA_BASE_URL=http://localhost:11434"
    echo "   OLLAMA_MODEL=llama2:7b"
    echo
    echo "2. Start your CollabFlow backend:"
    echo "   cd backend && npm run dev"
    echo
    echo "3. Test the AI integration:"
    echo "   curl http://localhost:3001/api/health/ai"
    echo
    print_status "Available models:"
    ollama list
    echo
    print_status "For more information, see docs/OLLAMA_SETUP.md"
}

# Main execution
main() {
    echo "🚀 CollabFlow Ollama Setup Script"
    echo "=================================="
    echo
    
    check_system_requirements
    install_ollama
    start_ollama
    pull_models
    test_installation
    show_next_steps
}

# Handle script arguments
case "${1:-}" in
    --help|-h)
        echo "CollabFlow Ollama Setup Script"
        echo
        echo "Usage: $0 [options]"
        echo
        echo "Options:"
        echo "  --help, -h     Show this help message"
        echo "  --models-only  Only pull models (skip installation)"
        echo "  --test-only    Only test existing installation"
        echo
        exit 0
        ;;
    --models-only)
        print_status "Pulling models only..."
        start_ollama
        pull_models
        exit 0
        ;;
    --test-only)
        print_status "Testing installation only..."
        test_installation
        exit 0
        ;;
    *)
        main
        ;;
esac
