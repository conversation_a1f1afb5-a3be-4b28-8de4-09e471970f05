version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: collabflow-postgres
    environment:
      POSTGRES_DB: collabflow
      POSTGRES_USER: collabflow_user
      POSTGRES_PASSWORD: collabflow_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/database/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - collabflow-network

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: collabflow-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - collabflow-network

  # Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: collabflow-backend
    environment:
      NODE_ENV: development
      PORT: 3001
      DATABASE_URL: **************************************************************/collabflow
      REDIS_URL: redis://redis:6379
      JWT_SECRET: your-development-jwt-secret
    ports:
      - "3001:3001"
    volumes:
      - ./backend:/app
      - /app/node_modules
    depends_on:
      - postgres
      - redis
    networks:
      - collabflow-network
    command: npm run dev

  # Frontend Application
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: collabflow-frontend
    environment:
      REACT_APP_API_URL: http://localhost:3001/api
      REACT_APP_WS_URL: ws://localhost:3001
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    depends_on:
      - backend
    networks:
      - collabflow-network
    command: npm start

  # Nginx Reverse Proxy (Optional for production)
  nginx:
    image: nginx:alpine
    container_name: collabflow-nginx
    ports:
      - "80:80"
    volumes:
      - ./docker/nginx.conf:/etc/nginx/nginx.conf
    depends_on:
      - frontend
      - backend
    networks:
      - collabflow-network
    profiles:
      - production

volumes:
  postgres_data:
  redis_data:

networks:
  collabflow-network:
    driver: bridge
