# CollabFlow API Documentation

## Overview

The CollabFlow API is a RESTful API that provides endpoints for managing teams, projects, tasks, meetings, and analytics. All API endpoints are prefixed with `/api` and require authentication unless otherwise specified.

## Base URL

```
Development: http://localhost:3001/api
Production: https://api.collabflow.dev/api
```

## Authentication

CollabFlow uses JWT (JSON Web Tokens) for authentication. Include the token in the Authorization header:

```
Authorization: Bearer <your-jwt-token>
```

### Authentication Endpoints

#### POST /auth/register
Register a new user account.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "securePassword123",
  "firstName": "John",
  "lastName": "Doe",
  "skills": ["JavaScript", "React"],
  "timezone": "America/New_York"
}
```

**Response:**
```json
{
  "success": true,
  "message": "User registered successfully",
  "data": {
    "user": {
      "id": "user-id",
      "email": "<EMAIL>",
      "firstName": "John",
      "lastName": "Doe",
      "role": "MEMBER"
    },
    "tokens": {
      "accessToken": "jwt-access-token",
      "refreshToken": "jwt-refresh-token"
    }
  }
}
```

#### POST /auth/login
Authenticate user and receive tokens.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "securePassword123"
}
```

#### POST /auth/refresh
Refresh access token using refresh token.

**Request Body:**
```json
{
  "refreshToken": "jwt-refresh-token"
}
```

#### POST /auth/logout
Logout user and invalidate refresh token.

**Request Body:**
```json
{
  "refreshToken": "jwt-refresh-token"
}
```

#### GET /auth/me
Get current user information.

**Response:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "user-id",
      "email": "<EMAIL>",
      "firstName": "John",
      "lastName": "Doe",
      "role": "MEMBER",
      "skills": ["JavaScript", "React"],
      "timezone": "America/New_York"
    }
  }
}
```

## User Management

### GET /users
Get list of users (Admin/Manager only).

**Query Parameters:**
- `page` (number): Page number (default: 1)
- `limit` (number): Items per page (default: 10)
- `search` (string): Search term
- `role` (string): Filter by role

### GET /users/:id
Get user by ID.

### PUT /users/:id
Update user information.

### DELETE /users/:id
Delete user (Admin only).

### GET /users/:id/availability
Get user availability schedule.

### PUT /users/:id/availability
Update user availability schedule.

## Team Management

### GET /teams
Get list of teams for current user.

### POST /teams
Create a new team.

**Request Body:**
```json
{
  "name": "Development Team",
  "description": "Main development team for the project"
}
```

### GET /teams/:id
Get team details including members and projects.

### PUT /teams/:id
Update team information.

### DELETE /teams/:id
Delete team (Team Lead/Admin only).

### POST /teams/:id/members
Add member to team.

**Request Body:**
```json
{
  "userId": "user-id",
  "role": "MEMBER"
}
```

### DELETE /teams/:id/members/:userId
Remove member from team.

### PATCH /teams/:id/members/:userId/role
Update member role.

## Project Management

### GET /projects
Get list of projects.

### POST /projects
Create a new project.

**Request Body:**
```json
{
  "name": "CollabFlow MVP",
  "description": "Minimum viable product development",
  "teamId": "team-id",
  "priority": "HIGH",
  "startDate": "2024-01-01T00:00:00Z",
  "endDate": "2024-03-31T23:59:59Z"
}
```

### GET /projects/:id
Get project details.

### PUT /projects/:id
Update project information.

### DELETE /projects/:id
Delete project.

## Task Management

### GET /tasks
Get list of tasks.

**Query Parameters:**
- `status` (string): Filter by status
- `priority` (string): Filter by priority
- `assigneeId` (string): Filter by assignee
- `projectId` (string): Filter by project

### POST /tasks
Create a new task.

**Request Body:**
```json
{
  "title": "Implement authentication system",
  "description": "Set up JWT-based authentication",
  "projectId": "project-id",
  "assigneeId": "user-id",
  "priority": "HIGH",
  "estimatedHours": 16,
  "tags": ["backend", "security"],
  "dueDate": "2024-01-15T23:59:59Z",
  "dependencies": ["task-id-1", "task-id-2"]
}
```

### GET /tasks/:id
Get task details.

### PUT /tasks/:id
Update task information.

### DELETE /tasks/:id
Delete task.

## Meeting Management

### GET /meetings
Get list of meetings.

### POST /meetings
Schedule a new meeting.

**Request Body:**
```json
{
  "title": "Sprint Planning",
  "description": "Plan tasks for upcoming sprint",
  "participants": ["user-id-1", "user-id-2"],
  "startTime": "2024-01-15T14:00:00Z",
  "endTime": "2024-01-15T15:00:00Z",
  "location": "Conference Room A",
  "meetingUrl": "https://zoom.us/j/123456789"
}
```

### GET /meetings/:id
Get meeting details.

### PUT /meetings/:id
Update meeting information.

### DELETE /meetings/:id
Cancel meeting.

## Analytics

### GET /analytics/team/:id
Get team analytics and metrics.

### GET /analytics/productivity
Get productivity metrics.

### GET /analytics/capacity
Get capacity analysis.

## Health Check

### GET /health
Basic health check endpoint.

### GET /health/detailed
Detailed health check including database and Redis status.

## Error Responses

All endpoints return errors in the following format:

```json
{
  "success": false,
  "error": "Error message",
  "details": [
    {
      "field": "email",
      "message": "Please provide a valid email"
    }
  ]
}
```

## HTTP Status Codes

- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `409` - Conflict
- `422` - Validation Error
- `500` - Internal Server Error

## Rate Limiting

API requests are limited to 100 requests per 15-minute window per IP address.

## Pagination

List endpoints support pagination with the following parameters:

- `page` - Page number (default: 1)
- `limit` - Items per page (default: 10, max: 100)

Paginated responses include metadata:

```json
{
  "success": true,
  "data": [...],
  "meta": {
    "page": 1,
    "limit": 10,
    "total": 50,
    "totalPages": 5,
    "hasNextPage": true,
    "hasPrevPage": false
  }
}
```

## WebSocket Events

CollabFlow supports real-time updates via WebSocket connections:

### Connection
```javascript
const socket = io('ws://localhost:3001', {
  auth: {
    token: 'your-jwt-token'
  }
});
```

### Events
- `task_updated` - Task status or details changed
- `meeting_scheduled` - New meeting scheduled
- `team_notification` - Team-wide notification
- `user_status_changed` - User availability status changed
- `project_updated` - Project details changed
