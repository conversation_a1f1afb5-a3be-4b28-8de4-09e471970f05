import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import compression from 'compression';
import rateLimit from 'express-rate-limit';
import { createServer } from 'http';
import { Server as SocketIOServer } from 'socket.io';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Import configurations
import { logger, stream } from './config/logger';
import { database } from './config/database';
import { redisClient } from './config/redis';

// Import middleware
import { errorHandler } from './middleware/errorHandler';
import { notFound } from './middleware/notFound';
import { authMiddleware } from './middleware/auth';

// Import routes
import authRoutes from './routes/auth';
import userRoutes from './routes/users';
import teamRoutes from './routes/teams';
import projectRoutes from './routes/projects';
import taskRoutes from './routes/tasks';
import meetingRoutes from './routes/meetings';
import analyticsRoutes from './routes/analytics';
import aiRoutes from './routes/ai';
import notificationsRoutes from './routes/notifications';
import healthRoutes from './routes/health';

// Import socket handlers
import { websocketService } from './services/websocketService';
import { aiService } from './services/aiService';

class App {
  public app: express.Application;
  public server: any;
  public io: SocketIOServer;

  constructor() {
    this.app = express();
    this.server = createServer(this.app);
    this.io = new SocketIOServer(this.server, {
      cors: {
        origin: process.env.CORS_ORIGIN || "http://localhost:3000",
        methods: ["GET", "POST"]
      }
    });

    this.initializeMiddleware();
    this.initializeRoutes();
    this.initializeErrorHandling();
    this.initializeSocket();
  }

  private initializeMiddleware(): void {
    // Security middleware
    this.app.use(helmet());
    
    // CORS configuration
    this.app.use(cors({
      origin: process.env.CORS_ORIGIN || "http://localhost:3000",
      credentials: true
    }));

    // Compression middleware
    this.app.use(compression());

    // Rate limiting
    const limiter = rateLimit({
      windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000'), // 15 minutes
      max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100'), // limit each IP to 100 requests per windowMs
      message: 'Too many requests from this IP, please try again later.',
      standardHeaders: true,
      legacyHeaders: false,
    });
    this.app.use('/api/', limiter);

    // HTTP request logger
    this.app.use(morgan('combined', { stream }));

    // Body parsing middleware
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));

    // Static files
    this.app.use('/uploads', express.static('uploads'));
  }

  private initializeRoutes(): void {
    // Health check route (no auth required)
    this.app.use('/api/health', healthRoutes);

    // Authentication routes (no auth required)
    this.app.use('/api/auth', authRoutes);

    // Protected routes (auth required)
    this.app.use('/api/users', authMiddleware, userRoutes);
    this.app.use('/api/teams', authMiddleware, teamRoutes);
    this.app.use('/api/projects', authMiddleware, projectRoutes);
    this.app.use('/api/tasks', authMiddleware, taskRoutes);
    this.app.use('/api/meetings', authMiddleware, meetingRoutes);
    this.app.use('/api/analytics', authMiddleware, analyticsRoutes);
    this.app.use('/api/ai', authMiddleware, aiRoutes);
    this.app.use('/api/notifications', authMiddleware, notificationsRoutes);

    // API documentation route
    this.app.get('/api', (req, res) => {
      res.json({
        message: 'CollabFlow API',
        version: '1.0.0',
        documentation: '/api/docs',
        endpoints: {
          auth: '/api/auth',
          users: '/api/users',
          teams: '/api/teams',
          projects: '/api/projects',
          tasks: '/api/tasks',
          meetings: '/api/meetings',
          analytics: '/api/analytics',
          ai: '/api/ai',
          notifications: '/api/notifications',
          health: '/api/health'
        }
      });
    });
  }

  private initializeErrorHandling(): void {
    // 404 handler
    this.app.use(notFound);

    // Global error handler
    this.app.use(errorHandler);
  }

  private initializeSocket(): void {
    websocketService.initialize(this.server);
  }

  public async start(): Promise<void> {
    const port = process.env.PORT || 3001;
    const host = process.env.HOST || 'localhost';

    try {
      // Connect to database
      await database.connect();
      
      // Connect to Redis
      await redisClient.connect();

      // Initialize AI service
      await aiService.initialize();

      // Start server
      this.server.listen(port, () => {
        logger.info(`🚀 CollabFlow API server running on http://${host}:${port}`);
        logger.info(`📊 Environment: ${process.env.NODE_ENV || 'development'}`);
        logger.info(`🔌 Socket.IO server initialized`);
      });

    } catch (error) {
      logger.error('Failed to start server:', error);
      process.exit(1);
    }
  }

  public async stop(): Promise<void> {
    try {
      // Close server
      this.server.close();
      
      // Disconnect from database
      await database.disconnect();
      
      // Disconnect from Redis
      await redisClient.disconnect();

      logger.info('Server stopped gracefully');
    } catch (error) {
      logger.error('Error stopping server:', error);
    }
  }
}

// Create and start the application
const app = new App();

// Graceful shutdown
process.on('SIGTERM', async () => {
  logger.info('SIGTERM received, shutting down gracefully');
  await app.stop();
  process.exit(0);
});

process.on('SIGINT', async () => {
  logger.info('SIGINT received, shutting down gracefully');
  await app.stop();
  process.exit(0);
});

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  logger.error('Uncaught Exception:', error);
  process.exit(1);
});

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// Start the server
if (require.main === module) {
  app.start();
}

export default app;
