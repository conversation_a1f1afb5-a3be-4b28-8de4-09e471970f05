import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import compression from 'compression';
import rateLimit from 'express-rate-limit';
import { createServer } from 'http';
import { Server as SocketIOServer } from 'socket.io';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Initialize monitoring first (before other imports)
import { monitoring } from './config/monitoring';
monitoring.initialize();

// Import configurations
import { logger, stream } from './config/logger';
import { database } from './config/database';
import { redisClient } from './config/redis';

// Import middleware
import { errorHandler } from './middleware/errorHandler';
import { notFound } from './middleware/notFound';
import { authMiddleware } from './middleware/auth';
import {
  generalRateLimit,
  authRateLimit,
  aiRateLimit,
  sanitizeInput,
  validateRequest,
  requestLogger,
  securityHeaders,
  limitRequestSize
} from './middleware/security';

// Import routes
import authRoutes from './routes/auth';
import userRoutes from './routes/users';
import teamRoutes from './routes/teams';
import projectRoutes from './routes/projects';
import taskRoutes from './routes/tasks';
import meetingRoutes from './routes/meetings';
import analyticsRoutes from './routes/analytics';
import aiRoutes from './routes/ai';
import notificationsRoutes from './routes/notifications';
import healthRoutes from './routes/health';

// Import socket handlers
import { websocketService } from './services/websocketService';
import { aiService } from './services/aiService';
import { setupSwagger } from './config/swagger';

class App {
  public app: express.Application;
  public server: any;
  public io: SocketIOServer;

  constructor() {
    this.app = express();
    this.server = createServer(this.app);
    this.io = new SocketIOServer(this.server, {
      cors: {
        origin: process.env.CORS_ORIGIN || "http://localhost:3000",
        methods: ["GET", "POST"]
      }
    });

    this.initializeMiddleware();
    this.initializeRoutes();
    this.initializeSwagger();
    this.initializeErrorHandling();
    this.initializeSocket();
  }

  private initializeMiddleware(): void {
    // Security headers (first)
    this.app.use(securityHeaders);

    // Request logging and monitoring
    this.app.use(requestLogger);

    // Enhanced security middleware
    this.app.use(helmet({
      contentSecurityPolicy: false, // We set our own CSP
      crossOriginEmbedderPolicy: false,
    }));

    // Enhanced CORS configuration
    this.app.use(cors({
      origin: process.env.CORS_ORIGIN || "http://localhost:3000",
      credentials: true,
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization', 'X-Request-ID'],
    }));

    // Request size limiting
    this.app.use(limitRequestSize(10 * 1024 * 1024)); // 10MB

    // Compression middleware
    this.app.use(compression());

    // General rate limiting (replaced the old one)
    this.app.use('/api/', generalRateLimit);

    // HTTP request logger
    this.app.use(morgan('combined', { stream }));

    // Body parsing middleware with security
    this.app.use(express.json({
      limit: '10mb',
      verify: (req, res, buf) => {
        // Store raw body for webhook verification if needed
        (req as any).rawBody = buf;
      }
    }));
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));

    // Input sanitization and validation
    this.app.use(sanitizeInput);
    this.app.use(validateRequest);

    // Static files
    this.app.use('/uploads', express.static('uploads'));
  }

  private initializeRoutes(): void {
    // Health check route (no auth required)
    this.app.use('/api/health', healthRoutes);

    // Authentication routes (with specific rate limiting)
    this.app.use('/api/auth', authRateLimit, authRoutes);

    // Protected routes (auth required)
    this.app.use('/api/users', authMiddleware, userRoutes);
    this.app.use('/api/teams', authMiddleware, teamRoutes);
    this.app.use('/api/projects', authMiddleware, projectRoutes);
    this.app.use('/api/tasks', authMiddleware, taskRoutes);
    this.app.use('/api/meetings', authMiddleware, meetingRoutes);
    this.app.use('/api/analytics', authMiddleware, analyticsRoutes);
    this.app.use('/api/ai', authMiddleware, aiRateLimit, aiRoutes);
    this.app.use('/api/notifications', authMiddleware, notificationsRoutes);

    // API documentation route
    this.app.get('/api', (req, res) => {
      res.json({
        message: 'CollabFlow API',
        version: '1.0.0',
        documentation: '/api/docs',
        endpoints: {
          auth: '/api/auth',
          users: '/api/users',
          teams: '/api/teams',
          projects: '/api/projects',
          tasks: '/api/tasks',
          meetings: '/api/meetings',
          analytics: '/api/analytics',
          ai: '/api/ai',
          notifications: '/api/notifications',
          health: '/api/health'
        }
      });
    });
  }

  private initializeSwagger(): void {
    // Only enable Swagger in development and staging
    if (process.env.NODE_ENV !== 'production') {
      setupSwagger(this.app);
      logger.info('📚 Swagger documentation available at /api/docs');
    }
  }

  private initializeErrorHandling(): void {
    // 404 handler
    this.app.use(notFound);

    // Global error handler
    this.app.use(errorHandler);
  }

  private initializeSocket(): void {
    websocketService.initialize(this.server);
  }

  public async start(): Promise<void> {
    const port = process.env.PORT || 3001;
    const host = process.env.HOST || 'localhost';

    try {
      // Connect to database
      await database.connect();
      
      // Connect to Redis
      await redisClient.connect();

      // Initialize AI service
      await aiService.initialize();

      // Start server
      this.server.listen(port, () => {
        logger.info(`🚀 CollabFlow API server running on http://${host}:${port}`);
        logger.info(`📊 Environment: ${process.env.NODE_ENV || 'development'}`);
        logger.info(`🔌 Socket.IO server initialized`);
      });

    } catch (error) {
      logger.error('Failed to start server:', error);
      process.exit(1);
    }
  }

  public async stop(): Promise<void> {
    try {
      // Close server
      this.server.close();
      
      // Disconnect from database
      await database.disconnect();
      
      // Disconnect from Redis
      await redisClient.disconnect();

      logger.info('Server stopped gracefully');
    } catch (error) {
      logger.error('Error stopping server:', error);
    }
  }
}

// Create and start the application
const app = new App();

// Graceful shutdown
process.on('SIGTERM', async () => {
  logger.info('SIGTERM received, shutting down gracefully');
  await app.stop();
  process.exit(0);
});

process.on('SIGINT', async () => {
  logger.info('SIGINT received, shutting down gracefully');
  await app.stop();
  process.exit(0);
});

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  logger.error('Uncaught Exception:', error);
  process.exit(1);
});

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// Start the server
if (require.main === module) {
  app.start();
}

export default app;
