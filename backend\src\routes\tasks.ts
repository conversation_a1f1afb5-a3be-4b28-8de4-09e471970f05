import { Router, Request, Response } from 'express';
import { validationResult } from 'express-validator';
import { asyncHand<PERSON>, CustomError } from '../middleware/errorHandler';
import { AuthenticatedRequest, requireTeamMembership } from '../middleware/auth';
import { prisma } from '../config/database';
import { aiService } from '../services/aiService';
import { websocketService } from '../services/websocketService';
import { notificationService } from '../services/notificationService';
import {
  commonValidations,
  taskValidations,
  paginationValidation
} from '../utils/validation';
import {
  createSuccessResponse,
  getPaginationParams,
  createPaginationMeta
} from '../utils/helpers';

const router = Router();

// Helper function to handle validation errors
const handleValidationErrors = (req: Request, res: Response, next: any) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errors.array(),
    });
  }
  next();
};

// Get all tasks
router.get('/',
  paginationValidation,
  handleValidationErrors,
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const { page, limit, sortBy, sortOrder, status, priority, assigneeId, projectId } = req.query;
    const userId = req.user!.id;

    const { skip, take, orderBy } = getPaginationParams({ query: req.query } as any);

    const where: any = {
      project: {
        team: {
          members: {
            some: {
              userId,
            },
          },
        },
      },
    };

    // Apply filters
    if (status) where.status = status;
    if (priority) where.priority = priority;
    if (assigneeId) where.assigneeId = assigneeId;
    if (projectId) where.projectId = projectId;

    const [tasks, total] = await Promise.all([
      prisma.task.findMany({
        where,
        skip,
        take,
        orderBy: orderBy || { createdAt: 'desc' },
        include: {
          project: {
            select: {
              id: true,
              name: true,
              team: {
                select: {
                  id: true,
                  name: true,
                },
              },
            },
          },
          assignee: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              avatar: true,
            },
          },
          dependencies: {
            include: {
              dependency: {
                select: {
                  id: true,
                  title: true,
                  status: true,
                },
              },
            },
          },
          dependents: {
            include: {
              task: {
                select: {
                  id: true,
                  title: true,
                  status: true,
                },
              },
            },
          },
        },
      }),
      prisma.task.count({ where }),
    ]);

    const meta = createPaginationMeta(
      Math.floor(skip / take) + 1,
      take,
      total
    );

    res.json(createSuccessResponse(tasks, 'Tasks retrieved successfully', meta));
  })
);

// Create task with AI-powered priority analysis
router.post('/',
  taskValidations.create,
  handleValidationErrors,
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const {
      title,
      description,
      projectId,
      assigneeId,
      priority,
      status,
      estimatedHours,
      tags,
      dueDate,
      dependencies,
    } = req.body;

    // Verify project access
    const project = await prisma.project.findUnique({
      where: { id: projectId },
      include: {
        team: {
          include: {
            members: {
              where: { userId: req.user!.id },
            },
          },
        },
      },
    });

    if (!project || project.team.members.length === 0) {
      throw new CustomError('Project not found or access denied', 404);
    }

    // Use AI to analyze task priority if not provided
    let finalPriority = priority;
    let aiEstimatedHours = estimatedHours;

    if (!priority || !estimatedHours) {
      try {
        const analysis = await aiService.analyzeTaskPriority(
          title,
          description || '',
          dueDate ? new Date(dueDate) : undefined,
          dependencies,
          `Project: ${project.name}`
        );

        if (!priority) finalPriority = analysis.priority;
        if (!estimatedHours) aiEstimatedHours = analysis.estimatedHours;
      } catch (error) {
        // Fallback to defaults if AI analysis fails
        if (!priority) finalPriority = 'MEDIUM';
        if (!estimatedHours) aiEstimatedHours = 8;
      }
    }

    const task = await prisma.task.create({
      data: {
        title,
        description,
        projectId,
        assigneeId,
        priority: finalPriority,
        status: status || 'TODO',
        estimatedHours: aiEstimatedHours,
        tags: tags || [],
        dueDate: dueDate ? new Date(dueDate) : null,
      },
      include: {
        project: {
          select: {
            id: true,
            name: true,
            team: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
        assignee: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            avatar: true,
          },
        },
      },
    });

    // Create dependencies if provided
    if (dependencies && dependencies.length > 0) {
      await prisma.taskDependency.createMany({
        data: dependencies.map((depId: string) => ({
          taskId: task.id,
          dependencyId: depId,
        })),
      });
    }

    // Emit real-time update to project subscribers
    websocketService.emitToProject(projectId, 'task:created', {
      task,
      createdBy: req.user,
    });

    // Send notification to assignee if different from creator
    if (assigneeId && assigneeId !== req.user!.id) {
      await notificationService.notifyTaskAssigned(task.id, assigneeId, req.user!.id);
    }

    res.status(201).json(createSuccessResponse(task, 'Task created successfully'));
  })
);

// Get task by ID
router.get('/:id',
  commonValidations.id,
  handleValidationErrors,
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const { id } = req.params;
    const userId = req.user!.id;

    const task = await prisma.task.findFirst({
      where: {
        id,
        project: {
          team: {
            members: {
              some: { userId },
            },
          },
        },
      },
      include: {
        project: {
          select: {
            id: true,
            name: true,
            team: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
        assignee: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            avatar: true,
            skills: true,
          },
        },
        dependencies: {
          include: {
            dependency: {
              select: {
                id: true,
                title: true,
                status: true,
                priority: true,
              },
            },
          },
        },
        dependents: {
          include: {
            task: {
              select: {
                id: true,
                title: true,
                status: true,
                priority: true,
              },
            },
          },
        },
      },
    });

    if (!task) {
      throw new CustomError('Task not found', 404);
    }

    res.json(createSuccessResponse(task, 'Task retrieved successfully'));
  })
);

// Update task
router.put('/:id',
  commonValidations.id,
  taskValidations.update,
  handleValidationErrors,
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const { id } = req.params;
    const userId = req.user!.id;
    const updateData = req.body;

    // Verify task access
    const existingTask = await prisma.task.findFirst({
      where: {
        id,
        project: {
          team: {
            members: {
              some: { userId },
            },
          },
        },
      },
    });

    if (!existingTask) {
      throw new CustomError('Task not found', 404);
    }

    const task = await prisma.task.update({
      where: { id },
      data: {
        ...updateData,
        dueDate: updateData.dueDate ? new Date(updateData.dueDate) : undefined,
      },
      include: {
        project: {
          select: {
            id: true,
            name: true,
            team: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
        assignee: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            avatar: true,
          },
        },
      },
    });

    // Emit real-time update
    websocketService.emitToTask(id, 'task:updated', {
      task,
      updates: updateData,
      updatedBy: req.user,
    });

    websocketService.emitToProject(task.project.id, 'task:updated', {
      task,
      updates: updateData,
      updatedBy: req.user,
    });

    // Check if task was completed and notify dependent tasks
    if (updateData.status === 'COMPLETED' && existingTask.status !== 'COMPLETED') {
      await notificationService.notifyDependencyCompleted(id);
    }

    // Notify assignee if task was reassigned
    if (updateData.assigneeId && updateData.assigneeId !== existingTask.assigneeId) {
      await notificationService.notifyTaskAssigned(id, updateData.assigneeId, req.user!.id);
    }

    res.json(createSuccessResponse(task, 'Task updated successfully'));
  })
);

// Delete task
router.delete('/:id',
  commonValidations.id,
  handleValidationErrors,
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const { id } = req.params;
    const userId = req.user!.id;

    // Verify task access and check if user can delete
    const task = await prisma.task.findFirst({
      where: {
        id,
        project: {
          team: {
            members: {
              some: {
                userId,
                role: { in: ['LEAD'] }
              },
            },
          },
        },
      },
    });

    if (!task && req.user!.role !== 'ADMIN') {
      throw new CustomError('Task not found or insufficient permissions', 404);
    }

    // Delete dependencies first
    await prisma.taskDependency.deleteMany({
      where: {
        OR: [
          { taskId: id },
          { dependencyId: id },
        ],
      },
    });

    // Delete the task
    await prisma.task.delete({
      where: { id },
    });

    res.json(createSuccessResponse(null, 'Task deleted successfully'));
  })
);

export default router;
