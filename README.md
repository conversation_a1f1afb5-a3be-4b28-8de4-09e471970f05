# CollabFlow
**Seamless team orchestration**

An AI-powered team coordination platform that solves distributed team coordination challenges through intelligent automation and AI-driven insights. **Now with open-source AI integration using Ollama - zero API costs!**

## 🆓 Open-Source AI Integration

CollabFlow now uses **Ollama** for local AI inference, eliminating OpenAI API costs while providing powerful AI capabilities:

- **Zero API Costs**: Run AI models locally with Ollama
- **Privacy First**: All AI processing happens on your infrastructure
- **Multiple Models**: Support for Llama 2, Mistral, Code Llama, and more
- **Offline Capable**: Works without internet connection
- **Easy Setup**: One-command installation and model management
- **Backward Compatible**: Optional OpenAI integration still available

> **Cost Savings**: Teams using AI features heavily can save $100-1000+ per month by switching from OpenAI to Ollama.

## 🎯 Problem Statement

CollabFlow addresses critical pain points in modern distributed teams:
- Poor coordination in distributed teams
- Unclear task dependencies and handoffs
- Inefficient meeting scheduling and preparation
- Lack of visibility into team capacity and availability

## ✨ Core Features

### 🤖 Intelligent Team Orchestration
- Automated coordination of handoffs and dependencies
- Smart workflow automation
- Task dependency mapping and visualization

### 📅 Smart Meeting Assistant
- Context-aware meeting scheduling with necessity validation
- Automated agenda generation
- Meeting optimization recommendations

### 📊 Capacity Planning Engine
- Skills and availability-based workload balancing
- Resource allocation optimization
- Team capacity visualization

### 💬 Asynchronous Communication Hub
- Meeting reduction through intelligent async updates
- Threaded discussions and decision tracking
- Automated status updates

### 🔧 Conflict Resolution AI
- Automated scheduling and resource conflict detection
- Priority-based resolution suggestions
- Alternative scheduling recommendations

### 📈 Performance Analytics
- Team productivity tracking with optimization recommendations
- Bottleneck identification and resolution
- Custom reporting and insights

## 🏗️ System Architecture

```mermaid
graph TB
    subgraph "Frontend Layer"
        A[React TypeScript App]
        B[Real-time UI Components]
        C[State Management - Redux]
    end
    
    subgraph "API Gateway"
        D[Express.js Server]
        E[Authentication Middleware]
        F[Rate Limiting]
    end
    
    subgraph "Core Services"
        G[Team Management Service]
        H[Task Orchestration Service]
        I[Meeting Assistant Service]
        J[Capacity Planning Service]
        K[Analytics Service]
    end
    
    subgraph "AI/ML Layer"
        L[Scheduling Algorithm]
        M[Conflict Resolution AI]
        N[Capacity Optimization]
        O[Meeting Necessity Scorer]
    end
    
    subgraph "Data Layer"
        P[PostgreSQL Database]
        Q[Redis Cache]
        R[File Storage]
    end
    
    subgraph "External Integrations"
        S[Calendar APIs]
        T[Video Conferencing]
        U[Notification Services]
    end
    
    A --> D
    B --> D
    C --> D
    D --> E
    E --> F
    F --> G
    F --> H
    F --> I
    F --> J
    F --> K
    G --> L
    H --> M
    I --> N
    J --> O
    L --> P
    M --> P
    N --> Q
    O --> Q
    P --> R
    G --> S
    I --> T
    K --> U
```

## 🔄 Workflow Diagrams

### Task Orchestration Workflow
```mermaid
flowchart TD
    A[Task Created] --> B{Dependencies Exist?}
    B -->|Yes| C[Map Dependencies]
    B -->|No| D[Assign to Team Member]
    C --> E[Check Prerequisite Status]
    E --> F{Prerequisites Complete?}
    F -->|Yes| D
    F -->|No| G[Queue Task]
    D --> H[Notify Assignee]
    H --> I[Track Progress]
    I --> J{Task Complete?}
    J -->|No| K[Send Reminders]
    J -->|Yes| L[Trigger Dependent Tasks]
    K --> I
    L --> M[Update Analytics]
```

### Smart Meeting Workflow
```mermaid
flowchart TD
    A[Meeting Request] --> B[Analyze Necessity Score]
    B --> C{Score > Threshold?}
    C -->|No| D[Suggest Async Alternative]
    C -->|Yes| E[Check Participant Availability]
    E --> F[Detect Conflicts]
    F --> G{Conflicts Found?}
    G -->|Yes| H[Suggest Alternative Times]
    G -->|No| I[Schedule Meeting]
    H --> J[Apply Resolution Algorithm]
    J --> I
    I --> K[Generate Agenda]
    K --> L[Send Notifications]
    D --> M[Create Async Thread]
```

## � Quick Start

### Prerequisites
- Node.js 18+ and npm
- PostgreSQL 13+
- Redis 6+
- Docker (optional)

### Option 1: Docker Setup (Recommended)
```bash
# Clone the repository
git clone https://github.com/your-username/collabflow.git
cd collabflow

# Start all services with Docker Compose (includes Ollama)
docker-compose up -d

# Wait for Ollama to start, then pull the AI model
docker exec collabflow-ollama ollama pull llama2:7b

# Your app is now running at:
# - Frontend: http://localhost:3000
# - Backend API: http://localhost:3001
# - API Documentation: http://localhost:3001/api/docs
```

### Option 2: Manual Setup

#### 1. Setup Ollama (AI Provider)
```bash
# Install Ollama (see docs/OLLAMA_SETUP.md for detailed instructions)
curl -fsSL https://ollama.ai/install.sh | sh

# Pull the recommended model
ollama pull llama2:7b

# Verify installation
curl http://localhost:11434/api/tags
```

#### 2. Setup Backend
```bash
cd backend

# Install dependencies
npm install

# Setup environment
cp .env.example .env
# Edit .env with your database and Redis URLs

# Run database migrations
npm run db:migrate

# Seed the database (optional)
npm run db:seed

# Start development server
npm run dev
```

#### 3. Setup Frontend
```bash
cd frontend

# Install dependencies
npm install

# Start development server
npm start
```

### 🔧 Configuration

Key environment variables in `backend/.env`:

```env
# AI Provider (Ollama is default, free, and runs locally)
AI_PROVIDER=ollama
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_MODEL=llama2:7b

# Optional: OpenAI (if you prefer to use it)
# OPENAI_API_KEY=your-openai-key
# AI_PROVIDER=openai

# Database
DATABASE_URL=postgresql://user:password@localhost:5432/collabflow

# Redis
REDIS_URL=redis://localhost:6379

# Security
JWT_SECRET=your-super-secret-jwt-key
```

### 📚 API Documentation

Once running, visit `http://localhost:3001/api/docs` for interactive API documentation.

## �📁 Project Structure

```
CollabFlow/
├── backend/                    # Node.js Express API
│   ├── src/
│   │   ├── controllers/        # Route controllers
│   │   ├── models/            # Database models
│   │   ├── routes/            # API routes
│   │   ├── middleware/        # Custom middleware
│   │   ├── services/          # Business logic
│   │   ├── utils/             # Utility functions
│   │   ├── config/            # Configuration files
│   │   └── app.ts             # Express app setup
│   ├── tests/                 # Backend tests
│   ├── package.json
│   └── tsconfig.json
├── frontend/                   # React TypeScript app
│   ├── src/
│   │   ├── components/        # Reusable components
│   │   ├── pages/             # Page components
│   │   ├── hooks/             # Custom React hooks
│   │   ├── services/          # API services
│   │   ├── store/             # Redux store
│   │   ├── utils/             # Utility functions
│   │   ├── types/             # TypeScript types
│   │   └── App.tsx            # Main app component
│   ├── public/                # Static assets
│   ├── package.json
│   └── tsconfig.json
├── shared/                     # Shared types and utilities
│   └── types/                 # Shared TypeScript types
├── docs/                      # Documentation
├── docker/                    # Docker configurations
├── scripts/                   # Build and deployment scripts
├── .github/                   # GitHub workflows
│   └── workflows/
├── README.md
├── docker-compose.yml
├── .gitignore
└── package.json               # Root package.json

## 🚀 Installation & Setup

### Prerequisites
- Node.js 18+ and npm/yarn
- PostgreSQL 14+
- Redis 6+
- Docker (optional)

### Quick Start

1. **Clone the repository**
   ```bash
   git clone https://github.com/HectorTa1989/CollabFlow.git
   cd CollabFlow
   ```

2. **Install dependencies**
   ```bash
   npm install
   cd backend && npm install
   cd ../frontend && npm install
   ```

3. **Environment setup**
   ```bash
   cp backend/.env.example backend/.env
   cp frontend/.env.example frontend/.env
   # Edit .env files with your configuration
   ```

4. **Database setup**
   ```bash
   cd backend
   npm run db:migrate
   npm run db:seed
   ```

5. **Start development servers**
   ```bash
   # Terminal 1 - Backend
   cd backend && npm run dev

   # Terminal 2 - Frontend
   cd frontend && npm start
   ```

### Docker Setup (Alternative)
```bash
docker-compose up -d
```

## 🔧 Configuration

### Environment Variables

#### Backend (.env)
```env
NODE_ENV=development
PORT=3001
DATABASE_URL=postgresql://user:password@localhost:5432/collabflow
REDIS_URL=redis://localhost:6379
JWT_SECRET=your-jwt-secret
OPENAI_API_KEY=your-openai-key
```

#### Frontend (.env)
```env
REACT_APP_API_URL=http://localhost:3001/api
REACT_APP_WS_URL=ws://localhost:3001
```

## 📚 API Documentation

### Authentication Endpoints
- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login
- `POST /api/auth/refresh` - Refresh token
- `POST /api/auth/logout` - User logout

### Team Management
- `GET /api/teams` - List teams
- `POST /api/teams` - Create team
- `GET /api/teams/:id` - Get team details
- `PUT /api/teams/:id` - Update team
- `DELETE /api/teams/:id` - Delete team

### Task Orchestration
- `GET /api/tasks` - List tasks
- `POST /api/tasks` - Create task
- `GET /api/tasks/:id` - Get task details
- `PUT /api/tasks/:id` - Update task
- `DELETE /api/tasks/:id` - Delete task

### Meeting Management
- `GET /api/meetings` - List meetings
- `POST /api/meetings` - Schedule meeting
- `GET /api/meetings/:id` - Get meeting details
- `PUT /api/meetings/:id` - Update meeting
- `DELETE /api/meetings/:id` - Cancel meeting

### Analytics
- `GET /api/analytics/team/:id` - Team analytics
- `GET /api/analytics/productivity` - Productivity metrics
- `GET /api/analytics/capacity` - Capacity analysis

## 🧪 Testing

### Backend Tests
```bash
cd backend
npm test                # Run all tests
npm run test:watch     # Watch mode
npm run test:coverage  # Coverage report
```

### Frontend Tests
```bash
cd frontend
npm test               # Run all tests
npm run test:coverage  # Coverage report
```

### E2E Tests
```bash
npm run test:e2e       # Run end-to-end tests
```

## 🚀 Deployment

### Production Build
```bash
npm run build          # Build both frontend and backend
```

### Docker Deployment
```bash
docker-compose -f docker-compose.prod.yml up -d
```

### Environment-specific Deployments
- **Development**: `npm run deploy:dev`
- **Staging**: `npm run deploy:staging`
- **Production**: `npm run deploy:prod`

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Guidelines
- Follow TypeScript best practices
- Write comprehensive tests
- Update documentation
- Follow conventional commit messages

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- OpenAI for AI capabilities
- The open-source community for amazing tools
- Contributors and early adopters

## 📞 Support

- 📧 Email: <EMAIL>
- 💬 Discord: [CollabFlow Community](https://discord.gg/collabflow)
- 📖 Documentation: [docs.collabflow.dev](https://docs.collabflow.dev)
- 🐛 Issues: [GitHub Issues](https://github.com/HectorTa1989/CollabFlow/issues)

---

**Built with ❤️ for distributed teams worldwide**
```
