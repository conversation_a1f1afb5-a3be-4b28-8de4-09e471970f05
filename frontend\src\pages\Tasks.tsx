import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Grid,
  Card,
  CardContent,
  CardActions,
  Button,
  Chip,
  Avatar,
  IconButton,
  Menu,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Select,
  FormControl,
  InputLabel,
  Autocomplete,
  DatePicker,
  Fab,
  Tabs,
  Tab,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  ListItemSecondaryAction,
  CircularProgress,
  Alert,
  LinearProgress,
  Tooltip,
} from '@mui/material';
import {
  Add as AddIcon,
  MoreVert as MoreVertIcon,
  Assignment as TaskIcon,
  Person as PersonIcon,
  Schedule as ScheduleIcon,
  Flag as FlagIcon,
  CheckCircle as CheckCircleIcon,
  PlayArrow as PlayArrowIcon,
  Pause as PauseIcon,
} from '@mui/icons-material';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { taskService, Task, CreateTaskData, UpdateTaskData } from '../services/taskService';
import { teamService } from '../services/teamService';
import { aiService } from '../services/aiService';
import { useAppSelector } from '../store';
import { useTaskSubscription } from '../hooks/useWebSocket';
import toast from 'react-hot-toast';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`tasks-tabpanel-${index}`}
      aria-labelledby={`tasks-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

// Validation schema
const createTaskSchema = yup.object({
  title: yup
    .string()
    .required('Task title is required')
    .min(3, 'Title must be at least 3 characters'),
  description: yup.string().max(1000, 'Description must be less than 1000 characters'),
  projectId: yup.string().required('Project is required'),
  assigneeId: yup.string(),
  priority: yup.string().oneOf(['LOW', 'MEDIUM', 'HIGH', 'CRITICAL']),
  estimatedHours: yup.number().min(0.5, 'Minimum 0.5 hours').max(200, 'Maximum 200 hours'),
  dueDate: yup.date().nullable(),
});

const Tasks: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAppSelector((state) => state.auth);

  const [tasks, setTasks] = useState<Task[]>([]);
  const [loading, setLoading] = useState(true);
  const [tabValue, setTabValue] = useState(0);
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [menuAnchor, setMenuAnchor] = useState<null | HTMLElement>(null);
  const [selectedTask, setSelectedTask] = useState<Task | null>(null);
  const [teams, setTeams] = useState<any[]>([]);
  const [projects, setProjects] = useState<any[]>([]);
  const [aiAnalysisLoading, setAiAnalysisLoading] = useState(false);

  const {
    control,
    handleSubmit,
    reset,
    watch,
    setValue,
    formState: { errors, isSubmitting },
  } = useForm<CreateTaskData>({
    resolver: yupResolver(createTaskSchema),
    defaultValues: {
      title: '',
      description: '',
      projectId: '',
      assigneeId: '',
      priority: 'MEDIUM',
      estimatedHours: 8,
      tags: [],
      dueDate: undefined,
    },
  });

  const watchedProjectId = watch('projectId');

  useEffect(() => {
    loadTasks();
    loadTeams();
  }, []);

  useEffect(() => {
    if (watchedProjectId) {
      loadProjectMembers(watchedProjectId);
    }
  }, [watchedProjectId]);

  const loadTasks = async () => {
    try {
      setLoading(true);
      const response = await taskService.getTasks({
        sortBy: 'createdAt',
        sortOrder: 'desc',
      });
      setTasks(response.data || []);
    } catch (error) {
      toast.error('Failed to load tasks');
      console.error('Error loading tasks:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadTeams = async () => {
    try {
      const response = await teamService.getTeams();
      setTeams(response.data || []);
    } catch (error) {
      console.error('Error loading teams:', error);
    }
  };

  const loadProjectMembers = async (projectId: string) => {
    // This would load project members for assignee selection
    // For now, we'll use team members
    try {
      const team = teams.find(t => t.projects?.some((p: any) => p.id === projectId));
      if (team) {
        setProjects(team.members || []);
      }
    } catch (error) {
      console.error('Error loading project members:', error);
    }
  };

  const handleCreateTask = async (data: CreateTaskData) => {
    try {
      setAiAnalysisLoading(true);

      // Get AI analysis for task priority and estimation
      let aiAnalysis = null;
      try {
        const analysisResponse = await aiService.analyzeTaskPriority({
          title: data.title,
          description: data.description,
          dueDate: data.dueDate,
          projectContext: projects.find(p => p.id === data.projectId)?.name,
        });
        aiAnalysis = analysisResponse.data.analysis;
      } catch (aiError) {
        console.warn('AI analysis failed, using default values');
      }

      // Use AI suggestions if available
      const taskData = {
        ...data,
        priority: data.priority || aiAnalysis?.priority || 'MEDIUM',
        estimatedHours: data.estimatedHours || aiAnalysis?.estimatedHours || 8,
      };

      const response = await taskService.createTask(taskData);
      setTasks([response.data, ...tasks]);
      setCreateDialogOpen(false);
      reset();

      if (aiAnalysis) {
        toast.success(`Task created with AI-suggested priority: ${aiAnalysis.priority}`);
      } else {
        toast.success('Task created successfully!');
      }
    } catch (error: any) {
      toast.error(error.response?.data?.error || 'Failed to create task');
    } finally {
      setAiAnalysisLoading(false);
    }
  };

  const handleTaskStatusChange = async (taskId: string, newStatus: Task['status']) => {
    try {
      await taskService.updateTaskStatus(taskId, newStatus);
      setTasks(tasks.map(task =>
        task.id === taskId ? { ...task, status: newStatus } : task
      ));
      toast.success(`Task status updated to ${newStatus}`);
    } catch (error: any) {
      toast.error(error.response?.data?.error || 'Failed to update task status');
    }
  };

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>, task: Task) => {
    setMenuAnchor(event.currentTarget);
    setSelectedTask(task);
  };

  const handleMenuClose = () => {
    setMenuAnchor(null);
    setSelectedTask(null);
  };

  const handleTaskClick = (taskId: string) => {
    navigate(`/tasks/${taskId}`);
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'CRITICAL': return 'error';
      case 'HIGH': return 'warning';
      case 'MEDIUM': return 'info';
      case 'LOW': return 'success';
      default: return 'default';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'COMPLETED': return 'success';
      case 'IN_PROGRESS': return 'primary';
      case 'REVIEW': return 'warning';
      case 'TODO': return 'default';
      case 'CANCELLED': return 'error';
      default: return 'default';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'COMPLETED': return <CheckCircleIcon />;
      case 'IN_PROGRESS': return <PlayArrowIcon />;
      case 'REVIEW': return <PauseIcon />;
      default: return <TaskIcon />;
    }
  };

  const filterTasksByStatus = (status?: string) => {
    if (!status) return tasks;
    return tasks.filter(task => task.status === status);
  };

  const getTabTasks = () => {
    switch (tabValue) {
      case 0: return tasks; // All tasks
      case 1: return filterTasksByStatus('TODO');
      case 2: return filterTasksByStatus('IN_PROGRESS');
      case 3: return filterTasksByStatus('REVIEW');
      case 4: return filterTasksByStatus('COMPLETED');
      default: return tasks;
    }
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <Box>
        {/* Header */}
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
          <Box>
            <Typography variant="h4" component="h1" gutterBottom>
              Tasks
            </Typography>
            <Typography variant="body1" color="text.secondary">
              Manage and track your tasks efficiently
            </Typography>
          </Box>
        </Box>

        {/* Task Stats */}
        <Grid container spacing={2} sx={{ mb: 3 }}>
          <Grid item xs={6} sm={3}>
            <Card>
              <CardContent sx={{ textAlign: 'center', py: 2 }}>
                <Typography variant="h4" color="primary">
                  {tasks.length}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Total Tasks
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={6} sm={3}>
            <Card>
              <CardContent sx={{ textAlign: 'center', py: 2 }}>
                <Typography variant="h4" color="warning.main">
                  {filterTasksByStatus('IN_PROGRESS').length}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  In Progress
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={6} sm={3}>
            <Card>
              <CardContent sx={{ textAlign: 'center', py: 2 }}>
                <Typography variant="h4" color="info.main">
                  {filterTasksByStatus('REVIEW').length}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  In Review
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={6} sm={3}>
            <Card>
              <CardContent sx={{ textAlign: 'center', py: 2 }}>
                <Typography variant="h4" color="success.main">
                  {filterTasksByStatus('COMPLETED').length}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Completed
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Tabs */}
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs
            value={tabValue}
            onChange={(e, newValue) => setTabValue(newValue)}
            aria-label="task tabs"
          >
            <Tab label={`All (${tasks.length})`} />
            <Tab label={`To Do (${filterTasksByStatus('TODO').length})`} />
            <Tab label={`In Progress (${filterTasksByStatus('IN_PROGRESS').length})`} />
            <Tab label={`Review (${filterTasksByStatus('REVIEW').length})`} />
            <Tab label={`Completed (${filterTasksByStatus('COMPLETED').length})`} />
          </Tabs>
        </Box>

        {/* Task List */}
        <TabPanel value={tabValue} index={tabValue}>
          {getTabTasks().length === 0 ? (
            <Alert severity="info" sx={{ mt: 2 }}>
              No tasks found in this category.
            </Alert>
          ) : (
            <Grid container spacing={2}>
              {getTabTasks().map((task) => (
                <Grid item xs={12} sm={6} md={4} key={task.id}>
                  <Card
                    sx={{
                      height: '100%',
                      display: 'flex',
                      flexDirection: 'column',
                      cursor: 'pointer',
                      '&:hover': {
                        boxShadow: (theme) => theme.shadows[8],
                      },
                    }}
                    onClick={() => handleTaskClick(task.id)}
                  >
                    <CardContent sx={{ flexGrow: 1 }}>
                      <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={2}>
                        <Typography variant="h6" component="h3" noWrap>
                          {task.title}
                        </Typography>
                        <IconButton
                          size="small"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleMenuOpen(e, task);
                          }}
                        >
                          <MoreVertIcon />
                        </IconButton>
                      </Box>

                      {task.description && (
                        <Typography
                          variant="body2"
                          color="text.secondary"
                          sx={{
                            mb: 2,
                            display: '-webkit-box',
                            WebkitLineClamp: 2,
                            WebkitBoxOrient: 'vertical',
                            overflow: 'hidden',
                          }}
                        >
                          {task.description}
                        </Typography>
                      )}

                      <Box display="flex" flexWrap="wrap" gap={1} mb={2}>
                        <Chip
                          icon={getStatusIcon(task.status)}
                          label={task.status.replace('_', ' ')}
                          size="small"
                          color={getStatusColor(task.status) as any}
                        />
                        <Chip
                          icon={<FlagIcon />}
                          label={task.priority}
                          size="small"
                          color={getPriorityColor(task.priority) as any}
                          variant="outlined"
                        />
                      </Box>

                      <Box display="flex" alignItems="center" justifyContent="space-between">
                        {task.assignee && (
                          <Box display="flex" alignItems="center" gap={1}>
                            <Avatar
                              src={task.assignee.avatar}
                              sx={{ width: 24, height: 24 }}
                            >
                              {task.assignee.firstName[0]}{task.assignee.lastName[0]}
                            </Avatar>
                            <Typography variant="body2" color="text.secondary">
                              {task.assignee.firstName} {task.assignee.lastName}
                            </Typography>
                          </Box>
                        )}

                        {task.dueDate && (
                          <Tooltip title="Due Date">
                            <Chip
                              icon={<ScheduleIcon />}
                              label={new Date(task.dueDate).toLocaleDateString()}
                              size="small"
                              variant="outlined"
                            />
                          </Tooltip>
                        )}
                      </Box>

                      {task.estimatedHours && (
                        <Box mt={2}>
                          <Typography variant="body2" color="text.secondary" gutterBottom>
                            Progress: {task.actualHours || 0}h / {task.estimatedHours}h
                          </Typography>
                          <LinearProgress
                            variant="determinate"
                            value={Math.min(((task.actualHours || 0) / task.estimatedHours) * 100, 100)}
                            sx={{ height: 6, borderRadius: 3 }}
                          />
                        </Box>
                      )}
                    </CardContent>

                    <CardActions>
                      <Button
                        size="small"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleTaskClick(task.id);
                        }}
                      >
                        View Details
                      </Button>
                      {task.status !== 'COMPLETED' && (
                        <Button
                          size="small"
                          onClick={(e) => {
                            e.stopPropagation();
                            const nextStatus = task.status === 'TODO' ? 'IN_PROGRESS' :
                                             task.status === 'IN_PROGRESS' ? 'REVIEW' : 'COMPLETED';
                            handleTaskStatusChange(task.id, nextStatus);
                          }}
                        >
                          {task.status === 'TODO' ? 'Start' :
                           task.status === 'IN_PROGRESS' ? 'Review' : 'Complete'}
                        </Button>
                      )}
                    </CardActions>
                  </Card>
                </Grid>
              ))}
            </Grid>
          )}
        </TabPanel>

        {/* Floating Action Button */}
        <Fab
          color="primary"
          aria-label="create task"
          sx={{
            position: 'fixed',
            bottom: 16,
            right: 16,
          }}
          onClick={() => setCreateDialogOpen(true)}
        >
          <AddIcon />
        </Fab>

        {/* Create Task Dialog */}
        <Dialog
          open={createDialogOpen}
          onClose={() => setCreateDialogOpen(false)}
          maxWidth="md"
          fullWidth
        >
          <form onSubmit={handleSubmit(handleCreateTask)}>
            <DialogTitle>
              Create New Task
              {aiAnalysisLoading && (
                <LinearProgress sx={{ mt: 1 }} />
              )}
            </DialogTitle>
            <DialogContent>
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <Controller
                    name="title"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        autoFocus
                        label="Task Title"
                        fullWidth
                        variant="outlined"
                        error={!!errors.title}
                        helperText={errors.title?.message}
                      />
                    )}
                  />
                </Grid>

                <Grid item xs={12}>
                  <Controller
                    name="description"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        label="Description"
                        fullWidth
                        multiline
                        rows={3}
                        variant="outlined"
                        error={!!errors.description}
                        helperText={errors.description?.message}
                      />
                    )}
                  />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <Controller
                    name="projectId"
                    control={control}
                    render={({ field }) => (
                      <FormControl fullWidth variant="outlined" error={!!errors.projectId}>
                        <InputLabel>Project</InputLabel>
                        <Select {...field} label="Project">
                          {teams.flatMap(team =>
                            team.projects?.map((project: any) => (
                              <MenuItem key={project.id} value={project.id}>
                                {project.name} ({team.name})
                              </MenuItem>
                            )) || []
                          )}
                        </Select>
                      </FormControl>
                    )}
                  />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <Controller
                    name="assigneeId"
                    control={control}
                    render={({ field }) => (
                      <FormControl fullWidth variant="outlined">
                        <InputLabel>Assignee</InputLabel>
                        <Select {...field} label="Assignee">
                          <MenuItem value="">Unassigned</MenuItem>
                          {projects.map((member: any) => (
                            <MenuItem key={member.userId} value={member.userId}>
                              {member.team.firstName} {member.team.lastName}
                            </MenuItem>
                          ))}
                        </Select>
                      </FormControl>
                    )}
                  />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <Controller
                    name="priority"
                    control={control}
                    render={({ field }) => (
                      <FormControl fullWidth variant="outlined">
                        <InputLabel>Priority</InputLabel>
                        <Select {...field} label="Priority">
                          <MenuItem value="LOW">Low</MenuItem>
                          <MenuItem value="MEDIUM">Medium</MenuItem>
                          <MenuItem value="HIGH">High</MenuItem>
                          <MenuItem value="CRITICAL">Critical</MenuItem>
                        </Select>
                      </FormControl>
                    )}
                  />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <Controller
                    name="estimatedHours"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        label="Estimated Hours"
                        type="number"
                        fullWidth
                        variant="outlined"
                        error={!!errors.estimatedHours}
                        helperText={errors.estimatedHours?.message}
                        inputProps={{ min: 0.5, max: 200, step: 0.5 }}
                      />
                    )}
                  />
                </Grid>

                <Grid item xs={12}>
                  <Controller
                    name="dueDate"
                    control={control}
                    render={({ field }) => (
                      <DatePicker
                        {...field}
                        label="Due Date"
                        renderInput={(params) => (
                          <TextField
                            {...params}
                            fullWidth
                            variant="outlined"
                            error={!!errors.dueDate}
                            helperText={errors.dueDate?.message}
                          />
                        )}
                      />
                    )}
                  />
                </Grid>
              </Grid>
            </DialogContent>
            <DialogActions>
              <Button onClick={() => setCreateDialogOpen(false)}>Cancel</Button>
              <Button
                type="submit"
                variant="contained"
                disabled={isSubmitting || aiAnalysisLoading}
              >
                {isSubmitting ? 'Creating...' : 'Create Task'}
              </Button>
            </DialogActions>
          </form>
        </Dialog>

        {/* Task Actions Menu */}
        <Menu
          anchorEl={menuAnchor}
          open={Boolean(menuAnchor)}
          onClose={handleMenuClose}
        >
          <MenuItem onClick={() => {
            if (selectedTask) {
              navigate(`/tasks/${selectedTask.id}`);
            }
            handleMenuClose();
          }}>
            View Details
          </MenuItem>
          <MenuItem onClick={() => {
            if (selectedTask) {
              navigate(`/tasks/${selectedTask.id}/edit`);
            }
            handleMenuClose();
          }}>
            Edit Task
          </MenuItem>
          {selectedTask?.status !== 'COMPLETED' && (
            <MenuItem onClick={() => {
              if (selectedTask) {
                const nextStatus = selectedTask.status === 'TODO' ? 'IN_PROGRESS' :
                                 selectedTask.status === 'IN_PROGRESS' ? 'REVIEW' : 'COMPLETED';
                handleTaskStatusChange(selectedTask.id, nextStatus);
              }
              handleMenuClose();
            }}>
              Mark as {selectedTask?.status === 'TODO' ? 'In Progress' :
                      selectedTask?.status === 'IN_PROGRESS' ? 'Review' : 'Completed'}
            </MenuItem>
          )}
        </Menu>
      </Box>
    </LocalizationProvider>
  );
};

export default Tasks;
