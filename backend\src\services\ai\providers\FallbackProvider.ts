import { logger } from '../../../config/logger';
import {
  AIProvider,
  AIProviderConfig,
  ChatCompletionRequest,
  ChatCompletionResponse,
  MeetingNecessityAnalysis,
  TaskPriorityAnalysis,
  TeamCapacityAnalysis,
  ConflictResolution,
} from '../types';

export class FallbackProvider extends AIProvider {
  constructor() {
    super({ provider: 'fallback' }, 'fallback');
  }

  async isAvailable(): Promise<boolean> {
    return true; // Fallback is always available
  }

  async healthCheck(): Promise<boolean> {
    return true; // Fallback is always healthy
  }

  async chatCompletion(request: ChatCompletionRequest): Promise<ChatCompletionResponse> {
    // Fallback doesn't support general chat completion
    throw new Error('Chat completion not supported by fallback provider');
  }

  async analyzeMeetingNecessity(
    title: string,
    description: string,
    participants: string[],
    duration: number,
    context?: string
  ): Promise<MeetingNecessityAnalysis> {
    logger.info('Using fallback meeting necessity analysis');

    let score = 50; // Base score

    // Adjust score based on participants
    if (participants.length <= 2) {
      score -= 20; // Small meetings might not be necessary
    } else if (participants.length > 8) {
      score -= 10; // Large meetings can be inefficient
    } else {
      score += 10; // Optimal size
    }

    // Adjust score based on duration
    if (duration <= 15) {
      score += 10; // Short meetings are good
    } else if (duration > 60) {
      score -= 15; // Long meetings often unnecessary
    }

    // Adjust score based on title keywords
    const urgentKeywords = ['urgent', 'critical', 'emergency', 'asap', 'immediate'];
    const planningKeywords = ['planning', 'strategy', 'roadmap', 'quarterly', 'annual'];
    const updateKeywords = ['update', 'status', 'sync', 'standup', 'check-in'];
    const decisionKeywords = ['decision', 'approve', 'review', 'sign-off', 'vote'];

    const titleLower = title.toLowerCase();
    const descriptionLower = description.toLowerCase();

    if (urgentKeywords.some(keyword => 
      titleLower.includes(keyword) || descriptionLower.includes(keyword)
    )) {
      score += 20;
    }

    if (planningKeywords.some(keyword => 
      titleLower.includes(keyword) || descriptionLower.includes(keyword)
    )) {
      score += 15;
    }

    if (decisionKeywords.some(keyword => 
      titleLower.includes(keyword) || descriptionLower.includes(keyword)
    )) {
      score += 10;
    }

    if (updateKeywords.some(keyword => 
      titleLower.includes(keyword) || descriptionLower.includes(keyword)
    )) {
      score -= 10; // Updates can often be async
    }

    // Check for async-friendly keywords
    const asyncKeywords = ['fyi', 'info', 'announcement', 'broadcast'];
    if (asyncKeywords.some(keyword => 
      titleLower.includes(keyword) || descriptionLower.includes(keyword)
    )) {
      score -= 15;
    }

    score = Math.max(0, Math.min(100, score));

    return {
      score,
      reasoning: 'Analysis based on meeting characteristics and heuristics',
      alternatives: score < 60 ? ['Email update', 'Slack discussion', 'Shared document'] : [],
      recommendations: [
        score > 80 ? 'Meeting is highly necessary' : 'Consider if meeting is essential',
        participants.length > 5 ? 'Consider reducing participants' : 'Participant count is appropriate',
        duration > 60 ? 'Consider shortening the meeting' : 'Duration seems reasonable',
      ],
    };
  }

  async analyzeTaskPriority(
    title: string,
    description: string,
    dueDate?: Date,
    dependencies?: string[],
    projectContext?: string
  ): Promise<TaskPriorityAnalysis> {
    logger.info('Using fallback task priority analysis');

    let priorityScore = 50; // Base score
    let estimatedHours = 8; // Default estimate

    // Analyze due date urgency
    if (dueDate) {
      const now = new Date();
      const daysUntilDue = Math.ceil((dueDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
      
      if (daysUntilDue <= 1) {
        priorityScore += 30; // Very urgent
      } else if (daysUntilDue <= 3) {
        priorityScore += 20; // Urgent
      } else if (daysUntilDue <= 7) {
        priorityScore += 10; // Somewhat urgent
      } else if (daysUntilDue > 30) {
        priorityScore -= 10; // Not urgent
      }
    }

    // Analyze title and description for priority keywords
    const highPriorityKeywords = ['critical', 'urgent', 'blocker', 'emergency', 'hotfix', 'security'];
    const mediumPriorityKeywords = ['important', 'feature', 'enhancement', 'improvement'];
    const lowPriorityKeywords = ['nice-to-have', 'optional', 'future', 'cleanup', 'refactor'];

    const titleLower = title.toLowerCase();
    const descriptionLower = description.toLowerCase();

    if (highPriorityKeywords.some(keyword => 
      titleLower.includes(keyword) || descriptionLower.includes(keyword)
    )) {
      priorityScore += 25;
    }

    if (mediumPriorityKeywords.some(keyword => 
      titleLower.includes(keyword) || descriptionLower.includes(keyword)
    )) {
      priorityScore += 10;
    }

    if (lowPriorityKeywords.some(keyword => 
      titleLower.includes(keyword) || descriptionLower.includes(keyword)
    )) {
      priorityScore -= 15;
    }

    // Analyze complexity indicators
    const complexityKeywords = ['integration', 'migration', 'architecture', 'database', 'api'];
    if (complexityKeywords.some(keyword => 
      titleLower.includes(keyword) || descriptionLower.includes(keyword)
    )) {
      estimatedHours += 8; // More complex tasks take longer
    }

    // Adjust for dependencies
    if (dependencies && dependencies.length > 0) {
      priorityScore += dependencies.length * 5; // More dependencies = higher priority
    }

    // Determine priority level
    let priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
    if (priorityScore >= 80) {
      priority = 'CRITICAL';
    } else if (priorityScore >= 65) {
      priority = 'HIGH';
    } else if (priorityScore >= 40) {
      priority = 'MEDIUM';
    } else {
      priority = 'LOW';
    }

    return {
      priority,
      reasoning: 'Priority determined using heuristic analysis of task characteristics',
      estimatedHours,
      dependencies: dependencies || [],
    };
  }

  async analyzeTeamCapacity(
    teamMembers: Array<{
      id: string;
      name: string;
      skills: string[];
      currentTasks: number;
      availability: number;
    }>,
    upcomingTasks: Array<{
      title: string;
      estimatedHours: number;
      requiredSkills: string[];
    }>
  ): Promise<TeamCapacityAnalysis> {
    logger.info('Using fallback team capacity analysis');

    // Calculate overall utilization
    const totalAvailability = teamMembers.reduce((sum, member) => sum + member.availability, 0);
    const totalCurrentTasks = teamMembers.reduce((sum, member) => sum + member.currentTasks, 0);
    const overallUtilization = totalAvailability > 0 ? Math.min(100, (totalCurrentTasks / totalAvailability) * 100) : 0;

    // Identify bottlenecks (members with >80% utilization)
    const bottlenecks = teamMembers
      .filter(member => {
        const utilization = member.availability > 0 ? (member.currentTasks / member.availability) * 100 : 0;
        return utilization > 80;
      })
      .map(member => ({
        userId: member.id,
        utilization: member.availability > 0 ? (member.currentTasks / member.availability) * 100 : 0,
        skills: member.skills,
      }));

    // Generate recommendations
    const recommendations = [];
    
    if (overallUtilization > 90) {
      recommendations.push('Team is severely overutilized - consider hiring or reducing scope');
    } else if (overallUtilization > 75) {
      recommendations.push('Team is highly utilized - monitor workload carefully');
    } else if (overallUtilization < 50) {
      recommendations.push('Team has available capacity for additional work');
    }

    if (bottlenecks.length > 0) {
      recommendations.push(`${bottlenecks.length} team member(s) are overutilized - consider redistributing tasks`);
    }

    // Check for skill gaps
    const allRequiredSkills = upcomingTasks.flatMap(task => task.requiredSkills);
    const availableSkills = teamMembers.flatMap(member => member.skills);
    const missingSkills = allRequiredSkills.filter(skill => !availableSkills.includes(skill));
    
    if (missingSkills.length > 0) {
      recommendations.push(`Missing skills detected: ${[...new Set(missingSkills)].join(', ')}`);
    }

    return {
      overallUtilization,
      bottlenecks,
      recommendations,
    };
  }

  async detectConflicts(
    teamId: string,
    startDate: Date,
    endDate: Date,
    meetings: any[],
    tasks: any[]
  ): Promise<ConflictResolution> {
    logger.info('Using fallback conflict detection');

    const conflicts = [];
    const resolutions = [];

    // Check for meeting time conflicts
    const sortedMeetings = meetings.sort((a, b) => new Date(a.startTime).getTime() - new Date(b.startTime).getTime());
    
    for (let i = 0; i < sortedMeetings.length - 1; i++) {
      const current = sortedMeetings[i];
      const next = sortedMeetings[i + 1];
      
      if (new Date(current.endTime) > new Date(next.startTime)) {
        conflicts.push({
          type: 'schedule' as const,
          description: `Meeting "${current.title}" conflicts with "${next.title}"`,
          severity: 'high' as const,
        });
      }
    }

    // Check for resource conflicts (simplified)
    const tasksByAssignee = tasks.reduce((acc: any, task: any) => {
      if (task.assigneeId) {
        if (!acc[task.assigneeId]) acc[task.assigneeId] = [];
        acc[task.assigneeId].push(task);
      }
      return acc;
    }, {});

    Object.entries(tasksByAssignee).forEach(([assigneeId, assigneeTasks]: [string, any]) => {
      if (assigneeTasks.length > 5) { // Arbitrary threshold
        conflicts.push({
          type: 'resource' as const,
          description: `User ${assigneeId} has ${assigneeTasks.length} tasks assigned`,
          severity: 'medium' as const,
        });
      }
    });

    // Generate resolutions for conflicts
    if (conflicts.some(c => c.type === 'schedule')) {
      resolutions.push({
        conflictType: 'schedule',
        solution: 'Reschedule conflicting meetings to different time slots',
        impact: 'Improved team availability and reduced scheduling stress',
      });
    }

    if (conflicts.some(c => c.type === 'resource')) {
      resolutions.push({
        conflictType: 'resource',
        solution: 'Redistribute tasks among team members more evenly',
        impact: 'Better workload balance and improved team productivity',
      });
    }

    return {
      conflicts,
      resolutions,
    };
  }
}
