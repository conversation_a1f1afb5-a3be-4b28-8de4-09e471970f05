import { PrismaClient, UserRole, TeamRole, ProjectStatus, TaskStatus, Priority } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting database seeding...');

  // Create admin user
  const adminPassword = await bcrypt.hash('admin123', 12);
  const admin = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      password: adminPassword,
      firstName: 'Admin',
      lastName: 'User',
      role: UserRole.ADMIN,
      skills: ['Leadership', 'Project Management', 'Strategy'],
      timezone: 'UTC',
    },
  });

  // Create sample users
  const users = await Promise.all([
    prisma.user.create({
      data: {
        email: '<EMAIL>',
        password: await bcrypt.hash('password123', 12),
        firstName: '<PERSON>',
        lastName: 'Doe',
        role: User<PERSON>ole.MANAGER,
        skills: ['JavaScript', 'React', 'Node.js', 'Team Leadership'],
        timezone: 'America/New_York',
      },
    }),
    prisma.user.create({
      data: {
        email: '<EMAIL>',
        password: await bcrypt.hash('password123', 12),
        firstName: 'Jane',
        lastName: 'Smith',
        role: UserRole.MEMBER,
        skills: ['Python', 'Django', 'PostgreSQL', 'API Design'],
        timezone: 'Europe/London',
      },
    }),
    prisma.user.create({
      data: {
        email: '<EMAIL>',
        password: await bcrypt.hash('password123', 12),
        firstName: 'Mike',
        lastName: 'Johnson',
        role: UserRole.MEMBER,
        skills: ['UI/UX Design', 'Figma', 'React', 'CSS'],
        timezone: 'America/Los_Angeles',
      },
    }),
  ]);

  // Create sample team
  const team = await prisma.team.create({
    data: {
      name: 'Development Team',
      description: 'Main development team for CollabFlow platform',
    },
  });

  // Add team members
  await Promise.all([
    prisma.teamMember.create({
      data: {
        teamId: team.id,
        userId: users[0].id,
        role: TeamRole.LEAD,
      },
    }),
    prisma.teamMember.create({
      data: {
        teamId: team.id,
        userId: users[1].id,
        role: TeamRole.SENIOR,
      },
    }),
    prisma.teamMember.create({
      data: {
        teamId: team.id,
        userId: users[2].id,
        role: TeamRole.MEMBER,
      },
    }),
  ]);

  // Create team settings
  await prisma.teamSettings.create({
    data: {
      teamId: team.id,
      workingHoursStart: '09:00',
      workingHoursEnd: '17:00',
      timezone: 'UTC',
      maxDailyMeetings: 4,
      preferredMeetingLength: 30,
      bufferTime: 15,
      noMeetingDays: ['Saturday', 'Sunday'],
    },
  });

  // Create sample project
  const project = await prisma.project.create({
    data: {
      name: 'CollabFlow MVP',
      description: 'Minimum viable product for CollabFlow platform',
      teamId: team.id,
      creatorId: users[0].id,
      status: ProjectStatus.ACTIVE,
      priority: Priority.HIGH,
      startDate: new Date(),
      endDate: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000), // 90 days from now
    },
  });

  // Create sample tasks
  const tasks = await Promise.all([
    prisma.task.create({
      data: {
        title: 'Set up authentication system',
        description: 'Implement JWT-based authentication with role-based access control',
        projectId: project.id,
        assigneeId: users[1].id,
        status: TaskStatus.IN_PROGRESS,
        priority: Priority.HIGH,
        estimatedHours: 16,
        tags: ['backend', 'security', 'authentication'],
        dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
      },
    }),
    prisma.task.create({
      data: {
        title: 'Design user interface mockups',
        description: 'Create wireframes and mockups for main application screens',
        projectId: project.id,
        assigneeId: users[2].id,
        status: TaskStatus.TODO,
        priority: Priority.MEDIUM,
        estimatedHours: 24,
        tags: ['frontend', 'design', 'ui/ux'],
        dueDate: new Date(Date.now() + 10 * 24 * 60 * 60 * 1000), // 10 days from now
      },
    }),
    prisma.task.create({
      data: {
        title: 'Implement real-time notifications',
        description: 'Set up WebSocket connections for real-time updates',
        projectId: project.id,
        assigneeId: users[0].id,
        status: TaskStatus.TODO,
        priority: Priority.MEDIUM,
        estimatedHours: 12,
        tags: ['backend', 'websockets', 'real-time'],
        dueDate: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000), // 14 days from now
      },
    }),
  ]);

  // Create task dependencies
  await prisma.taskDependency.create({
    data: {
      taskId: tasks[2].id, // Real-time notifications depends on authentication
      dependencyId: tasks[0].id,
    },
  });

  // Create sample meeting
  const meeting = await prisma.meeting.create({
    data: {
      title: 'Sprint Planning Meeting',
      description: 'Plan tasks for the upcoming sprint',
      organizerId: users[0].id,
      startTime: new Date(Date.now() + 24 * 60 * 60 * 1000), // Tomorrow
      endTime: new Date(Date.now() + 24 * 60 * 60 * 1000 + 60 * 60 * 1000), // Tomorrow + 1 hour
      necessityScore: 85,
    },
  });

  // Add meeting participants
  await Promise.all([
    prisma.meetingParticipant.create({
      data: {
        meetingId: meeting.id,
        userId: users[0].id,
      },
    }),
    prisma.meetingParticipant.create({
      data: {
        meetingId: meeting.id,
        userId: users[1].id,
      },
    }),
    prisma.meetingParticipant.create({
      data: {
        meetingId: meeting.id,
        userId: users[2].id,
      },
    }),
  ]);

  // Create agenda items
  await Promise.all([
    prisma.agendaItem.create({
      data: {
        meetingId: meeting.id,
        title: 'Review previous sprint',
        description: 'Discuss completed tasks and blockers',
        duration: 15,
        order: 1,
      },
    }),
    prisma.agendaItem.create({
      data: {
        meetingId: meeting.id,
        title: 'Plan upcoming tasks',
        description: 'Assign tasks for the next sprint',
        duration: 30,
        order: 2,
      },
    }),
    prisma.agendaItem.create({
      data: {
        meetingId: meeting.id,
        title: 'Discuss blockers',
        description: 'Address any current or potential blockers',
        duration: 15,
        order: 3,
      },
    }),
  ]);

  // Create sample user availability
  const daysOfWeek = [1, 2, 3, 4, 5]; // Monday to Friday
  for (const user of users) {
    for (const day of daysOfWeek) {
      await prisma.userAvailability.create({
        data: {
          userId: user.id,
          dayOfWeek: day,
          startTime: '09:00',
          endTime: '17:00',
        },
      });
    }
  }

  console.log('✅ Database seeding completed successfully!');
  console.log(`👤 Admin user created: <EMAIL> (password: admin123)`);
  console.log(`👥 ${users.length} sample users created`);
  console.log(`🏢 1 team created with ${users.length} members`);
  console.log(`📋 1 project created with ${tasks.length} tasks`);
  console.log(`📅 1 meeting created with 3 agenda items`);
}

main()
  .catch((e) => {
    console.error('❌ Error during seeding:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
