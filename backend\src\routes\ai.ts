import { Router, Request, Response } from 'express';
import { validationResult, body, query } from 'express-validator';
import { aiService } from '../services/aiService';
import { schedulingService } from '../services/schedulingService';
import { asyncHandler, CustomError } from '../middleware/errorHandler';
import { AuthenticatedRequest, requireRole } from '../middleware/auth';
import { prisma } from '../config/database';

const router = Router();

// Helper function to handle validation errors
const handleValidationErrors = (req: Request, res: Response, next: any) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errors.array(),
    });
  }
  next();
};

// Meeting necessity analysis
router.post('/meetings/analyze-necessity',
  [
    body('title').notEmpty().withMessage('Meeting title is required'),
    body('description').optional().isString(),
    body('participants').isArray().withMessage('Participants must be an array'),
    body('duration').isInt({ min: 15, max: 480 }).withMessage('Duration must be between 15 and 480 minutes'),
    body('context').optional().isString(),
  ],
  handleValidationErrors,
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const { title, description, participants, duration, context } = req.body;

    const analysis = await aiService.analyzeMeetingNecessity(
      title,
      description || '',
      participants,
      duration,
      context
    );

    res.json({
      success: true,
      message: 'Meeting necessity analysis completed',
      data: { analysis },
    });
  })
);

// Task priority analysis
router.post('/tasks/analyze-priority',
  [
    body('title').notEmpty().withMessage('Task title is required'),
    body('description').optional().isString(),
    body('dueDate').optional().isISO8601().withMessage('Due date must be a valid ISO date'),
    body('dependencies').optional().isArray(),
    body('projectContext').optional().isString(),
  ],
  handleValidationErrors,
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const { title, description, dueDate, dependencies, projectContext } = req.body;

    const analysis = await aiService.analyzeTaskPriority(
      title,
      description || '',
      dueDate ? new Date(dueDate) : undefined,
      dependencies,
      projectContext
    );

    res.json({
      success: true,
      message: 'Task priority analysis completed',
      data: { analysis },
    });
  })
);

// Team capacity analysis
router.get('/teams/:teamId/analyze-capacity',
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const { teamId } = req.params;

    // Get team members with their current workload
    const team = await prisma.team.findUnique({
      where: { id: teamId },
      include: {
        members: {
          include: {
            team: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                skills: true,
              },
            },
            _count: {
              select: {
                assignedTasks: {
                  where: {
                    status: {
                      in: ['TODO', 'IN_PROGRESS'],
                    },
                  },
                },
              },
            },
          },
        },
      },
    });

    if (!team) {
      throw new CustomError('Team not found', 404);
    }

    // Get user availability
    const memberIds = team.members.map(m => m.userId);
    const availability = await prisma.userAvailability.findMany({
      where: {
        userId: { in: memberIds },
      },
    });

    // Calculate weekly availability for each member
    const teamMembers = team.members.map(member => {
      const userAvailability = availability.filter(a => a.userId === member.userId);
      const weeklyHours = userAvailability.reduce((total, slot) => {
        const start = parseInt(slot.startTime.split(':')[0]);
        const end = parseInt(slot.endTime.split(':')[0]);
        return total + (end - start);
      }, 0);

      return {
        id: member.userId,
        name: `${member.team.firstName} ${member.team.lastName}`,
        skills: member.team.skills,
        currentTasks: member._count.assignedTasks,
        availability: weeklyHours || 40, // Default to 40 hours if no availability set
      };
    });

    // Get upcoming tasks for the team
    const upcomingTasks = await prisma.task.findMany({
      where: {
        project: {
          teamId,
        },
        status: 'TODO',
      },
      select: {
        title: true,
        estimatedHours: true,
        tags: true,
      },
    });

    const upcomingTasksFormatted = upcomingTasks.map(task => ({
      title: task.title,
      estimatedHours: task.estimatedHours,
      requiredSkills: task.tags, // Using tags as required skills for now
    }));

    const analysis = await aiService.analyzeTeamCapacity(teamMembers, upcomingTasksFormatted);

    res.json({
      success: true,
      message: 'Team capacity analysis completed',
      data: { analysis },
    });
  })
);

// Conflict detection
router.post('/conflicts/detect',
  [
    body('teamId').isUUID().withMessage('Valid team ID is required'),
    body('startDate').isISO8601().withMessage('Start date must be a valid ISO date'),
    body('endDate').isISO8601().withMessage('End date must be a valid ISO date'),
  ],
  handleValidationErrors,
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const { teamId, startDate, endDate } = req.body;

    // Get meetings in the specified period
    const meetings = await prisma.meeting.findMany({
      where: {
        startTime: {
          gte: new Date(startDate),
          lte: new Date(endDate),
        },
        participants: {
          some: {
            user: {
              teamMemberships: {
                some: { teamId },
              },
            },
          },
        },
      },
      include: {
        participants: {
          select: {
            userId: true,
          },
        },
      },
    });

    // Get tasks in the specified period
    const tasks = await prisma.task.findMany({
      where: {
        project: { teamId },
        dueDate: {
          gte: new Date(startDate),
          lte: new Date(endDate),
        },
      },
      select: {
        id: true,
        title: true,
        assigneeId: true,
        dueDate: true,
        priority: true,
      },
    });

    const meetingsFormatted = meetings.map(meeting => ({
      id: meeting.id,
      title: meeting.title,
      startTime: meeting.startTime,
      endTime: meeting.endTime,
      participants: meeting.participants.map(p => p.userId),
    }));

    const tasksFormatted = tasks.map(task => ({
      id: task.id,
      title: task.title,
      assigneeId: task.assigneeId || '',
      dueDate: task.dueDate || new Date(),
      priority: task.priority,
    }));

    const conflictAnalysis = await aiService.detectConflicts(meetingsFormatted, tasksFormatted);

    res.json({
      success: true,
      message: 'Conflict detection completed',
      data: { conflictAnalysis },
    });
  })
);

// Optimal meeting time suggestions
router.post('/meetings/suggest-times',
  [
    body('duration').isInt({ min: 15, max: 480 }).withMessage('Duration must be between 15 and 480 minutes'),
    body('participants').isArray().withMessage('Participants must be an array'),
    body('preferredTimes').optional().isArray(),
    body('timezone').optional().isString(),
    body('bufferTime').optional().isInt({ min: 0, max: 60 }),
    body('excludeDates').optional().isArray(),
    body('lookAheadDays').optional().isInt({ min: 1, max: 30 }),
  ],
  handleValidationErrors,
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const {
      duration,
      participants,
      preferredTimes,
      timezone,
      bufferTime,
      excludeDates,
      lookAheadDays,
    } = req.body;

    const options = {
      duration,
      participants,
      preferredTimes,
      timezone,
      bufferTime,
      excludeDates: excludeDates?.map((date: string) => new Date(date)),
    };

    const suggestions = await schedulingService.findOptimalMeetingTimes(
      options,
      lookAheadDays || 14
    );

    res.json({
      success: true,
      message: 'Meeting time suggestions generated',
      data: { suggestions },
    });
  })
);

// Schedule recurring meeting with AI optimization
router.post('/meetings/schedule-recurring',
  [
    body('title').notEmpty().withMessage('Meeting title is required'),
    body('description').optional().isString(),
    body('participants').isArray().withMessage('Participants must be an array'),
    body('duration').isInt({ min: 15, max: 480 }).withMessage('Duration must be between 15 and 480 minutes'),
    body('recurrence.frequency').isIn(['daily', 'weekly', 'monthly']).withMessage('Invalid frequency'),
    body('recurrence.interval').isInt({ min: 1, max: 12 }).withMessage('Interval must be between 1 and 12'),
    body('recurrence.daysOfWeek').optional().isArray(),
    body('recurrence.endDate').optional().isISO8601(),
    body('recurrence.occurrences').optional().isInt({ min: 1, max: 100 }),
  ],
  handleValidationErrors,
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const {
      title,
      description,
      participants,
      duration,
      recurrence,
      preferredTimes,
      timezone,
      bufferTime,
    } = req.body;

    const meetingData = {
      title,
      description,
      organizerId: req.user!.id,
      participants,
      duration,
      recurrence: {
        ...recurrence,
        endDate: recurrence.endDate ? new Date(recurrence.endDate) : undefined,
      },
    };

    const schedulingOptions = {
      duration,
      participants,
      preferredTimes,
      timezone,
      bufferTime,
    };

    const meetingIds = await schedulingService.scheduleRecurringMeeting(
      meetingData,
      schedulingOptions
    );

    res.json({
      success: true,
      message: `${meetingIds.length} recurring meetings scheduled successfully`,
      data: { meetingIds },
    });
  })
);

// Optimize existing team schedule
router.post('/teams/:teamId/optimize-schedule',
  [
    body('startDate').isISO8601().withMessage('Start date must be a valid ISO date'),
    body('endDate').isISO8601().withMessage('End date must be a valid ISO date'),
  ],
  handleValidationErrors,
  requireRole(['ADMIN', 'MANAGER']),
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const { teamId } = req.params;
    const { startDate, endDate } = req.body;

    const optimization = await schedulingService.optimizeExistingSchedule(
      teamId,
      {
        start: new Date(startDate),
        end: new Date(endDate),
      }
    );

    res.json({
      success: true,
      message: 'Schedule optimization completed',
      data: { optimization },
    });
  })
);

// AI-powered task assignment suggestions
router.post('/tasks/suggest-assignment',
  [
    body('taskId').isUUID().withMessage('Valid task ID is required'),
    body('teamId').isUUID().withMessage('Valid team ID is required'),
  ],
  handleValidationErrors,
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const { taskId, teamId } = req.body;

    // Get task details
    const task = await prisma.task.findUnique({
      where: { id: taskId },
      include: {
        project: {
          select: {
            teamId: true,
          },
        },
      },
    });

    if (!task) {
      throw new CustomError('Task not found', 404);
    }

    if (task.project.teamId !== teamId) {
      throw new CustomError('Task does not belong to the specified team', 400);
    }

    // Get team members with their skills and current workload
    const teamMembers = await prisma.teamMember.findMany({
      where: { teamId },
      include: {
        team: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            skills: true,
          },
        },
        _count: {
          select: {
            assignedTasks: {
              where: {
                status: {
                  in: ['TODO', 'IN_PROGRESS'],
                },
              },
            },
          },
        },
      },
    });

    // Simple assignment algorithm based on skills and workload
    const suggestions = teamMembers
      .map(member => {
        const skillMatch = task.tags.filter(tag => 
          member.team.skills.includes(tag)
        ).length;
        
        const workloadScore = Math.max(0, 10 - member._count.assignedTasks);
        const skillScore = skillMatch * 2;
        const totalScore = workloadScore + skillScore;

        return {
          userId: member.userId,
          name: `${member.team.firstName} ${member.team.lastName}`,
          skills: member.team.skills,
          currentTasks: member._count.assignedTasks,
          skillMatch,
          score: totalScore,
          reasoning: `Skill match: ${skillMatch}/${task.tags.length}, Current workload: ${member._count.assignedTasks} tasks`,
        };
      })
      .sort((a, b) => b.score - a.score)
      .slice(0, 3); // Top 3 suggestions

    res.json({
      success: true,
      message: 'Task assignment suggestions generated',
      data: { suggestions },
    });
  })
);

export default router;
