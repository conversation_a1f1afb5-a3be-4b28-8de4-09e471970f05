import axios, { AxiosInstance, AxiosError } from 'axios';
import { logger } from '../../../config/logger';
import {
  AIProvider,
  AIProviderConfig,
  ChatCompletionRequest,
  ChatCompletionResponse,
  MeetingNecessityAnalysis,
  TaskPriorityAnalysis,
  TeamCapacityAnalysis,
  ConflictResolution,
} from '../types';

export class OllamaProvider extends AIProvider {
  private client: AxiosInstance;
  private defaultModel: string;

  constructor(config: AIProviderConfig) {
    super(config, 'ollama');
    
    this.defaultModel = config.model || 'llama2:7b';
    this.client = axios.create({
      baseURL: config.baseUrl || 'http://localhost:11434',
      timeout: config.timeout || 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Add request/response interceptors for logging
    this.client.interceptors.request.use(
      (config) => {
        logger.debug(`Ollama request: ${config.method?.toUpperCase()} ${config.url}`);
        return config;
      },
      (error) => {
        logger.error('Ollama request error:', error);
        return Promise.reject(error);
      }
    );

    this.client.interceptors.response.use(
      (response) => {
        logger.debug(`Ollama response: ${response.status} ${response.statusText}`);
        return response;
      },
      (error) => {
        logger.error('Ollama response error:', error.message);
        return Promise.reject(error);
      }
    );
  }

  async isAvailable(): Promise<boolean> {
    try {
      const response = await this.client.get('/api/tags', { timeout: 5000 });
      return response.status === 200;
    } catch (error) {
      logger.warn('Ollama is not available:', error instanceof Error ? error.message : 'Unknown error');
      return false;
    }
  }

  async healthCheck(): Promise<boolean> {
    try {
      // Check if Ollama is running
      const isRunning = await this.isAvailable();
      if (!isRunning) return false;

      // Check if the default model is available
      const response = await this.client.get('/api/tags');
      const models = response.data.models || [];
      const modelExists = models.some((model: any) => model.name === this.defaultModel);
      
      if (!modelExists) {
        logger.warn(`Model ${this.defaultModel} not found. Available models:`, models.map((m: any) => m.name));
        return false;
      }

      return true;
    } catch (error) {
      logger.error('Ollama health check failed:', error);
      return false;
    }
  }

  async chatCompletion(request: ChatCompletionRequest): Promise<ChatCompletionResponse> {
    try {
      const model = request.model || this.defaultModel;
      
      // Convert OpenAI-style messages to Ollama format
      const prompt = this.convertMessagesToPrompt(request.messages);
      
      const ollamaRequest = {
        model,
        prompt,
        stream: false,
        options: {
          temperature: request.temperature || 0.3,
          num_predict: request.maxTokens || 500,
        },
      };

      const response = await this.client.post('/api/generate', ollamaRequest);
      
      // Convert Ollama response to OpenAI format
      return {
        choices: [
          {
            message: {
              content: response.data.response || '',
              role: 'assistant',
            },
            finishReason: response.data.done ? 'stop' : 'length',
          },
        ],
        usage: {
          promptTokens: 0, // Ollama doesn't provide token counts
          completionTokens: 0,
          totalTokens: 0,
        },
      };
    } catch (error) {
      logger.error('Ollama chat completion failed:', error);
      throw new Error(`Ollama chat completion failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async analyzeMeetingNecessity(
    title: string,
    description: string,
    participants: string[],
    duration: number,
    context?: string
  ): Promise<MeetingNecessityAnalysis> {
    const prompt = `
Analyze the necessity of this meeting and provide a score from 0-100:

Meeting Details:
- Title: ${title}
- Description: ${description}
- Participants: ${participants.length} people (${participants.join(', ')})
- Duration: ${duration} minutes
- Context: ${context || 'None provided'}

Please respond with a JSON object containing:
- score: number (0-100, where 100 is absolutely necessary)
- reasoning: string explaining the score
- alternatives: array of strings suggesting alternatives if score is low
- recommendations: array of strings with actionable advice

Consider factors like:
- Number of participants vs decision complexity
- Whether this could be handled asynchronously
- Time investment vs expected outcomes
- Urgency and importance of the topic

Respond only with valid JSON.`;

    try {
      const response = await this.chatCompletion({
        messages: [
          {
            role: 'system',
            content: 'You are an expert in team productivity and meeting optimization. Provide practical, actionable insights in valid JSON format.',
          },
          {
            role: 'user',
            content: prompt,
          },
        ],
        temperature: 0.3,
        maxTokens: 500,
      });

      const content = response.choices[0]?.message?.content;
      if (!content) {
        throw new Error('No response from AI service');
      }

      const analysis = JSON.parse(content);
      
      // Validate response structure
      if (typeof analysis.score !== 'number' || analysis.score < 0 || analysis.score > 100) {
        throw new Error('Invalid score in AI response');
      }

      return analysis;
    } catch (error) {
      logger.error('Ollama meeting analysis failed:', error);
      throw error;
    }
  }

  async analyzeTaskPriority(
    title: string,
    description: string,
    dueDate?: Date,
    dependencies?: string[],
    projectContext?: string
  ): Promise<TaskPriorityAnalysis> {
    const dueDateStr = dueDate ? dueDate.toISOString().split('T')[0] : 'No due date';
    const dependenciesStr = dependencies?.length ? dependencies.join(', ') : 'None';

    const prompt = `
Analyze this task and determine its priority level:

Task Details:
- Title: ${title}
- Description: ${description}
- Due Date: ${dueDateStr}
- Dependencies: ${dependenciesStr}
- Project Context: ${projectContext || 'None provided'}

Please respond with a JSON object containing:
- priority: one of "LOW", "MEDIUM", "HIGH", "CRITICAL"
- reasoning: string explaining the priority assignment
- estimatedHours: number (estimated hours to complete)
- dependencies: array of strings listing key dependencies

Consider factors like:
- Business impact and urgency
- Complexity and effort required
- Dependencies and blockers
- Due date proximity
- Strategic importance

Respond only with valid JSON.`;

    try {
      const response = await this.chatCompletion({
        messages: [
          {
            role: 'system',
            content: 'You are an expert project manager. Analyze tasks objectively based on impact, urgency, and complexity. Respond in valid JSON format.',
          },
          {
            role: 'user',
            content: prompt,
          },
        ],
        temperature: 0.2,
        maxTokens: 400,
      });

      const content = response.choices[0]?.message?.content;
      if (!content) {
        throw new Error('No response from AI service');
      }

      return JSON.parse(content);
    } catch (error) {
      logger.error('Ollama task analysis failed:', error);
      throw error;
    }
  }

  async analyzeTeamCapacity(
    teamMembers: Array<{
      id: string;
      name: string;
      skills: string[];
      currentTasks: number;
      availability: number;
    }>,
    upcomingTasks: Array<{
      title: string;
      estimatedHours: number;
      requiredSkills: string[];
    }>
  ): Promise<TeamCapacityAnalysis> {
    // Implementation for team capacity analysis
    // This is a simplified version - you can expand based on needs
    const totalCapacity = teamMembers.reduce((sum, member) => sum + member.availability, 0);
    const totalWorkload = teamMembers.reduce((sum, member) => sum + member.currentTasks, 0);
    const overallUtilization = Math.min(100, (totalWorkload / Math.max(totalCapacity, 1)) * 100);

    const bottlenecks = teamMembers
      .filter(member => (member.currentTasks / Math.max(member.availability, 1)) > 0.8)
      .map(member => ({
        userId: member.id,
        utilization: (member.currentTasks / Math.max(member.availability, 1)) * 100,
        skills: member.skills,
      }));

    const recommendations = [];
    if (overallUtilization > 80) {
      recommendations.push('Team is at high utilization - consider redistributing tasks');
    }
    if (bottlenecks.length > 0) {
      recommendations.push(`${bottlenecks.length} team members are overutilized`);
    }

    return {
      overallUtilization,
      bottlenecks,
      recommendations,
    };
  }

  async detectConflicts(
    teamId: string,
    startDate: Date,
    endDate: Date,
    meetings: any[],
    tasks: any[]
  ): Promise<ConflictResolution> {
    // Simplified conflict detection - can be enhanced
    const conflicts = [];
    const resolutions = [];

    // Check for scheduling conflicts
    const overlappingMeetings = meetings.filter((meeting, index) => 
      meetings.slice(index + 1).some(other => 
        new Date(meeting.startTime) < new Date(other.endTime) &&
        new Date(meeting.endTime) > new Date(other.startTime)
      )
    );

    if (overlappingMeetings.length > 0) {
      conflicts.push({
        type: 'schedule' as const,
        description: `${overlappingMeetings.length} meetings have scheduling conflicts`,
        severity: 'high' as const,
      });

      resolutions.push({
        conflictType: 'schedule',
        solution: 'Reschedule conflicting meetings to different time slots',
        impact: 'Improved team availability and reduced stress',
      });
    }

    return { conflicts, resolutions };
  }

  private convertMessagesToPrompt(messages: Array<{ role: string; content: string }>): string {
    return messages
      .map(msg => {
        if (msg.role === 'system') {
          return `System: ${msg.content}`;
        } else if (msg.role === 'user') {
          return `Human: ${msg.content}`;
        } else {
          return `Assistant: ${msg.content}`;
        }
      })
      .join('\n\n') + '\n\nAssistant:';
  }
}
