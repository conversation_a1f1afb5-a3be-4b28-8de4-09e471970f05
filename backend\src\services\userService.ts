import { prisma } from '../config/database';
import { redisClient } from '../config/redis';
import { CustomError } from '../middleware/errorHandler';
import { logger } from '../config/logger';
import { User, UserRole } from '@prisma/client';
import { 
  createUserCacheKey, 
  createSuccessResponse, 
  getPaginationParams,
  createPaginationMeta,
  PaginationOptions 
} from '../utils/helpers';

export interface UpdateUserData {
  firstName?: string;
  lastName?: string;
  avatar?: string;
  skills?: string[];
  timezone?: string;
}

export interface UserAvailabilityData {
  dayOfWeek: number;
  startTime: string;
  endTime: string;
}

class UserService {
  private readonly CACHE_TTL = 3600; // 1 hour

  async getUsers(options: PaginationOptions & { search?: string; role?: UserRole }) {
    try {
      const { skip, take, orderBy } = getPaginationParams({ query: options } as any);
      const { search, role } = options;

      const where: any = {};

      if (search) {
        where.OR = [
          { firstName: { contains: search, mode: 'insensitive' } },
          { lastName: { contains: search, mode: 'insensitive' } },
          { email: { contains: search, mode: 'insensitive' } },
        ];
      }

      if (role) {
        where.role = role;
      }

      const [users, total] = await Promise.all([
        prisma.user.findMany({
          where,
          skip,
          take,
          orderBy: orderBy || { createdAt: 'desc' },
          select: {
            id: true,
            email: true,
            firstName: true,
            lastName: true,
            avatar: true,
            role: true,
            skills: true,
            timezone: true,
            createdAt: true,
            updatedAt: true,
          },
        }),
        prisma.user.count({ where }),
      ]);

      const meta = createPaginationMeta(
        Math.floor(skip / take) + 1,
        take,
        total
      );

      return createSuccessResponse(users, 'Users retrieved successfully', meta);
    } catch (error) {
      logger.error('Error getting users:', error);
      throw error;
    }
  }

  async getUserById(userId: string) {
    try {
      // Try to get from cache first
      const cacheKey = createUserCacheKey(userId, 'profile');
      const cachedUser = await redisClient.cacheGet(cacheKey);

      if (cachedUser) {
        return createSuccessResponse(cachedUser, 'User retrieved from cache');
      }

      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: {
          id: true,
          email: true,
          firstName: true,
          lastName: true,
          avatar: true,
          role: true,
          skills: true,
          timezone: true,
          createdAt: true,
          updatedAt: true,
          teamMemberships: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                },
              },
            },
          },
          availability: true,
        },
      });

      if (!user) {
        throw new CustomError('User not found', 404);
      }

      // Cache the user data
      await redisClient.cacheSet(cacheKey, user, this.CACHE_TTL);

      return createSuccessResponse(user, 'User retrieved successfully');
    } catch (error) {
      logger.error('Error getting user by ID:', error);
      throw error;
    }
  }

  async updateUser(userId: string, data: UpdateUserData) {
    try {
      const user = await prisma.user.update({
        where: { id: userId },
        data,
        select: {
          id: true,
          email: true,
          firstName: true,
          lastName: true,
          avatar: true,
          role: true,
          skills: true,
          timezone: true,
          createdAt: true,
          updatedAt: true,
        },
      });

      // Invalidate cache
      const cacheKey = createUserCacheKey(userId, 'profile');
      await redisClient.del(cacheKey);

      logger.info(`User updated: ${user.email}`);

      return createSuccessResponse(user, 'User updated successfully');
    } catch (error) {
      logger.error('Error updating user:', error);
      throw error;
    }
  }

  async deleteUser(userId: string) {
    try {
      // Check if user exists
      const user = await prisma.user.findUnique({
        where: { id: userId },
      });

      if (!user) {
        throw new CustomError('User not found', 404);
      }

      // Delete user (cascade will handle related records)
      await prisma.user.delete({
        where: { id: userId },
      });

      // Invalidate cache
      const cacheKey = createUserCacheKey(userId, 'profile');
      await redisClient.del(cacheKey);

      logger.info(`User deleted: ${user.email}`);

      return createSuccessResponse(null, 'User deleted successfully');
    } catch (error) {
      logger.error('Error deleting user:', error);
      throw error;
    }
  }

  async updateUserAvailability(userId: string, availability: UserAvailabilityData[]) {
    try {
      // Delete existing availability
      await prisma.userAvailability.deleteMany({
        where: { userId },
      });

      // Create new availability records
      if (availability.length > 0) {
        await prisma.userAvailability.createMany({
          data: availability.map(slot => ({
            userId,
            ...slot,
          })),
        });
      }

      // Get updated availability
      const updatedAvailability = await prisma.userAvailability.findMany({
        where: { userId },
        orderBy: [{ dayOfWeek: 'asc' }, { startTime: 'asc' }],
      });

      // Invalidate cache
      const cacheKey = createUserCacheKey(userId, 'profile');
      await redisClient.del(cacheKey);

      logger.info(`User availability updated for user: ${userId}`);

      return createSuccessResponse(
        updatedAvailability,
        'User availability updated successfully'
      );
    } catch (error) {
      logger.error('Error updating user availability:', error);
      throw error;
    }
  }

  async getUserAvailability(userId: string) {
    try {
      const availability = await prisma.userAvailability.findMany({
        where: { userId },
        orderBy: [{ dayOfWeek: 'asc' }, { startTime: 'asc' }],
      });

      return createSuccessResponse(availability, 'User availability retrieved successfully');
    } catch (error) {
      logger.error('Error getting user availability:', error);
      throw error;
    }
  }

  async getUserTeams(userId: string) {
    try {
      const cacheKey = createUserCacheKey(userId, 'teams');
      const cachedTeams = await redisClient.cacheGet(cacheKey);

      if (cachedTeams) {
        return createSuccessResponse(cachedTeams, 'User teams retrieved from cache');
      }

      const teamMemberships = await prisma.teamMember.findMany({
        where: { userId },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              description: true,
              createdAt: true,
              _count: {
                select: {
                  members: true,
                  projects: true,
                },
              },
            },
          },
        },
      });

      const teams = teamMemberships.map(membership => ({
        ...membership.user,
        memberRole: membership.role,
        joinedAt: membership.joinedAt,
      }));

      // Cache the teams data
      await redisClient.cacheSet(cacheKey, teams, this.CACHE_TTL);

      return createSuccessResponse(teams, 'User teams retrieved successfully');
    } catch (error) {
      logger.error('Error getting user teams:', error);
      throw error;
    }
  }

  async getUserTasks(userId: string, options: PaginationOptions & { status?: string }) {
    try {
      const { skip, take, orderBy } = getPaginationParams({ query: options } as any);
      const { status } = options;

      const where: any = { assigneeId: userId };

      if (status) {
        where.status = status;
      }

      const [tasks, total] = await Promise.all([
        prisma.task.findMany({
          where,
          skip,
          take,
          orderBy: orderBy || { createdAt: 'desc' },
          include: {
            project: {
              select: {
                id: true,
                name: true,
                team: {
                  select: {
                    id: true,
                    name: true,
                  },
                },
              },
            },
          },
        }),
        prisma.task.count({ where }),
      ]);

      const meta = createPaginationMeta(
        Math.floor(skip / take) + 1,
        take,
        total
      );

      return createSuccessResponse(tasks, 'User tasks retrieved successfully', meta);
    } catch (error) {
      logger.error('Error getting user tasks:', error);
      throw error;
    }
  }

  async updateUserRole(userId: string, role: UserRole, updatedBy: string) {
    try {
      // Check if the user being updated exists
      const user = await prisma.user.findUnique({
        where: { id: userId },
      });

      if (!user) {
        throw new CustomError('User not found', 404);
      }

      // Update user role
      const updatedUser = await prisma.user.update({
        where: { id: userId },
        data: { role },
        select: {
          id: true,
          email: true,
          firstName: true,
          lastName: true,
          role: true,
        },
      });

      // Invalidate cache
      const cacheKey = createUserCacheKey(userId, 'profile');
      await redisClient.del(cacheKey);

      logger.info(`User role updated: ${user.email} -> ${role} by ${updatedBy}`);

      return createSuccessResponse(updatedUser, 'User role updated successfully');
    } catch (error) {
      logger.error('Error updating user role:', error);
      throw error;
    }
  }
}

export const userService = new UserService();
