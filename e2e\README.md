# CollabFlow E2E Tests

This directory contains end-to-end tests for the CollabFlow application using Playwright.

## Setup

1. Install dependencies:
```bash
npm install
```

2. Install Playwright browsers:
```bash
npm run install-browsers
```

3. Install system dependencies (Linux only):
```bash
npm run install-deps
```

## Running Tests

### All Tests
```bash
npm test
```

### Specific Test Suites
```bash
npm run test:auth          # Authentication tests
npm run test:teams         # Team management tests
npm run test:tasks         # Task management tests
npm run test:meetings      # Meeting scheduling tests
npm run test:realtime      # Real-time collaboration tests
```

### Browser-Specific Tests
```bash
npm run test:chromium      # Chrome/Chromium only
npm run test:firefox       # Firefox only
npm run test:webkit        # Safari/WebKit only
npm run test:mobile        # Mobile browsers
```

### Development
```bash
npm run test:headed        # Run with browser UI visible
npm run test:debug         # Run in debug mode
npm run test:ui            # Run with <PERSON><PERSON> UI
```

### CI/CD
```bash
npm run test:ci            # Run with GitHub Actions reporter
```

## Test Structure

- `tests/` - Test files
  - `auth.spec.ts` - Authentication flow tests
  - `team-management.spec.ts` - Team creation, management, and collaboration
  - `task-management.spec.ts` - Task creation, assignment, and tracking
  - `meeting-scheduling.spec.ts` - Meeting scheduling and AI optimization
  - `realtime-collaboration.spec.ts` - Real-time features and WebSocket functionality

- `utils/` - Test utilities and helpers
  - `test-helpers.ts` - Common test functions and page object models

- `global-setup.ts` - Global test setup (database, test users, etc.)
- `global-teardown.ts` - Global test cleanup
- `playwright.config.ts` - Playwright configuration

## Test Data

Tests use dynamically generated test data to avoid conflicts. The global setup creates:
- Test user account for authentication tests
- Clean test database state
- Mock API responses where needed

## Environment Variables

The following environment variables are set during test execution:
- `NODE_ENV=test`
- `DATABASE_URL` - Test database connection
- `REDIS_URL` - Test Redis connection
- `JWT_SECRET` - Test JWT secret
- `E2E_TEST_EMAIL` - Test user email
- `E2E_TEST_PASSWORD` - Test user password

## Reports

After running tests, view the HTML report:
```bash
npm run report
```

Reports include:
- Test results and screenshots
- Video recordings of failed tests
- Trace files for debugging
- Performance metrics

## Best Practices

1. **Test Isolation**: Each test should be independent and not rely on other tests
2. **Data Cleanup**: Use unique identifiers to avoid test data conflicts
3. **Wait Strategies**: Use proper wait conditions instead of fixed timeouts
4. **Page Object Model**: Use the TestHelpers class for common operations
5. **Error Handling**: Tests should handle both success and failure scenarios

## Debugging

1. **Visual Debugging**: Use `npm run test:headed` to see browser actions
2. **Step-by-Step**: Use `npm run test:debug` to step through tests
3. **Screenshots**: Failed tests automatically capture screenshots
4. **Videos**: Failed tests record video for analysis
5. **Traces**: Use Playwright trace viewer for detailed debugging

## CI/CD Integration

Tests are configured to run in CI environments with:
- Parallel execution disabled for stability
- Retry logic for flaky tests
- Multiple output formats (HTML, JSON, JUnit)
- Artifact collection for failed tests

## Performance Testing

E2E tests also validate:
- Page load times
- API response times
- Real-time feature latency
- Memory usage patterns
- Network request optimization
