import { io, Socket } from 'socket.io-client';
import { store } from '../store';
import { addNotification, setNotifications } from '../store/slices/notificationsSlice';
import { showNotification } from '../store/slices/uiSlice';
import { updateTask } from '../store/slices/tasksSlice';
import { updateMeeting } from '../store/slices/meetingsSlice';
import toast from 'react-hot-toast';

class WebSocketService {
  private socket: Socket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;

  connect(token: string) {
    if (this.socket?.connected) {
      return;
    }

    const wsUrl = process.env.REACT_APP_WS_URL || 'ws://localhost:3001';

    this.socket = io(wsUrl, {
      auth: {
        token,
      },
      transports: ['websocket', 'polling'],
      timeout: 20000,
      forceNew: true,
    });

    this.setupEventHandlers();
  }

  disconnect() {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
  }

  private setupEventHandlers() {
    if (!this.socket) return;

    // Connection events
    this.socket.on('connect', () => {
      console.log('WebSocket connected');
      this.reconnectAttempts = 0;
      toast.success('Connected to real-time updates');
    });

    this.socket.on('disconnect', (reason) => {
      console.log('WebSocket disconnected:', reason);
      if (reason === 'io server disconnect') {
        // Server initiated disconnect, don't reconnect
        return;
      }
      this.handleReconnect();
    });

    this.socket.on('connect_error', (error) => {
      console.error('WebSocket connection error:', error);
      this.handleReconnect();
    });

    // Notification events
    this.socket.on('notification:new', (notification) => {
      store.dispatch(addNotification(notification));
      toast(notification.message, {
        icon: this.getNotificationIcon(notification.type),
        duration: 4000,
      });
    });

    this.socket.on('notification:marked_read', ({ notificationId }) => {
      // Handle notification marked as read
      console.log('Notification marked as read:', notificationId);
    });

    this.socket.on('notifications:all_marked_read', ({ markedCount }) => {
      console.log(`${markedCount} notifications marked as read`);
    });

    this.socket.on('notifications:unread_count', ({ count }) => {
      // Update unread count in store
      console.log('Unread notifications count:', count);
    });

    // Task events
    this.socket.on('task:created', ({ task, createdBy }) => {
      toast.success(`New task created: ${task.title}`);
    });

    this.socket.on('task:updated', ({ task, updates, updatedBy }) => {
      store.dispatch(updateTask(task));
      
      if (updates.status) {
        toast(`Task "${task.title}" status changed to ${updates.status}`, {
          icon: '📋',
        });
      }
    });

    this.socket.on('task:assigned', ({ task, assignedTo, assignedBy }) => {
      const state = store.getState();
      const currentUserId = state.auth.user?.id;
      
      if (assignedTo.id === currentUserId) {
        toast.success(`You've been assigned to task: ${task.title}`);
      }
    });

    // Meeting events
    this.socket.on('meeting:created', ({ meeting, createdBy }) => {
      toast(`New meeting scheduled: ${meeting.title}`, {
        icon: '📅',
      });
    });

    this.socket.on('meeting:updated', ({ meeting, updates, updatedBy }) => {
      store.dispatch(updateMeeting(meeting));
      toast(`Meeting "${meeting.title}" has been updated`);
    });

    this.socket.on('meeting:participant_joined', ({ user }) => {
      toast(`${user.firstName} ${user.lastName} joined the meeting`, {
        icon: '👋',
        duration: 2000,
      });
    });

    this.socket.on('meeting:participant_left', ({ user }) => {
      toast(`${user.firstName} ${user.lastName} left the meeting`, {
        icon: '👋',
        duration: 2000,
      });
    });

    this.socket.on('meeting:reminder', ({ meeting, minutesUntil }) => {
      toast(`Meeting "${meeting.title}" starts in ${minutesUntil} minutes`, {
        icon: '⏰',
        duration: 6000,
      });
    });

    // Presence events
    this.socket.on('presence:user_status_changed', ({ userId, status }) => {
      console.log(`User ${userId} status changed to ${status}`);
    });

    this.socket.on('presence:team_members', ({ onlineMembers }) => {
      console.log('Online team members:', onlineMembers);
    });

    // Typing indicators
    this.socket.on('typing:user_typing', ({ user, context }) => {
      console.log(`${user.firstName} is typing in ${context}`);
    });

    this.socket.on('typing:user_stopped', ({ user, context }) => {
      console.log(`${user.firstName} stopped typing in ${context}`);
    });

    // Error handling
    this.socket.on('error', ({ message }) => {
      toast.error(message);
    });
  }

  private handleReconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      toast.error('Unable to connect to real-time updates');
      return;
    }

    this.reconnectAttempts++;
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);

    setTimeout(() => {
      console.log(`Attempting to reconnect... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
      this.socket?.connect();
    }, delay);
  }

  private getNotificationIcon(type: string): string {
    switch (type) {
      case 'TASK_ASSIGNED':
        return '📋';
      case 'TASK_DUE':
        return '⏰';
      case 'MEETING_REMINDER':
        return '📅';
      case 'TEAM_INVITATION':
        return '👥';
      case 'DEPENDENCY_COMPLETED':
        return '✅';
      default:
        return '🔔';
    }
  }

  // Public methods for emitting events

  subscribeToTask(taskId: string) {
    this.socket?.emit('task:subscribe', taskId);
  }

  unsubscribeFromTask(taskId: string) {
    this.socket?.emit('task:unsubscribe', taskId);
  }

  subscribeToProject(projectId: string) {
    this.socket?.emit('project:subscribe', projectId);
  }

  unsubscribeFromProject(projectId: string) {
    this.socket?.emit('project:unsubscribe', projectId);
  }

  joinMeeting(meetingId: string) {
    this.socket?.emit('meeting:join', meetingId);
  }

  leaveMeeting(meetingId: string) {
    this.socket?.emit('meeting:leave', meetingId);
  }

  startTyping(room: string, context: string) {
    this.socket?.emit('typing:start', { room, context });
  }

  stopTyping(room: string, context: string) {
    this.socket?.emit('typing:stop', { room, context });
  }

  updatePresence(status: 'online' | 'away' | 'busy' | 'offline') {
    this.socket?.emit('presence:update', status);
  }

  markNotificationAsRead(notificationId: string) {
    this.socket?.emit('notifications:mark_read', notificationId);
  }

  markAllNotificationsAsRead() {
    this.socket?.emit('notifications:mark_all_read');
  }

  // Utility methods
  isConnected(): boolean {
    return this.socket?.connected || false;
  }

  getConnectionId(): string | undefined {
    return this.socket?.id;
  }
}

export const websocketService = new WebSocketService();
