import { apiClient } from './api';
import { LoginCredentials, RegisterData, AuthTokens, User } from '../store/slices/authSlice';

export const authService = {
  // Login user
  login: async (credentials: LoginCredentials) => {
    return apiClient.post<{ user: User; tokens: AuthTokens }>('/auth/login', credentials);
  },

  // Register user
  register: async (userData: RegisterData) => {
    return apiClient.post<{ user: User; tokens: AuthTokens }>('/auth/register', userData);
  },

  // Refresh access token
  refreshToken: async (refreshToken: string) => {
    return apiClient.post<{ tokens: AuthTokens }>('/auth/refresh', { refreshToken });
  },

  // Logout user
  logout: async (refreshToken: string) => {
    return apiClient.post('/auth/logout', { refreshToken });
  },

  // Get current user
  getCurrentUser: async () => {
    return apiClient.get<{ user: User }>('/auth/me');
  },

  // Change password
  changePassword: async (currentPassword: string, newPassword: string) => {
    return apiClient.post('/auth/change-password', {
      currentPassword,
      newPassword,
    });
  },
};
