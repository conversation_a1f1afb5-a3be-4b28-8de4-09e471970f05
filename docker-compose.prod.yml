version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: collabflow-postgres-prod
    environment:
      POSTGRES_DB: collabflow
      POSTGRES_USER: collabflow_user
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/database/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - collabflow-network
    restart: unless-stopped

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: collabflow-redis-prod
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - collabflow-network
    restart: unless-stopped
    command: redis-server --appendonly yes

  # Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: collabflow-backend-prod
    environment:
      NODE_ENV: production
      PORT: 3001
      DATABASE_URL: postgresql://collabflow_user:${POSTGRES_PASSWORD}@postgres:5432/collabflow
      REDIS_URL: redis://redis:6379
      JWT_SECRET: ${JWT_SECRET}
      OPENAI_API_KEY: ${OPENAI_API_KEY}
    ports:
      - "3001:3001"
    depends_on:
      - postgres
      - redis
    networks:
      - collabflow-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "node", "-e", "require('http').get('http://localhost:3001/api/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Frontend Application
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: collabflow-frontend-prod
    ports:
      - "80:80"
      - "443:443"
    depends_on:
      - backend
    networks:
      - collabflow-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost/"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx Load Balancer (Optional for scaling)
  nginx-lb:
    image: nginx:alpine
    container_name: collabflow-nginx-lb
    ports:
      - "8080:80"
    volumes:
      - ./docker/nginx-lb.conf:/etc/nginx/nginx.conf
    depends_on:
      - frontend
      - backend
    networks:
      - collabflow-network
    restart: unless-stopped
    profiles:
      - loadbalancer

volumes:
  postgres_data:
  redis_data:

networks:
  collabflow-network:
    driver: bridge
