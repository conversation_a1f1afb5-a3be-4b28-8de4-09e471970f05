-- Initialize CollabFlow database
-- This file is used by Docker to initialize the PostgreSQL database

-- Create database if it doesn't exist
-- Note: This is handled by Docker environment variables

-- Create extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- Create indexes for better performance (will be created by Prisma migrations)
-- These are just examples of what might be useful

-- Performance optimization settings
-- These can be adjusted based on your server specifications
-- ALTER SYSTEM SET shared_buffers = '256MB';
-- ALTER SYSTEM SET effective_cache_size = '1GB';
-- ALTER SYSTEM SET maintenance_work_mem = '64MB';
-- ALTER SYSTEM SET checkpoint_completion_target = 0.9;
-- ALTER SYSTEM SET wal_buffers = '16MB';
-- ALTER SYSTEM SET default_statistics_target = 100;

-- Note: The actual schema will be created by Prisma migrations
-- This file is mainly for database initialization and extensions
