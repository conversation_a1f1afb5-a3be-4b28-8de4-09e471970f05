import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface Meeting {
  id: string;
  title: string;
  description?: string;
  organizerId: string;
  participants: string[];
  startTime: string;
  endTime: string;
  location?: string;
  meetingUrl?: string;
  necessityScore: number;
  status: string;
  createdAt: string;
  updatedAt: string;
}

interface MeetingsState {
  meetings: Meeting[];
  currentMeeting: Meeting | null;
  isLoading: boolean;
  error: string | null;
  filters: {
    status?: string;
    date?: string;
    organizerId?: string;
  };
}

const initialState: MeetingsState = {
  meetings: [],
  currentMeeting: null,
  isLoading: false,
  error: null,
  filters: {},
};

const meetingsSlice = createSlice({
  name: 'meetings',
  initialState,
  reducers: {
    setMeetings: (state, action: PayloadAction<Meeting[]>) => {
      state.meetings = action.payload;
    },
    setCurrentMeeting: (state, action: PayloadAction<Meeting | null>) => {
      state.currentMeeting = action.payload;
    },
    addMeeting: (state, action: PayloadAction<Meeting>) => {
      state.meetings.push(action.payload);
    },
    updateMeeting: (state, action: PayloadAction<Meeting>) => {
      const index = state.meetings.findIndex(meeting => meeting.id === action.payload.id);
      if (index !== -1) {
        state.meetings[index] = action.payload;
      }
      if (state.currentMeeting?.id === action.payload.id) {
        state.currentMeeting = action.payload;
      }
    },
    removeMeeting: (state, action: PayloadAction<string>) => {
      state.meetings = state.meetings.filter(meeting => meeting.id !== action.payload);
      if (state.currentMeeting?.id === action.payload) {
        state.currentMeeting = null;
      }
    },
    setFilters: (state, action: PayloadAction<typeof initialState.filters>) => {
      state.filters = action.payload;
    },
    updateFilters: (state, action: PayloadAction<Partial<typeof initialState.filters>>) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    clearFilters: (state) => {
      state.filters = {};
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
  },
});

export const {
  setMeetings,
  setCurrentMeeting,
  addMeeting,
  updateMeeting,
  removeMeeting,
  setFilters,
  updateFilters,
  clearFilters,
  setLoading,
  setError,
  clearError,
} = meetingsSlice.actions;

export default meetingsSlice.reducer;
