import { Page, expect } from '@playwright/test';

export class TestHelpers {
  constructor(private page: Page) {}

  // Authentication helpers
  async login(email?: string, password?: string) {
    const testEmail = email || process.env.E2E_TEST_EMAIL || '<EMAIL>';
    const testPassword = password || process.env.E2E_TEST_PASSWORD || 'TestPassword123!';

    await this.page.goto('/login');
    await this.page.fill('[data-testid="email-input"]', testEmail);
    await this.page.fill('[data-testid="password-input"]', testPassword);
    await this.page.click('[data-testid="login-button"]');
    
    // Wait for successful login (redirect to dashboard)
    await this.page.waitForURL('/dashboard');
    await expect(this.page.locator('[data-testid="user-menu"]')).toBeVisible();
  }

  async logout() {
    await this.page.click('[data-testid="user-menu"]');
    await this.page.click('[data-testid="logout-button"]');
    await this.page.waitForURL('/login');
  }

  async register(userData: {
    email: string;
    password: string;
    firstName: string;
    lastName: string;
    skills?: string[];
  }) {
    await this.page.goto('/register');
    
    await this.page.fill('[data-testid="email-input"]', userData.email);
    await this.page.fill('[data-testid="password-input"]', userData.password);
    await this.page.fill('[data-testid="firstName-input"]', userData.firstName);
    await this.page.fill('[data-testid="lastName-input"]', userData.lastName);
    
    if (userData.skills && userData.skills.length > 0) {
      // Handle skills input (assuming it's an autocomplete or multi-select)
      for (const skill of userData.skills) {
        await this.page.fill('[data-testid="skills-input"]', skill);
        await this.page.press('[data-testid="skills-input"]', 'Enter');
      }
    }
    
    await this.page.click('[data-testid="register-button"]');
    
    // Wait for successful registration (redirect to dashboard)
    await this.page.waitForURL('/dashboard');
  }

  // Navigation helpers
  async navigateToTeams() {
    await this.page.click('[data-testid="nav-teams"]');
    await this.page.waitForURL('/teams');
  }

  async navigateToTasks() {
    await this.page.click('[data-testid="nav-tasks"]');
    await this.page.waitForURL('/tasks');
  }

  async navigateToMeetings() {
    await this.page.click('[data-testid="nav-meetings"]');
    await this.page.waitForURL('/meetings');
  }

  async navigateToDashboard() {
    await this.page.click('[data-testid="nav-dashboard"]');
    await this.page.waitForURL('/dashboard');
  }

  // Team helpers
  async createTeam(name: string, description?: string) {
    await this.navigateToTeams();
    
    // Click create team FAB
    await this.page.click('[data-testid="create-team-fab"]');
    
    // Fill form
    await this.page.fill('[data-testid="team-name-input"]', name);
    if (description) {
      await this.page.fill('[data-testid="team-description-input"]', description);
    }
    
    // Submit
    await this.page.click('[data-testid="create-team-button"]');
    
    // Wait for team to be created and dialog to close
    await expect(this.page.locator('[role="dialog"]')).not.toBeVisible();
    await expect(this.page.locator(`text=${name}`)).toBeVisible();
  }

  async deleteTeam(teamName: string) {
    await this.navigateToTeams();
    
    // Find team card and click more options
    const teamCard = this.page.locator(`[data-testid="team-card"]:has-text("${teamName}")`);
    await teamCard.locator('[data-testid="team-more-options"]').click();
    
    // Click delete
    await this.page.click('[data-testid="delete-team-option"]');
    
    // Confirm deletion if there's a confirmation dialog
    const confirmButton = this.page.locator('[data-testid="confirm-delete-button"]');
    if (await confirmButton.isVisible()) {
      await confirmButton.click();
    }
    
    // Wait for team to be removed
    await expect(teamCard).not.toBeVisible();
  }

  // Task helpers
  async createTask(taskData: {
    title: string;
    description?: string;
    projectId?: string;
    assigneeId?: string;
    priority?: string;
    estimatedHours?: number;
    dueDate?: string;
  }) {
    await this.navigateToTasks();
    
    // Click create task FAB
    await this.page.click('[data-testid="create-task-fab"]');
    
    // Fill form
    await this.page.fill('[data-testid="task-title-input"]', taskData.title);
    
    if (taskData.description) {
      await this.page.fill('[data-testid="task-description-input"]', taskData.description);
    }
    
    if (taskData.projectId) {
      await this.page.click('[data-testid="task-project-select"]');
      await this.page.click(`[data-value="${taskData.projectId}"]`);
    }
    
    if (taskData.priority) {
      await this.page.click('[data-testid="task-priority-select"]');
      await this.page.click(`[data-value="${taskData.priority}"]`);
    }
    
    if (taskData.estimatedHours) {
      await this.page.fill('[data-testid="task-estimated-hours-input"]', taskData.estimatedHours.toString());
    }
    
    if (taskData.dueDate) {
      await this.page.fill('[data-testid="task-due-date-input"]', taskData.dueDate);
    }
    
    // Submit
    await this.page.click('[data-testid="create-task-button"]');
    
    // Wait for task to be created
    await expect(this.page.locator('[role="dialog"]')).not.toBeVisible();
    await expect(this.page.locator(`text=${taskData.title}`)).toBeVisible();
  }

  async updateTaskStatus(taskTitle: string, newStatus: string) {
    await this.navigateToTasks();
    
    const taskCard = this.page.locator(`[data-testid="task-card"]:has-text("${taskTitle}")`);
    
    // Click the status update button based on current status
    const statusButton = taskCard.locator('[data-testid="task-status-button"]');
    await statusButton.click();
    
    // Wait for status to update
    await this.page.waitForTimeout(1000);
  }

  // Meeting helpers
  async createMeeting(meetingData: {
    title: string;
    description?: string;
    participants: string[];
    startTime: string;
    endTime: string;
    location?: string;
  }) {
    await this.navigateToMeetings();
    
    // Click create meeting FAB
    await this.page.click('[data-testid="create-meeting-fab"]');
    
    // Fill form
    await this.page.fill('[data-testid="meeting-title-input"]', meetingData.title);
    
    if (meetingData.description) {
      await this.page.fill('[data-testid="meeting-description-input"]', meetingData.description);
    }
    
    // Add participants
    for (const participant of meetingData.participants) {
      await this.page.fill('[data-testid="meeting-participants-input"]', participant);
      await this.page.press('[data-testid="meeting-participants-input"]', 'Enter');
    }
    
    // Set times
    await this.page.fill('[data-testid="meeting-start-time-input"]', meetingData.startTime);
    await this.page.fill('[data-testid="meeting-end-time-input"]', meetingData.endTime);
    
    if (meetingData.location) {
      await this.page.fill('[data-testid="meeting-location-input"]', meetingData.location);
    }
    
    // Submit
    await this.page.click('[data-testid="create-meeting-button"]');
    
    // Wait for meeting to be created
    await expect(this.page.locator('[role="dialog"]')).not.toBeVisible();
    await expect(this.page.locator(`text=${meetingData.title}`)).toBeVisible();
  }

  // Notification helpers
  async checkNotifications() {
    await this.page.click('[data-testid="notifications-button"]');
    return this.page.locator('[data-testid="notification-item"]');
  }

  async markNotificationAsRead(notificationText: string) {
    await this.page.click('[data-testid="notifications-button"]');
    
    const notification = this.page.locator(`[data-testid="notification-item"]:has-text("${notificationText}")`);
    await notification.locator('[data-testid="mark-read-button"]').click();
    
    await expect(notification).not.toHaveClass(/unread/);
  }

  // Real-time collaboration helpers
  async waitForRealTimeUpdate(selector: string, expectedText: string, timeout: number = 5000) {
    await expect(this.page.locator(selector)).toContainText(expectedText, { timeout });
  }

  async simulateTyping(selector: string, text: string) {
    const input = this.page.locator(selector);
    await input.focus();
    
    // Type character by character to trigger typing indicators
    for (const char of text) {
      await input.type(char, { delay: 100 });
    }
  }

  // Utility methods
  async waitForToast(message: string) {
    await expect(this.page.locator(`[data-testid="toast"]:has-text("${message}")`)).toBeVisible();
  }

  async dismissToast() {
    const toast = this.page.locator('[data-testid="toast"]');
    if (await toast.isVisible()) {
      await toast.locator('[data-testid="toast-close"]').click();
    }
  }

  async takeScreenshot(name: string) {
    await this.page.screenshot({ path: `test-results/screenshots/${name}.png` });
  }

  async waitForLoadingToFinish() {
    await expect(this.page.locator('[data-testid="loading-spinner"]')).not.toBeVisible();
  }

  // Generate test data
  generateRandomEmail(): string {
    const timestamp = Date.now();
    return `test-${timestamp}@example.com`;
  }

  generateRandomString(length: number = 8): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }

  futureDateTime(hoursFromNow: number = 24): string {
    const future = new Date();
    future.setHours(future.getHours() + hoursFromNow);
    return future.toISOString().slice(0, 16); // Format for datetime-local input
  }
}
