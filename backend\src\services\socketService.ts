import { Server as SocketIOServer, Socket } from 'socket.io';
import jwt from 'jsonwebtoken';
import { logger } from '../config/logger';
import { prisma } from '../config/database';

interface AuthenticatedSocket extends Socket {
  userId?: string;
  user?: {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
  };
}

export const initializeSocketHandlers = (io: SocketIOServer): void => {
  // Authentication middleware for socket connections
  io.use(async (socket: AuthenticatedSocket, next) => {
    try {
      const token = socket.handshake.auth.token;
      
      if (!token) {
        return next(new Error('Authentication error: No token provided'));
      }

      const decoded = jwt.verify(token, process.env.JWT_SECRET!) as any;
      
      if (!decoded || !decoded.userId) {
        return next(new Error('Authentication error: Invalid token'));
      }

      // Get user from database
      const user = await prisma.user.findUnique({
        where: { id: decoded.userId },
        select: {
          id: true,
          email: true,
          firstName: true,
          lastName: true,
        },
      });

      if (!user) {
        return next(new Error('Authentication error: User not found'));
      }

      socket.userId = user.id;
      socket.user = user;
      next();
    } catch (error) {
      logger.error('Socket authentication error:', error);
      next(new Error('Authentication error'));
    }
  });

  io.on('connection', (socket: AuthenticatedSocket) => {
    logger.info(`User ${socket.user?.email} connected via WebSocket`);

    // Join user to their personal room
    socket.join(`user:${socket.userId}`);

    // Handle joining team rooms
    socket.on('join-team', async (teamId: string) => {
      try {
        // Verify user is a member of the team
        const teamMember = await prisma.teamMember.findFirst({
          where: {
            teamId,
            userId: socket.userId!,
          },
        });

        if (teamMember) {
          socket.join(`team:${teamId}`);
          logger.info(`User ${socket.user?.email} joined team room: ${teamId}`);
        } else {
          socket.emit('error', { message: 'Not authorized to join this team' });
        }
      } catch (error) {
        logger.error('Error joining team room:', error);
        socket.emit('error', { message: 'Failed to join team room' });
      }
    });

    // Handle leaving team rooms
    socket.on('leave-team', (teamId: string) => {
      socket.leave(`team:${teamId}`);
      logger.info(`User ${socket.user?.email} left team room: ${teamId}`);
    });

    // Handle joining project rooms
    socket.on('join-project', async (projectId: string) => {
      try {
        // Verify user has access to the project
        const project = await prisma.project.findUnique({
          where: { id: projectId },
          include: {
            team: {
              include: {
                members: {
                  where: { userId: socket.userId! },
                },
              },
            },
          },
        });

        if (project && project.team.members.length > 0) {
          socket.join(`project:${projectId}`);
          logger.info(`User ${socket.user?.email} joined project room: ${projectId}`);
        } else {
          socket.emit('error', { message: 'Not authorized to join this project' });
        }
      } catch (error) {
        logger.error('Error joining project room:', error);
        socket.emit('error', { message: 'Failed to join project room' });
      }
    });

    // Handle leaving project rooms
    socket.on('leave-project', (projectId: string) => {
      socket.leave(`project:${projectId}`);
      logger.info(`User ${socket.user?.email} left project room: ${projectId}`);
    });

    // Handle user status updates
    socket.on('status-update', (status: string) => {
      // Broadcast status update to all user's teams
      socket.broadcast.emit('user-status-changed', {
        userId: socket.userId,
        status,
        timestamp: new Date(),
      });
    });

    // Handle disconnection
    socket.on('disconnect', () => {
      logger.info(`User ${socket.user?.email} disconnected from WebSocket`);
    });
  });
};

// Helper functions to emit events to specific rooms
export const emitToUser = (io: SocketIOServer, userId: string, event: string, data: any): void => {
  io.to(`user:${userId}`).emit(event, data);
};

export const emitToTeam = (io: SocketIOServer, teamId: string, event: string, data: any): void => {
  io.to(`team:${teamId}`).emit(event, data);
};

export const emitToProject = (io: SocketIOServer, projectId: string, event: string, data: any): void => {
  io.to(`project:${projectId}`).emit(event, data);
};
