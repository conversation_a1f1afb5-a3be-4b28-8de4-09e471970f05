import { prisma } from '../config/database';
import { logger } from '../config/logger';
import { CustomError } from '../middleware/errorHandler';
import { aiService } from './aiService';
import moment from 'moment-timezone';

export interface SchedulingOptions {
  duration: number; // minutes
  participants: string[];
  preferredTimes?: Array<{
    start: string; // HH:MM
    end: string;   // HH:MM
  }>;
  timezone?: string;
  bufferTime?: number; // minutes
  excludeDates?: Date[];
}

export interface TimeSlot {
  start: Date;
  end: Date;
  score: number; // 0-100, higher is better
  conflicts: string[];
}

export interface SchedulingSuggestion {
  timeSlots: TimeSlot[];
  analysis: {
    totalParticipants: number;
    availableParticipants: number;
    conflictingMeetings: number;
    optimalScore: number;
  };
  recommendations: string[];
}

class SchedulingService {
  async findOptimalMeetingTimes(
    options: SchedulingOptions,
    lookAheadDays: number = 14
  ): Promise<SchedulingSuggestion> {
    try {
      const {
        duration,
        participants,
        preferredTimes = [{ start: '09:00', end: '17:00' }],
        timezone = 'UTC',
        bufferTime = 15,
        excludeDates = [],
      } = options;

      logger.info(`Finding optimal meeting times for ${participants.length} participants`);

      // Get participant availability and existing meetings
      const [participantAvailability, existingMeetings] = await Promise.all([
        this.getParticipantAvailability(participants),
        this.getExistingMeetings(participants, lookAheadDays),
      ]);

      // Generate potential time slots
      const potentialSlots = this.generateTimeSlots(
        duration,
        preferredTimes,
        timezone,
        lookAheadDays,
        excludeDates
      );

      // Score each time slot
      const scoredSlots = await this.scoreTimeSlots(
        potentialSlots,
        participants,
        participantAvailability,
        existingMeetings,
        bufferTime
      );

      // Sort by score and take top suggestions
      const topSlots = scoredSlots
        .sort((a, b) => b.score - a.score)
        .slice(0, 10);

      // Generate analysis and recommendations
      const analysis = this.generateAnalysis(topSlots, participants, existingMeetings);
      const recommendations = this.generateRecommendations(analysis, options);

      return {
        timeSlots: topSlots,
        analysis,
        recommendations,
      };
    } catch (error) {
      logger.error('Error finding optimal meeting times:', error);
      throw new CustomError('Failed to find optimal meeting times', 500);
    }
  }

  async scheduleRecurringMeeting(
    meetingData: {
      title: string;
      description?: string;
      organizerId: string;
      participants: string[];
      duration: number;
      recurrence: {
        frequency: 'daily' | 'weekly' | 'monthly';
        interval: number; // every N days/weeks/months
        daysOfWeek?: number[]; // 0=Sunday, 1=Monday, etc.
        endDate?: Date;
        occurrences?: number;
      };
    },
    options: SchedulingOptions
  ): Promise<string[]> {
    try {
      const { recurrence } = meetingData;
      const meetingIds: string[] = [];

      // Calculate occurrence dates
      const occurrenceDates = this.calculateRecurrenceDates(recurrence);

      // Find optimal time for the first occurrence
      const suggestion = await this.findOptimalMeetingTimes(options, 7);
      
      if (suggestion.timeSlots.length === 0) {
        throw new CustomError('No suitable time slots found for recurring meeting', 400);
      }

      const optimalSlot = suggestion.timeSlots[0];
      const startTime = moment(optimalSlot.start);

      // Create meetings for each occurrence
      for (const occurrenceDate of occurrenceDates) {
        const meetingStart = moment(occurrenceDate)
          .hour(startTime.hour())
          .minute(startTime.minute())
          .toDate();

        const meetingEnd = moment(meetingStart)
          .add(meetingData.duration, 'minutes')
          .toDate();

        // Check for conflicts before creating
        const conflicts = await this.checkTimeSlotConflicts(
          meetingStart,
          meetingEnd,
          meetingData.participants
        );

        if (conflicts.length === 0) {
          const meeting = await prisma.meeting.create({
            data: {
              title: meetingData.title,
              description: meetingData.description,
              organizerId: meetingData.organizerId,
              startTime: meetingStart,
              endTime: meetingEnd,
              participants: {
                create: meetingData.participants.map(userId => ({
                  userId,
                })),
              },
            },
          });

          meetingIds.push(meeting.id);
        } else {
          logger.warn(`Skipping recurring meeting occurrence due to conflicts: ${meetingStart}`);
        }
      }

      logger.info(`Created ${meetingIds.length} recurring meetings`);
      return meetingIds;
    } catch (error) {
      logger.error('Error scheduling recurring meeting:', error);
      throw error;
    }
  }

  async optimizeExistingSchedule(
    teamId: string,
    optimizationPeriod: { start: Date; end: Date }
  ): Promise<{
    currentEfficiency: number;
    optimizedSchedule: Array<{
      meetingId: string;
      currentTime: { start: Date; end: Date };
      suggestedTime: { start: Date; end: Date };
      improvement: number;
    }>;
    totalImprovement: number;
  }> {
    try {
      // Get all meetings in the period
      const meetings = await prisma.meeting.findMany({
        where: {
          startTime: {
            gte: optimizationPeriod.start,
            lte: optimizationPeriod.end,
          },
          participants: {
            some: {
              user: {
                teamMemberships: {
                  some: {
                    teamId,
                  },
                },
              },
            },
          },
        },
        include: {
          participants: {
            include: {
              user: true,
            },
          },
        },
      });

      let totalCurrentScore = 0;
      let totalOptimizedScore = 0;
      const optimizedSchedule = [];

      // Analyze each meeting for optimization potential
      for (const meeting of meetings) {
        const participants = meeting.participants.map(p => p.userId);
        const duration = moment(meeting.endTime).diff(moment(meeting.startTime), 'minutes');

        // Get current meeting score
        const currentScore = await this.scoreSingleTimeSlot(
          meeting.startTime,
          meeting.endTime,
          participants
        );

        // Find better time slots
        const suggestion = await this.findOptimalMeetingTimes({
          duration,
          participants,
          timezone: 'UTC', // Should be from team settings
        }, 7);

        if (suggestion.timeSlots.length > 0) {
          const bestSlot = suggestion.timeSlots[0];
          
          if (bestSlot.score > currentScore + 10) { // Only suggest if significant improvement
            optimizedSchedule.push({
              meetingId: meeting.id,
              currentTime: {
                start: meeting.startTime,
                end: meeting.endTime,
              },
              suggestedTime: {
                start: bestSlot.start,
                end: bestSlot.end,
              },
              improvement: bestSlot.score - currentScore,
            });

            totalOptimizedScore += bestSlot.score;
          } else {
            totalOptimizedScore += currentScore;
          }
        } else {
          totalOptimizedScore += currentScore;
        }

        totalCurrentScore += currentScore;
      }

      const currentEfficiency = meetings.length > 0 ? totalCurrentScore / meetings.length : 0;
      const totalImprovement = totalOptimizedScore - totalCurrentScore;

      return {
        currentEfficiency,
        optimizedSchedule,
        totalImprovement,
      };
    } catch (error) {
      logger.error('Error optimizing schedule:', error);
      throw new CustomError('Failed to optimize schedule', 500);
    }
  }

  private async getParticipantAvailability(participantIds: string[]) {
    return await prisma.userAvailability.findMany({
      where: {
        userId: {
          in: participantIds,
        },
      },
      include: {
        user: {
          select: {
            id: true,
            timezone: true,
          },
        },
      },
    });
  }

  private async getExistingMeetings(participantIds: string[], lookAheadDays: number) {
    const endDate = moment().add(lookAheadDays, 'days').toDate();

    return await prisma.meeting.findMany({
      where: {
        startTime: {
          gte: new Date(),
          lte: endDate,
        },
        participants: {
          some: {
            userId: {
              in: participantIds,
            },
          },
        },
      },
      include: {
        participants: true,
      },
    });
  }

  private generateTimeSlots(
    duration: number,
    preferredTimes: Array<{ start: string; end: string }>,
    timezone: string,
    lookAheadDays: number,
    excludeDates: Date[]
  ): Array<{ start: Date; end: Date }> {
    const slots: Array<{ start: Date; end: Date }> = [];
    const excludeSet = new Set(excludeDates.map(d => d.toDateString()));

    for (let day = 0; day < lookAheadDays; day++) {
      const currentDate = moment().add(day, 'days');
      
      // Skip weekends and excluded dates
      if (currentDate.day() === 0 || currentDate.day() === 6) continue;
      if (excludeSet.has(currentDate.toDate().toDateString())) continue;

      for (const timeRange of preferredTimes) {
        const startTime = moment.tz(
          `${currentDate.format('YYYY-MM-DD')} ${timeRange.start}`,
          'YYYY-MM-DD HH:mm',
          timezone
        );

        const endTime = moment.tz(
          `${currentDate.format('YYYY-MM-DD')} ${timeRange.end}`,
          'YYYY-MM-DD HH:mm',
          timezone
        );

        // Generate 30-minute intervals within the time range
        let slotStart = startTime.clone();
        while (slotStart.clone().add(duration, 'minutes').isSameOrBefore(endTime)) {
          const slotEnd = slotStart.clone().add(duration, 'minutes');
          
          slots.push({
            start: slotStart.toDate(),
            end: slotEnd.toDate(),
          });

          slotStart.add(30, 'minutes'); // 30-minute intervals
        }
      }
    }

    return slots;
  }

  private async scoreTimeSlots(
    slots: Array<{ start: Date; end: Date }>,
    participants: string[],
    availability: any[],
    existingMeetings: any[],
    bufferTime: number
  ): Promise<TimeSlot[]> {
    const scoredSlots: TimeSlot[] = [];

    for (const slot of slots) {
      const score = await this.scoreSingleTimeSlot(slot.start, slot.end, participants);
      const conflicts = await this.checkTimeSlotConflicts(slot.start, slot.end, participants);

      scoredSlots.push({
        start: slot.start,
        end: slot.end,
        score,
        conflicts,
      });
    }

    return scoredSlots;
  }

  private async scoreSingleTimeSlot(
    start: Date,
    end: Date,
    participants: string[]
  ): Promise<number> {
    let score = 100; // Start with perfect score

    // Check for conflicts
    const conflicts = await this.checkTimeSlotConflicts(start, end, participants);
    score -= conflicts.length * 20; // Penalize conflicts heavily

    // Prefer mid-morning and early afternoon
    const hour = moment(start).hour();
    if (hour >= 10 && hour <= 11) score += 10; // Mid-morning bonus
    if (hour >= 14 && hour <= 15) score += 5;  // Early afternoon bonus
    if (hour < 9 || hour > 17) score -= 15;    // Outside business hours penalty

    // Prefer Tuesday-Thursday
    const dayOfWeek = moment(start).day();
    if (dayOfWeek >= 2 && dayOfWeek <= 4) score += 5;
    if (dayOfWeek === 1 || dayOfWeek === 5) score -= 5; // Monday/Friday penalty

    return Math.max(0, Math.min(100, score));
  }

  private async checkTimeSlotConflicts(
    start: Date,
    end: Date,
    participants: string[]
  ): Promise<string[]> {
    const conflicts = await prisma.meeting.findMany({
      where: {
        AND: [
          {
            startTime: {
              lt: end,
            },
          },
          {
            endTime: {
              gt: start,
            },
          },
          {
            participants: {
              some: {
                userId: {
                  in: participants,
                },
              },
            },
          },
        ],
      },
      select: {
        id: true,
        title: true,
      },
    });

    return conflicts.map(c => c.title);
  }

  private generateAnalysis(
    timeSlots: TimeSlot[],
    participants: string[],
    existingMeetings: any[]
  ) {
    const optimalScore = timeSlots.length > 0 ? timeSlots[0].score : 0;
    const conflictingMeetings = existingMeetings.length;

    return {
      totalParticipants: participants.length,
      availableParticipants: participants.length, // Simplified
      conflictingMeetings,
      optimalScore,
    };
  }

  private generateRecommendations(
    analysis: any,
    options: SchedulingOptions
  ): string[] {
    const recommendations: string[] = [];

    if (analysis.optimalScore < 50) {
      recommendations.push('Consider reducing the number of participants or adjusting the time requirements');
    }

    if (analysis.conflictingMeetings > 5) {
      recommendations.push('High number of existing meetings. Consider consolidating or rescheduling some meetings');
    }

    if (options.participants.length > 8) {
      recommendations.push('Large meeting size detected. Consider if all participants are necessary');
    }

    if (options.duration > 60) {
      recommendations.push('Long meeting duration. Consider breaking into smaller sessions');
    }

    return recommendations;
  }

  private calculateRecurrenceDates(recurrence: any): Date[] {
    const dates: Date[] = [];
    const startDate = moment();
    let currentDate = startDate.clone();

    const maxOccurrences = recurrence.occurrences || 52; // Default to 1 year
    const endDate = recurrence.endDate ? moment(recurrence.endDate) : startDate.clone().add(1, 'year');

    for (let i = 0; i < maxOccurrences && currentDate.isBefore(endDate); i++) {
      dates.push(currentDate.toDate());

      switch (recurrence.frequency) {
        case 'daily':
          currentDate.add(recurrence.interval, 'days');
          break;
        case 'weekly':
          currentDate.add(recurrence.interval, 'weeks');
          break;
        case 'monthly':
          currentDate.add(recurrence.interval, 'months');
          break;
      }
    }

    return dates;
  }
}

export const schedulingService = new SchedulingService();
