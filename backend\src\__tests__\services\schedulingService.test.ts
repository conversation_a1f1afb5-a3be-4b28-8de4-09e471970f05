import { jest } from '@jest/globals';
import { schedulingService } from '../../services/schedulingService';
import { prisma } from '../../config/database';

const mockPrisma = prisma as jest.Mocked<typeof prisma>;

describe('SchedulingService', () => {
  describe('findOptimalMeetingTimes', () => {
    it('should find optimal meeting times based on availability', async () => {
      // Arrange
      const request = {
        duration: 60,
        participants: ['user1', 'user2', 'user3'],
        timezone: 'UTC',
        bufferTime: 15,
        lookAheadDays: 7,
      };

      const mockAvailability = [
        {
          userId: 'user1',
          dayOfWeek: 1, // Monday
          startTime: '09:00',
          endTime: '17:00',
          timezone: 'UTC',
        },
        {
          userId: 'user2',
          dayOfWeek: 1,
          startTime: '10:00',
          endTime: '18:00',
          timezone: 'UTC',
        },
        {
          userId: 'user3',
          dayOfWeek: 1,
          startTime: '08:00',
          endTime: '16:00',
          timezone: 'UTC',
        },
      ];

      const mockMeetings = [
        {
          startTime: new Date('2024-01-15T14:00:00Z'),
          endTime: new Date('2024-01-15T15:00:00Z'),
          participants: [{ userId: 'user1' }],
        },
      ];

      mockPrisma.userAvailability.findMany.mockResolvedValue(mockAvailability as any);
      mockPrisma.meeting.findMany.mockResolvedValue(mockMeetings as any);

      // Act
      const result = await schedulingService.findOptimalMeetingTimes(request, 3);

      // Assert
      expect(result.timeSlots).toBeDefined();
      expect(result.timeSlots.length).toBeGreaterThan(0);
      expect(result.timeSlots[0]).toHaveProperty('start');
      expect(result.timeSlots[0]).toHaveProperty('end');
      expect(result.timeSlots[0]).toHaveProperty('score');
      expect(result.timeSlots[0]).toHaveProperty('conflicts');
      expect(result.analysis.totalParticipants).toBe(3);
    });

    it('should handle participants with no availability', async () => {
      // Arrange
      const request = {
        duration: 60,
        participants: ['user1', 'user2'],
        timezone: 'UTC',
      };

      mockPrisma.userAvailability.findMany.mockResolvedValue([]);
      mockPrisma.meeting.findMany.mockResolvedValue([]);

      // Act
      const result = await schedulingService.findOptimalMeetingTimes(request, 3);

      // Assert
      expect(result.timeSlots).toBeDefined();
      expect(result.analysis.totalParticipants).toBe(2);
      expect(result.analysis.availableParticipants).toBe(0);
    });

    it('should respect buffer time between meetings', async () => {
      // Arrange
      const request = {
        duration: 60,
        participants: ['user1'],
        timezone: 'UTC',
        bufferTime: 30,
      };

      const mockAvailability = [
        {
          userId: 'user1',
          dayOfWeek: 1,
          startTime: '09:00',
          endTime: '17:00',
          timezone: 'UTC',
        },
      ];

      const mockMeetings = [
        {
          startTime: new Date('2024-01-15T10:00:00Z'),
          endTime: new Date('2024-01-15T11:00:00Z'),
          participants: [{ userId: 'user1' }],
        },
      ];

      mockPrisma.userAvailability.findMany.mockResolvedValue(mockAvailability as any);
      mockPrisma.meeting.findMany.mockResolvedValue(mockMeetings as any);

      // Act
      const result = await schedulingService.findOptimalMeetingTimes(request, 5);

      // Assert
      // Should not suggest times between 9:30-11:30 (30min buffer before and after existing meeting)
      const conflictingSlots = result.timeSlots.filter(slot => {
        const slotStart = new Date(slot.start);
        const conflictStart = new Date('2024-01-15T09:30:00Z');
        const conflictEnd = new Date('2024-01-15T11:30:00Z');
        return slotStart >= conflictStart && slotStart < conflictEnd;
      });

      expect(conflictingSlots.length).toBe(0);
    });

    it('should exclude specified dates', async () => {
      // Arrange
      const excludeDate = '2024-01-15';
      const request = {
        duration: 60,
        participants: ['user1'],
        timezone: 'UTC',
        excludeDates: [excludeDate],
      };

      const mockAvailability = [
        {
          userId: 'user1',
          dayOfWeek: 1, // Monday
          startTime: '09:00',
          endTime: '17:00',
          timezone: 'UTC',
        },
      ];

      mockPrisma.userAvailability.findMany.mockResolvedValue(mockAvailability as any);
      mockPrisma.meeting.findMany.mockResolvedValue([]);

      // Act
      const result = await schedulingService.findOptimalMeetingTimes(request, 5);

      // Assert
      const excludedDateSlots = result.timeSlots.filter(slot => {
        const slotDate = new Date(slot.start).toISOString().split('T')[0];
        return slotDate === excludeDate;
      });

      expect(excludedDateSlots.length).toBe(0);
    });
  });

  describe('checkAvailability', () => {
    it('should return availability status for participants', async () => {
      // Arrange
      const participants = ['user1', 'user2'];
      const startTime = new Date('2024-01-15T14:00:00Z');
      const endTime = new Date('2024-01-15T15:00:00Z');

      const mockMeetings = [
        {
          startTime: new Date('2024-01-15T14:30:00Z'),
          endTime: new Date('2024-01-15T15:30:00Z'),
          participants: [{ userId: 'user1' }],
        },
      ];

      mockPrisma.meeting.findMany.mockResolvedValue(mockMeetings as any);

      // Act
      const result = await schedulingService.checkAvailability(participants, startTime, endTime);

      // Assert
      expect(result).toHaveProperty('user1');
      expect(result).toHaveProperty('user2');
      expect(result.user1.available).toBe(false);
      expect(result.user1.conflicts.length).toBe(1);
      expect(result.user2.available).toBe(true);
      expect(result.user2.conflicts.length).toBe(0);
    });

    it('should handle participants with no conflicts', async () => {
      // Arrange
      const participants = ['user1', 'user2'];
      const startTime = new Date('2024-01-15T14:00:00Z');
      const endTime = new Date('2024-01-15T15:00:00Z');

      mockPrisma.meeting.findMany.mockResolvedValue([]);

      // Act
      const result = await schedulingService.checkAvailability(participants, startTime, endTime);

      // Assert
      expect(result.user1.available).toBe(true);
      expect(result.user2.available).toBe(true);
      expect(result.user1.conflicts.length).toBe(0);
      expect(result.user2.conflicts.length).toBe(0);
    });
  });

  describe('getRecurringMeetingDates', () => {
    it('should generate daily recurring dates', () => {
      // Arrange
      const startDate = new Date('2024-01-15');
      const recurrence = {
        frequency: 'daily' as const,
        interval: 1,
        occurrences: 5,
      };

      // Act
      const result = schedulingService.getRecurringMeetingDates(startDate, recurrence);

      // Assert
      expect(result.length).toBe(5);
      expect(result[0]).toEqual(startDate);
      expect(result[1]).toEqual(new Date('2024-01-16'));
      expect(result[4]).toEqual(new Date('2024-01-19'));
    });

    it('should generate weekly recurring dates', () => {
      // Arrange
      const startDate = new Date('2024-01-15'); // Monday
      const recurrence = {
        frequency: 'weekly' as const,
        interval: 1,
        occurrences: 3,
      };

      // Act
      const result = schedulingService.getRecurringMeetingDates(startDate, recurrence);

      // Assert
      expect(result.length).toBe(3);
      expect(result[0]).toEqual(startDate);
      expect(result[1]).toEqual(new Date('2024-01-22'));
      expect(result[2]).toEqual(new Date('2024-01-29'));
    });

    it('should generate monthly recurring dates', () => {
      // Arrange
      const startDate = new Date('2024-01-15');
      const recurrence = {
        frequency: 'monthly' as const,
        interval: 1,
        occurrences: 3,
      };

      // Act
      const result = schedulingService.getRecurringMeetingDates(startDate, recurrence);

      // Assert
      expect(result.length).toBe(3);
      expect(result[0]).toEqual(startDate);
      expect(result[1]).toEqual(new Date('2024-02-15'));
      expect(result[2]).toEqual(new Date('2024-03-15'));
    });

    it('should respect end date over occurrences', () => {
      // Arrange
      const startDate = new Date('2024-01-15');
      const recurrence = {
        frequency: 'daily' as const,
        interval: 1,
        occurrences: 10,
        endDate: new Date('2024-01-18'),
      };

      // Act
      const result = schedulingService.getRecurringMeetingDates(startDate, recurrence);

      // Assert
      expect(result.length).toBe(4); // 15th, 16th, 17th, 18th
      expect(result[result.length - 1]).toEqual(new Date('2024-01-18'));
    });

    it('should filter by days of week for weekly recurrence', () => {
      // Arrange
      const startDate = new Date('2024-01-15'); // Monday
      const recurrence = {
        frequency: 'weekly' as const,
        interval: 1,
        daysOfWeek: [1, 3, 5], // Monday, Wednesday, Friday
        occurrences: 6,
      };

      // Act
      const result = schedulingService.getRecurringMeetingDates(startDate, recurrence);

      // Assert
      expect(result.length).toBe(6);
      // Should include Mon 15th, Wed 17th, Fri 19th, Mon 22nd, Wed 24th, Fri 26th
      expect(result[0]).toEqual(new Date('2024-01-15')); // Monday
      expect(result[1]).toEqual(new Date('2024-01-17')); // Wednesday
      expect(result[2]).toEqual(new Date('2024-01-19')); // Friday
    });
  });

  describe('optimizeSchedule', () => {
    it('should suggest schedule optimizations', async () => {
      // Arrange
      const teamId = 'team1';
      const startDate = new Date('2024-01-15');
      const endDate = new Date('2024-01-22');

      const mockMeetings = [
        {
          id: 'meeting1',
          title: 'Daily Standup',
          startTime: new Date('2024-01-15T09:00:00Z'),
          endTime: new Date('2024-01-15T09:30:00Z'),
          participants: [{ userId: 'user1' }, { userId: 'user2' }],
        },
        {
          id: 'meeting2',
          title: 'Sprint Planning',
          startTime: new Date('2024-01-15T09:15:00Z'),
          endTime: new Date('2024-01-15T11:15:00Z'),
          participants: [{ userId: 'user1' }, { userId: 'user3' }],
        },
      ];

      mockPrisma.meeting.findMany.mockResolvedValue(mockMeetings as any);

      // Act
      const result = await schedulingService.optimizeSchedule(teamId, startDate, endDate);

      // Assert
      expect(result.currentEfficiency).toBeGreaterThanOrEqual(0);
      expect(result.currentEfficiency).toBeLessThanOrEqual(100);
      expect(Array.isArray(result.optimizations)).toBe(true);
      expect(result.totalImprovement).toBeGreaterThanOrEqual(0);
    });

    it('should identify overlapping meetings as optimization opportunities', async () => {
      // Arrange
      const teamId = 'team1';
      const startDate = new Date('2024-01-15');
      const endDate = new Date('2024-01-22');

      const mockMeetings = [
        {
          id: 'meeting1',
          title: 'Meeting A',
          startTime: new Date('2024-01-15T14:00:00Z'),
          endTime: new Date('2024-01-15T15:00:00Z'),
          participants: [{ userId: 'user1' }],
        },
        {
          id: 'meeting2',
          title: 'Meeting B',
          startTime: new Date('2024-01-15T14:30:00Z'),
          endTime: new Date('2024-01-15T15:30:00Z'),
          participants: [{ userId: 'user1' }],
        },
      ];

      mockPrisma.meeting.findMany.mockResolvedValue(mockMeetings as any);

      // Act
      const result = await schedulingService.optimizeSchedule(teamId, startDate, endDate);

      // Assert
      expect(result.optimizations.length).toBeGreaterThan(0);
      expect(result.optimizations.some(opt => 
        opt.type === 'reschedule' && opt.reason.includes('overlap')
      )).toBe(true);
    });

    it('should handle empty meeting schedule', async () => {
      // Arrange
      const teamId = 'team1';
      const startDate = new Date('2024-01-15');
      const endDate = new Date('2024-01-22');

      mockPrisma.meeting.findMany.mockResolvedValue([]);

      // Act
      const result = await schedulingService.optimizeSchedule(teamId, startDate, endDate);

      // Assert
      expect(result.currentEfficiency).toBe(100);
      expect(result.optimizations).toEqual([]);
      expect(result.totalImprovement).toBe(0);
    });
  });
});
