import { Router, Request, Response } from 'express';
import { validationResult, query, param } from 'express-validator';
import { notificationService } from '../services/notificationService';
import { websocketService } from '../services/websocketService';
import { asyncHandler, CustomError } from '../middleware/errorHandler';
import { AuthenticatedRequest } from '../middleware/auth';
import { 
  commonValidations, 
  paginationValidation 
} from '../utils/validation';
import { createSuccessResponse } from '../utils/helpers';

const router = Router();

// Helper function to handle validation errors
const handleValidationErrors = (req: Request, res: Response, next: any) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errors.array(),
    });
  }
  next();
};

// Get user notifications
router.get('/',
  paginationValidation,
  handleValidationErrors,
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const { page, limit } = req.query;
    const userId = req.user!.id;

    const result = await notificationService.getUserNotifications(
      userId,
      page ? parseInt(page as string) : 1,
      limit ? parseInt(limit as string) : 20
    );

    res.json(createSuccessResponse(result, 'Notifications retrieved successfully'));
  })
);

// Get unread notification count
router.get('/unread-count',
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const userId = req.user!.id;
    const count = await notificationService.getUnreadCount(userId);

    res.json(createSuccessResponse({ count }, 'Unread count retrieved successfully'));
  })
);

// Mark notification as read
router.patch('/:id/read',
  commonValidations.id,
  handleValidationErrors,
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const { id } = req.params;
    const userId = req.user!.id;

    await notificationService.markAsRead(id, userId);

    // Emit real-time update
    websocketService.emitToUser(userId, 'notification:marked_read', { notificationId: id });

    res.json(createSuccessResponse(null, 'Notification marked as read'));
  })
);

// Mark all notifications as read
router.patch('/mark-all-read',
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const userId = req.user!.id;

    const result = await notificationService.markAllAsRead(userId);

    // Emit real-time update
    websocketService.emitToUser(userId, 'notifications:all_marked_read', { 
      markedCount: result.count 
    });

    res.json(createSuccessResponse(
      { markedCount: result.count }, 
      'All notifications marked as read'
    ));
  })
);

// Delete notification
router.delete('/:id',
  commonValidations.id,
  handleValidationErrors,
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const { id } = req.params;
    const userId = req.user!.id;

    await notificationService.deleteNotification(id, userId);

    // Emit real-time update
    websocketService.emitToUser(userId, 'notification:deleted', { notificationId: id });

    res.json(createSuccessResponse(null, 'Notification deleted successfully'));
  })
);

// Test notification endpoint (development only)
router.post('/test',
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    if (process.env.NODE_ENV === 'production') {
      throw new CustomError('Test endpoint not available in production', 403);
    }

    const { type, title, message, data } = req.body;
    const userId = req.user!.id;

    const notification = await notificationService.createNotification({
      userId,
      type: type || 'GENERAL',
      title: title || 'Test Notification',
      message: message || 'This is a test notification',
      data,
    });

    // Emit real-time notification
    websocketService.broadcastNotification(userId, notification);

    res.json(createSuccessResponse(notification, 'Test notification created'));
  })
);

export default router;
