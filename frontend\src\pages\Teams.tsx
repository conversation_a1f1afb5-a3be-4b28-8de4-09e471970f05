import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Grid,
  Card,
  CardContent,
  CardActions,
  Button,
  Fab,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Chip,
  Avatar,
  AvatarGroup,
  IconButton,
  Menu,
  MenuItem,
  CircularProgress,
  Alert,
} from '@mui/material';
import {
  Add as AddIcon,
  MoreVert as MoreVertIcon,
  People as PeopleIcon,
  Work as ProjectIcon,
  Settings as SettingsIcon,
} from '@mui/icons-material';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { teamService, Team, CreateTeamData } from '../services/teamService';
import { useAppSelector } from '../store';
import toast from 'react-hot-toast';

// Validation schema
const createTeamSchema = yup.object({
  name: yup
    .string()
    .required('Team name is required')
    .min(2, 'Team name must be at least 2 characters'),
  description: yup.string().max(500, 'Description must be less than 500 characters'),
});

const Teams: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAppSelector((state) => state.auth);

  const [teams, setTeams] = useState<Team[]>([]);
  const [loading, setLoading] = useState(true);
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [menuAnchor, setMenuAnchor] = useState<null | HTMLElement>(null);
  const [selectedTeam, setSelectedTeam] = useState<Team | null>(null);

  const {
    control,
    handleSubmit,
    reset,
    formState: { errors, isSubmitting },
  } = useForm<CreateTeamData>({
    resolver: yupResolver(createTeamSchema),
    defaultValues: {
      name: '',
      description: '',
    },
  });

  useEffect(() => {
    loadTeams();
  }, []);

  const loadTeams = async () => {
    try {
      setLoading(true);
      const response = await teamService.getTeams();
      setTeams(response.data || []);
    } catch (error) {
      toast.error('Failed to load teams');
      console.error('Error loading teams:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateTeam = async (data: CreateTeamData) => {
    try {
      const response = await teamService.createTeam(data);
      setTeams([...teams, response.data]);
      setCreateDialogOpen(false);
      reset();
      toast.success('Team created successfully!');
    } catch (error: any) {
      toast.error(error.response?.data?.error || 'Failed to create team');
    }
  };

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>, team: Team) => {
    setMenuAnchor(event.currentTarget);
    setSelectedTeam(team);
  };

  const handleMenuClose = () => {
    setMenuAnchor(null);
    setSelectedTeam(null);
  };

  const handleTeamClick = (teamId: string) => {
    navigate(`/teams/${teamId}`);
  };

  const handleDeleteTeam = async () => {
    if (!selectedTeam) return;

    try {
      await teamService.deleteTeam(selectedTeam.id);
      setTeams(teams.filter(t => t.id !== selectedTeam.id));
      toast.success('Team deleted successfully');
    } catch (error: any) {
      toast.error(error.response?.data?.error || 'Failed to delete team');
    } finally {
      handleMenuClose();
    }
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Box>
          <Typography variant="h4" component="h1" gutterBottom>
            Teams
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Manage your teams and collaborate effectively
          </Typography>
        </Box>
      </Box>

      {teams.length === 0 ? (
        <Alert severity="info" sx={{ mb: 3 }}>
          You're not a member of any teams yet. Create your first team to get started!
        </Alert>
      ) : null}

      <Grid container spacing={3}>
        {teams.map((team) => (
          <Grid item xs={12} sm={6} md={4} key={team.id}>
            <Card
              sx={{
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                cursor: 'pointer',
                '&:hover': {
                  boxShadow: (theme) => theme.shadows[8],
                },
              }}
              onClick={() => handleTeamClick(team.id)}
            >
              <CardContent sx={{ flexGrow: 1 }}>
                <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={2}>
                  <Typography variant="h6" component="h2" noWrap>
                    {team.name}
                  </Typography>
                  <IconButton
                    size="small"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleMenuOpen(e, team);
                    }}
                  >
                    <MoreVertIcon />
                  </IconButton>
                </Box>

                {team.description && (
                  <Typography
                    variant="body2"
                    color="text.secondary"
                    sx={{
                      mb: 2,
                      display: '-webkit-box',
                      WebkitLineClamp: 2,
                      WebkitBoxOrient: 'vertical',
                      overflow: 'hidden',
                    }}
                  >
                    {team.description}
                  </Typography>
                )}

                <Box display="flex" alignItems="center" gap={2} mb={2}>
                  <Chip
                    icon={<PeopleIcon />}
                    label={`${team.memberCount} members`}
                    size="small"
                    variant="outlined"
                  />
                  <Chip
                    icon={<ProjectIcon />}
                    label={`${team.projectCount} projects`}
                    size="small"
                    variant="outlined"
                  />
                </Box>

                {team.members && team.members.length > 0 && (
                  <Box>
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      Team Members
                    </Typography>
                    <AvatarGroup max={4} sx={{ justifyContent: 'flex-start' }}>
                      {team.members.map((member) => (
                        <Avatar
                          key={member.userId}
                          src={member.team.avatar}
                          alt={`${member.team.firstName} ${member.team.lastName}`}
                          sx={{ width: 32, height: 32 }}
                        >
                          {member.team.firstName[0]}{member.team.lastName[0]}
                        </Avatar>
                      ))}
                    </AvatarGroup>
                  </Box>
                )}
              </CardContent>

              <CardActions>
                <Button size="small" onClick={(e) => {
                  e.stopPropagation();
                  handleTeamClick(team.id);
                }}>
                  View Details
                </Button>
              </CardActions>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Floating Action Button */}
      <Fab
        color="primary"
        aria-label="create team"
        sx={{
          position: 'fixed',
          bottom: 16,
          right: 16,
        }}
        onClick={() => setCreateDialogOpen(true)}
      >
        <AddIcon />
      </Fab>

      {/* Create Team Dialog */}
      <Dialog
        open={createDialogOpen}
        onClose={() => setCreateDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <form onSubmit={handleSubmit(handleCreateTeam)}>
          <DialogTitle>Create New Team</DialogTitle>
          <DialogContent>
            <Controller
              name="name"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  autoFocus
                  margin="dense"
                  label="Team Name"
                  fullWidth
                  variant="outlined"
                  error={!!errors.name}
                  helperText={errors.name?.message}
                  sx={{ mb: 2 }}
                />
              )}
            />
            <Controller
              name="description"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  margin="dense"
                  label="Description (Optional)"
                  fullWidth
                  multiline
                  rows={3}
                  variant="outlined"
                  error={!!errors.description}
                  helperText={errors.description?.message}
                />
              )}
            />
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setCreateDialogOpen(false)}>Cancel</Button>
            <Button type="submit" variant="contained" disabled={isSubmitting}>
              {isSubmitting ? 'Creating...' : 'Create Team'}
            </Button>
          </DialogActions>
        </form>
      </Dialog>

      {/* Team Actions Menu */}
      <Menu
        anchorEl={menuAnchor}
        open={Boolean(menuAnchor)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={() => {
          if (selectedTeam) {
            navigate(`/teams/${selectedTeam.id}/settings`);
          }
          handleMenuClose();
        }}>
          <SettingsIcon sx={{ mr: 1 }} />
          Settings
        </MenuItem>
        <MenuItem onClick={handleDeleteTeam} sx={{ color: 'error.main' }}>
          Delete Team
        </MenuItem>
      </Menu>
    </Box>
  );
};

export default Teams;
