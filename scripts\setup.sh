#!/bin/bash

# CollabFlow Setup Script
# This script sets up the development environment for CollabFlow

set -e

echo "🚀 Setting up CollabFlow development environment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Node.js is installed
check_node() {
    if command -v node &> /dev/null; then
        NODE_VERSION=$(node --version)
        print_success "Node.js is installed: $NODE_VERSION"
        
        # Check if version is 18 or higher
        MAJOR_VERSION=$(echo $NODE_VERSION | cut -d'.' -f1 | sed 's/v//')
        if [ "$MAJOR_VERSION" -lt 18 ]; then
            print_warning "Node.js version 18 or higher is recommended. Current: $NODE_VERSION"
        fi
    else
        print_error "Node.js is not installed. Please install Node.js 18 or higher."
        exit 1
    fi
}

# Check if Docker is installed
check_docker() {
    if command -v docker &> /dev/null; then
        DOCKER_VERSION=$(docker --version)
        print_success "Docker is installed: $DOCKER_VERSION"
    else
        print_warning "Docker is not installed. Some features may not work."
    fi
}

# Check if PostgreSQL is installed
check_postgres() {
    if command -v psql &> /dev/null; then
        POSTGRES_VERSION=$(psql --version)
        print_success "PostgreSQL is installed: $POSTGRES_VERSION"
    else
        print_warning "PostgreSQL is not installed. You can use Docker instead."
    fi
}

# Check if Redis is installed
check_redis() {
    if command -v redis-server &> /dev/null; then
        REDIS_VERSION=$(redis-server --version)
        print_success "Redis is installed: $REDIS_VERSION"
    else
        print_warning "Redis is not installed. You can use Docker instead."
    fi
}

# Install dependencies
install_dependencies() {
    print_status "Installing dependencies..."
    
    # Install root dependencies
    if [ -f "package.json" ]; then
        print_status "Installing root dependencies..."
        npm install
    fi
    
    # Install backend dependencies
    if [ -d "backend" ]; then
        print_status "Installing backend dependencies..."
        cd backend
        npm install
        cd ..
    fi
    
    # Install frontend dependencies
    if [ -d "frontend" ]; then
        print_status "Installing frontend dependencies..."
        cd frontend
        npm install
        cd ..
    fi
    
    print_success "Dependencies installed successfully!"
}

# Setup environment files
setup_env_files() {
    print_status "Setting up environment files..."
    
    # Backend environment
    if [ ! -f "backend/.env" ] && [ -f "backend/.env.example" ]; then
        cp backend/.env.example backend/.env
        print_success "Created backend/.env from example"
    fi
    
    # Frontend environment
    if [ ! -f "frontend/.env" ] && [ -f "frontend/.env.example" ]; then
        cp frontend/.env.example frontend/.env
        print_success "Created frontend/.env from example"
    fi
    
    print_warning "Please update the .env files with your actual configuration values."
}

# Setup database
setup_database() {
    print_status "Setting up database..."
    
    if [ -d "backend" ]; then
        cd backend
        
        # Generate Prisma client
        if command -v npx &> /dev/null; then
            print_status "Generating Prisma client..."
            npx prisma generate
            
            # Run migrations (if database is available)
            print_status "Running database migrations..."
            if npx prisma migrate deploy 2>/dev/null; then
                print_success "Database migrations completed"
                
                # Seed database
                print_status "Seeding database..."
                if npx prisma db seed 2>/dev/null; then
                    print_success "Database seeded successfully"
                else
                    print_warning "Database seeding failed. You may need to set up the database first."
                fi
            else
                print_warning "Database migrations failed. Please ensure your database is running and configured correctly."
            fi
        fi
        
        cd ..
    fi
}

# Create necessary directories
create_directories() {
    print_status "Creating necessary directories..."
    
    # Backend directories
    mkdir -p backend/logs
    mkdir -p backend/uploads
    
    # Frontend directories
    mkdir -p frontend/build
    
    print_success "Directories created successfully!"
}

# Setup Git hooks (optional)
setup_git_hooks() {
    if [ -d ".git" ]; then
        print_status "Setting up Git hooks..."
        
        # Create pre-commit hook
        cat > .git/hooks/pre-commit << 'EOF'
#!/bin/bash
echo "Running pre-commit checks..."

# Run linting
npm run lint:backend
npm run lint:frontend

# Run type checking
cd backend && npm run type-check
cd ../frontend && npm run type-check

echo "Pre-commit checks passed!"
EOF
        
        chmod +x .git/hooks/pre-commit
        print_success "Git hooks set up successfully!"
    fi
}

# Main setup function
main() {
    echo "🎯 CollabFlow - AI-powered team coordination platform"
    echo "📋 Seamless team orchestration"
    echo ""
    
    # Check prerequisites
    print_status "Checking prerequisites..."
    check_node
    check_docker
    check_postgres
    check_redis
    
    echo ""
    
    # Setup steps
    install_dependencies
    setup_env_files
    create_directories
    setup_database
    setup_git_hooks
    
    echo ""
    print_success "🎉 CollabFlow setup completed successfully!"
    echo ""
    echo "📚 Next steps:"
    echo "1. Update your .env files with actual configuration values"
    echo "2. Start the development servers:"
    echo "   - Backend: cd backend && npm run dev"
    echo "   - Frontend: cd frontend && npm start"
    echo "   - Or use Docker: docker-compose up -d"
    echo ""
    echo "3. Access the application:"
    echo "   - Frontend: http://localhost:3000"
    echo "   - Backend API: http://localhost:3001/api"
    echo "   - API Health: http://localhost:3001/api/health"
    echo ""
    echo "4. Default admin credentials:"
    echo "   - Email: <EMAIL>"
    echo "   - Password: admin123"
    echo ""
    echo "📖 For more information, check the README.md file."
}

# Run main function
main "$@"
